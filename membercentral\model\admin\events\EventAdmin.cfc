<cfcomponent extends="model.admin.admin" output="no">
	<cfset defaultEvent = "controller">
	<cfset variables.enableEventGuestTracking = false>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// Load Objects for page -------------------------------------------------------------------- ::
			this.objAdminEvent = CreateObject("component","model.admin.events.event");
			this.objCategories = CreateObject('component', 'model.system.platform.category');
			
			//
			//  Overriding the siteResourceID for the current object breaks the platform model.
			//  Per AT:  we will want to refactor this at a later date.
			//  KLUDGE
			this.eventAdminSiteResourceID = this.siteResourceID;
			
			// ------------------------------------------------------------------------------------------ ::
			if( ListFindNoCase('editEvent,copyEvent,saveEventDetails,editRegistration,addReg,exportReg,exportTicket,exportRegTrans,exportOnlineMeetingLog,showSubmitEvaluationForm,saveEvaluationFormSubmission',arguments.event.getValue('mca_ta')) ){
				this.siteResourceID = this.objAdminEvent.getSiteResourceIDByEventID(siteID=arguments.event.getValue('mc_siteInfo.siteid'), eventID=arguments.event.getValue('eID'));
			}
			if( ListFindNoCase('updateRegistration,addRate,editRate,editTicket',arguments.event.getValue('mca_ta')) ){
				this.siteResourceID = this.objAdminEvent.getSiteResourceIDByRegistrationID(siteID=arguments.event.getValue('mc_siteInfo.siteid'), registrationID=arguments.event.getValue('registrationID'));
			}
			if( ListFindNoCase('listCalEvents,saveCalendar,saveChangeCalendar,changeCalendar,addEvent,insertEvent,addSyndication,insertSyndication',arguments.event.getValue('mca_ta')) ){
				this.siteResourceID = this.objAdminEvent.getSiteResourceIDByCalendarID(siteID=arguments.event.getValue('mc_siteInfo.siteid'), calendarID=arguments.event.getValue('cID'));
			}
			
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(
				siteResourceID=this.siteResourceID, 
				memberID=session.cfcuser.memberdata.memberID, 
				siteID=arguments.event.getValue('mc_siteInfo.siteid'));
				
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;

			local.controlPanelSettings = super.getInstanceSettings(this.appInstanceID);
			
			// Build Quick Links for SectionAdmin ------------------------------------------------------- ::
				this.link.listAssets = buildCurrentLink(arguments.event,"listAssets") & "&bc=" & arguments.event.getValue('bc',"c");
				this.link.listCalendars = buildCurrentLink(arguments.event,"listCalendars") & "&bc=" & arguments.event.getValue('bc',"c");
				this.link.listCalEvents = buildCurrentLink(arguments.event,"listCalEvents") & "&bc=" & arguments.event.getValue('bc',"c");
				this.link.listEvents = buildCurrentLink(arguments.event,"listEvents") & "&bc=" & arguments.event.getValue('bc',"e");
			// calendar code ------------------------------------------------------------------------------- ::
				this.link.saveCalendar = buildCurrentLink(arguments.event,"saveCalendar") & "&bc=" & arguments.event.getValue('bc',"c") & "&mode=stream";
				this.link.changeCalendar = buildCurrentLink(arguments.event,"changeCalendar") & "&bc=" & arguments.event.getValue('bc',"c") & '&mode=direct';
				this.link.saveChangeCalendar = buildCurrentLink(arguments.event,"saveChangeCalendar") & "&bc=" & arguments.event.getValue('bc',"c") & "&mode=stream";
			// event code ------------------------------------------------------------------------------- ::
				this.link.editEvent = buildCurrentLink(arguments.event,"editEvent") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.copyEvent = buildCurrentLink(arguments.event,"copyEvent") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.saveEventDetails = buildCurrentLink(arguments.event,"saveEventDetails") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.addEvent = buildCurrentLink(arguments.event,"addEvent") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.insertEvent = buildCurrentLink(arguments.event,"insertEvent") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.previewConfirmationEmail = buildCurrentLink(arguments.event,"previewConfirmationEmail") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=direct";
				this.link.editSubEvent = buildCurrentLink(arguments.event,"editSubEvent") & "&mode=direct";
				this.link.saveSubEventSettings = buildCurrentLink(arguments.event,"saveSubEventSettings") & "&mode=stream";
				this.link.linkEvent = buildCurrentLink(arguments.event,"linkEvent") & "&mode=direct";
				this.link.saveLinkEvent = buildCurrentLink(arguments.event,"saveLinkEvent") & "&mode=stream";
				this.link.processCopyEvent = buildCurrentLink(arguments.event,"processCopyEvent") & "&mode=direct";
			// syndications ----------------------------------------------------------------------------- ::
				this.link.addSyndication = buildCurrentLink(arguments.event,"addSyndication") & "&bc=" & arguments.event.getValue('bc',"c");
				this.link.insertSyndication = buildCurrentLink(arguments.event,"insertSyndication") & "&bc=" & arguments.event.getValue('bc',"c");
				this.link.deleteSyndication = buildCurrentLink(arguments.event,"deleteSyndication") & "&bc=" & arguments.event.getValue('bc',"c");
			// registration code ------------------------------------------------------------------------ ::
				this.link.insertRegistration = buildCurrentLink(arguments.event,"insertRegistration") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.saveEventRegDetails = buildCurrentLink(arguments.event,"saveEventRegDetails") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.deleteRegistration = buildCurrentLink(arguments.event,"deleteRegistration") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.saveAltURL = buildCurrentLink(arguments.event,"saveAltURL") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.emailRegistrations = buildCurrentLink(arguments.event,"emailRegistrations") & "&mode=direct";
				this.link.massEmailRegistrantMaterials = buildCurrentLink(arguments.event,"massEmailRegistrantMaterials") & "&mode=direct";
				this.link.saveEventRegRateAndAccDetails = buildCurrentLink(arguments.event,"saveEventRegRateAndAccDetails") & "&bc=" & arguments.event.getValue('bc',"e");
			// Registrant Search code ------------------------------------------------------------------- ::
				this.link.regSearch = buildCurrentLink(arguments.event,"regSearch");				
			// Registrants code ------------------------------------------------------------------------- ::
				this.link.addReg = buildCurrentLink(arguments.event,"addReg") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.printReg = buildCurrentLink(arguments.event,"printReg") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=direct";
				this.link.sendConfirmation = buildCurrentLink(arguments.event,"sendConfirmation") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=direct";
				this.link.sendRegistrantMaterials = buildCurrentLink(arguments.event,"sendRegistrantMaterials") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=direct";
				this.link.manageRegTickets = buildCurrentLink(arguments.event,"manageRegTickets") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=stream";
				this.link.manageRegTicketSelections = buildCurrentLink(arguments.event,"manageRegTicketSelections") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=stream";
				this.link.manageRegTicketFields = buildCurrentLink(arguments.event,"manageRegTicketFields") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=stream";
				this.link.loadRegConfirmation = buildCurrentLink(arguments.event,"loadRegConfirmation") & "&mode=stream";
				this.link.printBadgeRegistrant = buildCurrentLink(arguments.event,"printBadgeRegistrant") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=direct";
			// RSVP code -------------------------------------------------------------------------------- ::	
				this.link.addRSVP = buildCurrentLink(arguments.event,"addRSVP") & "&bc=" & arguments.event.getValue('bc',"e")& "&mode=direct";
				this.link.insertRSVP = buildCurrentLink(arguments.event,"insertRSVP") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.editRSVP = buildCurrentLink(arguments.event,"editRSVP") & "&bc=" & arguments.event.getValue('bc',"e") & "&mode=direct";
				this.link.updateRSVP = buildCurrentLink(arguments.event,"updateRSVP") & "&bc=" & arguments.event.getValue('bc',"e");
			// export links ----------------------------------------------------------------------------- ::
				this.link.exportRSVP = buildCurrentLink(arguments.event,"exportRSVP") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportRegPrompt = buildCurrentLink(arguments.event,"exportRegPrompt") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportReg = buildCurrentLink(arguments.event,"exportReg") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportRegTransPrompt = buildCurrentLink(arguments.event,"exportRegTransPrompt") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportRegTrans = buildCurrentLink(arguments.event,"exportRegTransactions") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportOnlineMeetingLogPrompt = buildCurrentLink(arguments.event,"exportOnlineMeetingLogPrompt") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportOnlineMeetingLog = buildCurrentLink(arguments.event,"exportOnlineMeetingLog") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportTicketPrompt = buildCurrentLink(arguments.event,"exportTicketPrompt") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportTicket = buildCurrentLink(arguments.event,"exportTicket") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.exportRegSearch = buildCurrentLink(arguments.event,"exportRegSearch") & "&bc=" & arguments.event.getValue('bc',"e");
			// import registrants links ----------------------------------------------------------------------------- ::
				this.link.importRegPrompt = buildCurrentLink(arguments.event,"importRegPrompt") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.importRegComplete = buildCurrentLink(arguments.event,"importRegComplete") & "&bc=" & arguments.event.getValue('bc',"e");	
				this.link.importReg = buildCurrentLink(arguments.event,"importReg") & "&bc=" & arguments.event.getValue('bc',"e");
			// category code ------------------------------------------------------------- ::
				this.link.addCategory = buildCurrentLink(arguments.event,"addCategory") & "&mode=direct";
				this.link.insertCategory = buildCurrentLink(arguments.event,"insertCategory") & "&mode=stream";
				this.link.editCategory = buildCurrentLink(arguments.event,"editCategory") & "&mode=direct";
				this.link.updateCategory = buildCurrentLink(arguments.event,"updateCategory") & "&mode=stream";
			// question code  ------------------------------------------------------------- ::
				this.link.addQuestion = buildCurrentLink(arguments.event,"addQuestion") & "&mode=direct";
				this.link.saveQuestion = buildCurrentLink(arguments.event,"saveQuestion") & "&mode=stream";
				this.link.editQuestion = buildCurrentLink(arguments.event,"editQuestion") & "&mode=direct";
			// answer code --------------------------------------------------------------- ::
				this.link.addAnswer = buildCurrentLink(arguments.event,"addAnswer") & "&mode=direct";
				this.link.saveAnswer = buildCurrentLink(arguments.event,"saveAnswer") & "&mode=stream";
				this.link.editAnswer = buildCurrentLink(arguments.event,"editAnswer") & "&mode=direct";
			// Rates code ---------------------------------------------------------------- ::
				this.link.addRate = buildCurrentLink(arguments.event,"addRate") & "&mode=direct";
				this.link.saveRate = buildCurrentLink(arguments.event,"saveRate") & "&mode=stream";
				this.link.editRate = buildCurrentLink(arguments.event,"editRate") & "&mode=direct";
				this.link.copyRate = buildCurrentLink(arguments.event,"copyRate") & "&mode=direct";
				this.link.saveCopyRate = buildCurrentLink(arguments.event,"saveCopyRate") & "&mode=stream";
				this.link.manageMassRates = buildCurrentLink(arguments.event,"manageMassRates") & "&mode=stream";
				this.link.permissionsGridShow = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
				this.link.permissionsGridAdd = CreateObject('component','model.admin.admin').buildLinkToTool(toolType='PermissionsAdmin',mca_ta='addPermission') & '&mode=direct';
			// transaction payments ------------------------------------------------------ ::
				this.link.addPayment = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=addPayment&mode=direct";
				this.link.allocatePayment = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin') & "&mca_ta=allocatePayment&mode=direct";
			// remove registrant ------------------------------------------------------ ::
				this.link.removeRegistrant = buildCurrentLink(arguments.event,"removeReg");
				this.link.removeFilteredRegistrant = buildCurrentLink(arguments.event,"removeFilteredRegistrant");
			// Manage credits and attendance 
				this.link.saveEventRegCreditDetails	= buildCurrentLink(arguments.event,"saveEventRegCreditDetails") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.manageAttendanceCredit = buildCurrentLink(arguments.event,"manageAttendanceCredit") & "&mode=direct";
				this.link.manageSubEventAttendanceCredit = buildCurrentLink(arguments.event,"manageSubEventAttendanceCredit") & "&mode=direct";
				this.link.saveAttendanceCredit = buildCurrentLink(arguments.event,"saveAttendanceCredit") & "&mode=stream";
				this.link.saveSubEventAttendanceCredit = buildCurrentLink(arguments.event,"saveSubEventAttendanceCredit") & "&mode=stream";
				this.link.importCreditsPrompt = buildCurrentLink(arguments.event,"importCreditsPrompt") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.sampleCreditsImportTemplate = buildCurrentLink(arguments.event,"sampleCreditsImportTemplate") & "&bc=" & arguments.event.getValue('bc',"e");
				this.link.importCreditsComplete = buildCurrentLink(arguments.event,"importCreditsComplete") & "&bc=" & arguments.event.getValue('bc',"e");
			// View certificate 
				this.link.emailCertificates = buildCurrentLink(arguments.event,"emailCertificates") & "&mode=direct";
				this.link.viewCertificate = buildCurrentLink(arguments.event,"viewCertificate") & "&mode=direct";
				this.link.viewCertificatePDF = buildCurrentLink(arguments.event,"viewCertificate") & "&mode=stream";
				this.link.viewSigninRSVPsheet = buildCurrentLink(arguments.event,"viewSigninRSVPsheet") & "&mode=stream";
			// Message Link ----------------------------------------------------------------------------- ::
				this.link.message = buildCurrentLink(arguments.event,"message");
			// ------------------------------------------------------------------------------------------ ::
			
			this.link.listAssetCategories = buildCurrentLink(arguments.event,"listAssetCategories");
			this.link.editAssetCategory = buildCurrentLink(arguments.event,"editAssetCategory");

			this.link.listEventRoles = buildCurrentLink(arguments.event,"listEventRoles");
			this.link.editEventRole = buildCurrentLink(arguments.event,"editEventRole") & "&mode=direct";
			
			this.link.editMember = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			this.link.editDocument = buildCurrentLink(arguments.event,"editDocument") & "&mode=direct";
			this.link.viewDocument = buildCurrentLink(arguments.event,"viewDocument") & "&mode=direct";
			this.link.saveDocument = buildCurrentLink(arguments.event,"saveDocument") & "&mode=stream";
			this.link.replaceEventFile = buildCurrentLink(arguments.event,"replaceEventFile") & "&mode=direct";

			// event registration schedule
			this.link.saveRegistrationSchedule = buildCurrentLink(arguments.event,"saveRegistrationSchedule") & "&mode=stream";
			this.link.removeRegistrationSchedule = buildCurrentLink(arguments.event,"saveRegistrationSchedule") & "&mode=stream";

			// tickets and ticket packages
			this.link.editTicket = buildCurrentLink(arguments.event,"editTicket") & "&mode=direct";
			this.link.saveTicket = buildCurrentLink(arguments.event,"saveTicket") & "&mode=stream";
			this.link.editTicketPackage = buildCurrentLink(arguments.event,"editTicketPackage") & "&mode=direct";
			this.link.saveTicketPackage = buildCurrentLink(arguments.event,"saveTicketPackage") & "&mode=stream";
			this.link.loadResourceGrid = buildCurrentLink(arguments.event,"loadResourceGrid") & "&mode=stream";
			
			this.languageID	= arguments.event.getValue('mc_siteInfo.defaultLanguageID');

			// Run Assigned Method
			local.methodToRun = this[arguments.event.getValue('mca_ta')];

			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<!--- EVENT LIST --->
	<cffunction name="selectEventForAddRegistrant" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			arguments.event.setValue('mid',int(val(arguments.event.getValue('mid',0))));
			local.currentEventsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getCurrentEventsOnSite&mid=#arguments.event.getValue('mid')#&mode=stream";
		</cfscript>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_selectEvent.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listEvents" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.qryCalendars = this.objAdminEvent.getCalendarsForFilters(arguments.event.getValue('mc_siteInfo.siteID'));
			local.fromDt = dateFormat(now(), "m/d/yyyy");
			local.toDt = dateFormat(dateAdd("d",365,now()), "m/d/yyyy");
			local.evAddLink = buildCurrentLink(arguments.event,"addEvent") & "&bc=c";
			local.evExportLink = buildCurrentLink(arguments.event,"exportEvents") & "&mode=stream";
			local.deleteEventPromptLink = buildCurrentLink(arguments.event,"deleteEventPrompt") & "&mode=direct";
			local.eventsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getEventsOnSite&mode=stream";
			local.sampleEventImportTemplate = buildCurrentLink(arguments.event,"sampleEventImportTemplate") & "&mode=stream";
			local.sampleRegistrantsImportTemplate = buildCurrentLink(arguments.event,"sampleRegistrantsImportTemplate") & "&mode=stream";
			local.sampleRolesImportTemplate = buildCurrentLink(arguments.event,"sampleRolesImportTemplate") & "&mode=stream";
			local.objFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector");
			local.objMemberFieldsets = createObject("component","model.admin.memberFieldSets.memberFieldSets");
			
			local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=local.siteID);

			// Reg FieldSets
			local.qryResultsFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=local.eventAdminSiteResourceID, area='regIdResults', module="Events");
			local.resultsFieldsetID = val(local.qryResultsFieldSet.fieldsetID);
			local.resultsUseID = val(local.qryResultsFieldSet.useID);

			local.qryNewAcctFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=local.eventAdminSiteResourceID, area='regIdNewacct', module="Events");
			local.newacctFieldsetID = val(local.qryNewAcctFieldSet.fieldsetID);
			local.newacctUseID = val(local.qryNewAcctFieldSet.useID);

			local.strResultsFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="ResultsFieldSet", selectedValue=local.resultsFieldsetID, fieldLabel='Member Field Set for Registrant Identifier Results (First Name, Last Name, Company always appear)');
			local.strNewAcctFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="NewAccFieldSet", selectedValue=local.newacctFieldsetID, fieldLabel='Member Field Set for Registrant Identifier New Account Form');

			// Admin Reg FieldSets
			local.qryAdminRegSearchFormFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=local.eventAdminSiteResourceID, area='adminEvRegSearchForm', module="Events");
			local.adminEvRegSearchFormFieldsetID = val(local.qryAdminRegSearchFormFieldSet.fieldsetID);
			local.adminEvRegSearchFormFSUseID = val(local.qryAdminRegSearchFormFieldSet.useID);

			local.qryAdminRegResultsFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=local.eventAdminSiteResourceID, area='adminEvRegResults', module="Events");
			local.adminEvRegResultsFieldsetID = val(local.qryAdminRegResultsFieldSet.fieldsetID);
			local.adminEvRegSResultsFSUseID = val(local.qryAdminRegResultsFieldSet.useID);

			local.qryAdminEvRegNewAcctFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=local.eventAdminSiteResourceID, area='adminEvRegNewAcct', module="Events");
			local.adminEvRegNewAcctFieldsetID = val(local.qryAdminEvRegNewAcctFieldSet.fieldsetID);
			local.adminEvRegNewAcctFSUseID = val(local.qryAdminEvRegNewAcctFieldSet.useID);

			local.strAdminEvRegSearchFormFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="adminEvRegSearchFormFieldSet", selectedValue=local.adminEvRegSearchFormFieldsetID, allowBlankOption=false, fieldLabel='Member Field Set for Add Registrant Search Form');
			local.strAdminEvRegResultsFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="adminEvRegResultsFieldSet", selectedValue=local.adminEvRegResultsFieldsetID, allowBlankOption=false, fieldLabel='Member Field Set for Add Registrant Search Results');
			local.strAdminEvRegNewAcctFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="adminEvRegNewAcctFieldSet", selectedValue=local.adminEvRegNewAcctFieldsetID, allowBlankOption=false, fieldLabel='Member Field Set for Add Registrant Create Account');

			local.strETData = {
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				treeCode='ETEVENTS',
				title="Event Email Templates ", 
				intro="Here you manage email templates used by events.",
				gridext="#this.siteResourceID#_1",
				gridwidth=690,
				initGridOnLoad=false
			};
			local.strEmailTemplatesGrid = createObject("component","model.admin.emailTemplates.emailTemplateAdmin").manageEmailTemplates(strETData=local.strETData);

			local.strBTData = {
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				treeCode='BTEVENTS',
				title="Badge Event Templates ",
				intro="Here you manage badge templates used by events.",
				gridext="#this.siteResourceID#_1",
				gridwidth=690,
				initGridOnLoad=false
			};
			local.strBadgeTemplatesGrid = createObject("component","model.admin.badges.deviceAdmin").manageBadgeTemplates(strBTData=local.strBTData);

			local.eventAppSettingsXML = this.objAdminEvent.getEventAppSettingsXML(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			local.evListdefaultAction = xmlSearch(local.eventAppSettingsXML,'string(/settings/setting[@name="defaultAction"]/@value)');
			local.evshowPhotosRegStep1 = xmlSearch(local.eventAppSettingsXML,'string(/settings/setting[@name="showPhotosRegStep1"]/@value)');
			local.evForceLoginForReg = xmlSearch(local.eventAppSettingsXML,'string(/settings/setting[@name="forceLoginForReg"]/@value)');
			local.sidebarPosition = xmlSearch(local.eventAppSettingsXML,'string(/settings/setting[@name="sidebarPosition"]/@value)');

			local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages");
			local.qrySiteFeaturedImageConfigs = local.objFeaturedImages.getFeaturedImageConfigsForSite(siteID=arguments.event.getValue('mc_siteInfo.siteID'), excludeEmpty=1);
			local.arrFeaturedImgSizeConfigs = [ { "sizeReferenceType":"viewEventDetails", "sizeColumnName":"evSponsorFeatureImageSizeID" },
												{ "sizeReferenceType":"viewSemwebDetails", "sizeColumnName":"swSponsorFeatureImageSizeID" } ];
			local.qryEvFeaturedImgSetttings = local.objFeaturedImages.getFeaturedmageConfigSettings(configReferenceID=arguments.event.getValue('mc_siteInfo.siteID'), 
												configReferenceType="evSiteSponsor", configParentTable="dbo.sites", configParentTableReferenceCol="siteID",
												configColumnName="sponsorFeatureImageConfigID", arrFeaturedImgSizeConfigs=local.arrFeaturedImgSizeConfigs);
		</cfscript>
		
		<cfset local.showImpTemplate = true>
		<cfif arguments.event.getValue('tab','') eq 'import' and len(arguments.event.getValue('importFileName1',''))>
			<cfset local.showImpTemplate = false>
			<cfset local.impData = processEventImport(event=arguments.event)>
		<cfelseif arguments.event.getValue('tab','') eq 'import' and len(arguments.event.getValue('importFileName2',''))>
			<cfset local.showImpTemplate = false>
			<cfset local.impData = processRegistrantsImport(event=arguments.event)>
		<cfelseif arguments.event.getValue('tab','') eq 'import' and len(arguments.event.getValue('importFileName3',''))>
			<cfset local.showImpTemplate = false>
			<cfset local.impData = processRolesImport(event=arguments.event)>
		</cfif>
		<cfset local.eventFilter = CreateObject("component","model.admin.events.event").getEventsFilter()>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_events.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportEvents" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.data = "The export file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.evCalendarID = arguments.event.getValue('fCalendar',0)>
		<cfset local.evCategoryID = arguments.event.getValue('fCategory',0)>
		<cfset local.evKeyword = arguments.event.getValue('fKeyword','')>
		<cfset local.evRegTypeID = arguments.event.getValue('fRegType',0)>
		<cfset local.evHideDeleted = arguments.event.getValue('fHideDeleted',0)>
		<cfset local.evDateFrom = arguments.event.getValue('fDateFrom','')>
		<cfset local.evDateTo = arguments.event.getValue('fDateTo','')>
		<cfset local.evReportCode = arguments.event.getValue('fReportCode','')>

		<cfif len(local.evDateFrom) gt 0>
			<cfset local.evDateFrom = DateFormat(local.evDateFrom, "mm/dd/yyyy")>
		<cfelse>
			<cfset local.evDateFrom = DateFormat(now(), "mm/dd/yyyy")>
		</cfif>
		<cfif len(local.evDateTo) gt 0>
			<cfset local.evDateTo = DateFormat(local.evDateTo, "mm/dd/yyyy") & " 23:59:59.997">
		<cfelse>
			<cfset local.evDateTo = dateFormat(dateAdd("d",365,now()), "m/d/yyyy") & " 23:59:59.997">
		</cfif>
		
		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "Events#DateFormat(local.evDateFrom,'yyyymmdd')#-#DateFormat(local.evDateTo,'yyyymmdd')#.csv">
		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEvents" result="local.qryEventsResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				declare @siteID int, @orgID int, @RTID int, @swcfList varchar(max), @sql varchar(max);
				set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
				set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
				set @RTID = dbo.fn_getResourceTypeID('Community');

				IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL
					DROP TABLE ##tmpEvents;
				IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
					DROP TABLE ##tmp_CF_ItemIDs;
				IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
					DROP TABLE ##tmp_CF_FieldData;
				IF OBJECT_ID('tempdb..##tmpSWCF') IS NOT NULL
					DROP TABLE ##tmpSWCF;
				IF OBJECT_ID('tempdb..####tmpSWCF#local.tmpSuffix#') IS NOT NULL 
					DROP TABLE ####tmpSWCF#local.tmpSuffix#;

				CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
				CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);
				CREATE TABLE ##tmpSWCF (eventID int, titleOnInvoice varchar(128), answer varchar(max), INDEX IX_tbltmpSWCF_eventID (eventID));

				-- get events on site
				IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
					DROP TABLE ##tmpEventsOnSite;
				CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
					startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
					displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
					displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, altRegistrationURL varchar(300),
					eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200),
					categoryIDList varchar(max));
				EXEC dbo.ev_getEventsOnSite @siteID=@siteID, @startDate='#Dateformat(arguments.event.getValue('fDateFrom'),"m/d/yyyy")#',
					@endDate='#Dateformat(arguments.event.getValue('fDateTo'),"m/d/yyyy")# 23:59:59.997',
					@categoryIDList='<cfif arguments.event.getValue('fCategory',0) gt 0>#arguments.event.getValue('fCategory')#</cfif>';

				SELECT e.eventID, e.siteResourceID, tmp.startTime as [Event Date],
					CASE 
					WHEN e.altRegistrationURL IS NULL AND ert.registrationType  = '' THEN ''
					WHEN e.altRegistrationURL IS NOT NULL THEN 'Alt'
					ELSE ert.registrationType
					END as [Registration Type],
					er.registrationID, 
					tmp.eventTitle as [Event], tmp.eventSubTitle as [Event Sub Title], e.reportCode as [Report Code], 
					ai.applicationInstanceName + case WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')' ELSE '' END as [Calendar], 
					STRING_AGG(evCat.category,'|') as [Category],
					case when e.status = 'A' then 'Active' when e.status = 'D' then 'Deleted' when e.status = 'I' then 'Inactive' end as [Event Status], 
					cast(null as int) as [Registrant Count],
					cast(null as decimal(18,2)) as [Registrant Amount]
				into ##tmpEvents
				FROM dbo.ev_events as e
				INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
				CROSS APPLY dbo.fn_IntListToTable(tmp.categoryIDList,',') as tmpCat
				INNER JOIN dbo.ev_categories as evCat on evCat.categoryID = tmpCat.listItem
				LEFT OUTER JOIN dbo.ev_registration as er 
					INNER JOIN dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID
						<cfif arguments.event.getValue('fRegType',0) gt 0>
							and ert.registrationTypeID = <cfqueryparam value="#arguments.event.getValue('fRegType')#" cfsqltype="CF_SQL_INTEGER">
						</cfif>
					on er.eventID = e.eventID and er.siteID = @siteID and er.status = 'A'
				INNER JOIN dbo.ev_calendars as c on c.siteID = @siteID and c.calendarid = tmp.calendarID
				inner join dbo.cms_applicationInstances as ai on c.applicationInstanceID = ai.applicationInstanceID 
					and ai.siteID = @siteID
				inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and ai.siteResourceID = sr.siteResourceID
				inner join dbo.cms_siteResources as parentResource on parentResource.siteID = @siteID 
					and parentResource.siteResourceID = sr.parentSiteResourceID
				inner join dbo.cms_contentLanguages as eventContent on eventContent.contentID = e.eventContentID
					and eventContent.languageID = 1
				left outer join dbo.cms_siteResources as grandparentResource
					inner join dbo.cms_applicationInstances as CommunityInstances on CommunityInstances.siteID = @siteID
						and communityInstances.siteResourceID = grandParentResource.siteResourceID
					on grandparentResource.siteID = @siteID
					and grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					and grandparentResource.resourceTypeID = @RTID
				WHERE e.siteID = @siteID
				<cfif arguments.event.getValue('fCalendar',0) gt 0>
					and tmp.calendarID = <cfqueryparam value="#arguments.event.getValue('fCalendar')#" cfsqltype="CF_SQL_INTEGER">
				</cfif>	
				<cfif len(arguments.event.getTrimValue('fKeyword',''))>
					and tmp.eventTitle like <cfqueryparam value="%#replace(arguments.event.getValue('fKeyword'),'_','\_','ALL')#%" cfsqltype="cf_sql_varchar"> ESCAPE('\')
				</cfif>
				<cfif arguments.event.getValue('fHideDeleted',0) is 1>
					and e.status <> 'D'
				</cfif>
				<cfif len(arguments.event.getTrimValue('fReportCode',''))>
					and e.ReportCode = <cfqueryparam value="#arguments.event.getTrimValue('fReportCode')#" cfsqltype="cf_sql_varchar">
				</cfif>
				<cfif arguments.event.getValue('fEventType','') eq 'main' and not val(arguments.event.getValue('peID',0))>
					and NOT EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID)
				</cfif>
				<cfif arguments.event.getValue('fEventType','') eq 'sub' and not val(arguments.event.getValue('peID',0))>
					and EXISTS (select se.eventID from dbo.ev_subevents se where se.eventID = e.eventID)
				</cfif>
				<cfif arguments.event.getValue('fEventType','') eq 'rev'>
					and e.recurringSeriesID is not null
				</cfif>
				GROUP BY tmp.calendarID, e.eventid, e.status, tmp.eventTitle, tmp.eventSubTitle, e.altRegistrationURL, ert.registrationType,
					ai.applicationInstanceName, communityInstances.applicationInstanceName, tmp.startTime, tmp.endTime, e.siteResourceID,
					er.registrationID, e.reportCode;

				-- reg counts and fees
				UPDATE tmp
				set [Registrant Count] =
					case 
					WHEN [Registration Type] = 'Reg' then (select count(*) from dbo.ev_registrants where registrationid = tmp.registrationID and status = 'A')
					ELSE (select count(*) from dbo.ev_rsvp where registrationid = tmp.registrationID) 
					end 
				from ##tmpEvents as tmp
				where tmp.[Event Status] <> 'Deleted'
				and tmp.registrationID is not null;

				IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL 
					DROP TABLE ##tmpEventsForFee; 
				IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL 
					DROP TABLE ##tmpEventsForFeeResult; 
				IF OBJECT_ID('tempdb..##tmpEventsFee') IS NOT NULL 
					DROP TABLE ##tmpEventsFee;
				CREATE TABLE ##tmpEventsForFee (eventID int PRIMARY KEY);
				CREATE TABLE ##tmpEventsForFeeResult (eventID int, registrantID int, transactionID int, TransactionIDForRateAdjustment int);
				CREATE TABLE ##tmpEventsFee (eventID int PRIMARY KEY, regFee decimal(18,2));

				insert into ##tmpEventsForFee (eventID)
				select eventID from ##tmpEvents;

				EXEC dbo.ev_registrantTransactionsByEventBulk;

				INSERT INTO ##tmpEventsFee (eventID, regFee)
				select fees.eventID, sum(ts.cache_amountAfterAdjustment) as regFee
				from ##tmpEventsForFeeResult as fees
				inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
				group by fees.eventID;

				UPDATE tmp
				set [Registrant Amount] = (select regFee from ##tmpEventsFee where eventID = tmp.eventID)
				from ##tmpEvents as tmp
				where tmp.[Event Status] <> 'Deleted'
				and tmp.registrationID is not null
				and tmp.[Registration Type] = 'Reg';

				-- system wide event custom fields
				INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
				SELECT DISTINCT siteResourceID, 'CrossEvent'
				FROM ##tmpEvents;

				EXEC dbo.cf_getFieldData;
				
				INSERT INTO ##tmpSWCF (eventID, titleOnInvoice, answer)
				SELECT tmp.eventID, replace(f.fieldReference,',','') as titleOnInvoice, fd.fieldValue AS answer
				FROM ##tmp_CF_FieldData AS fd
				INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
				INNER JOIN ##tmpEvents AS tmp ON tmp.siteResourceID = fd.itemID;

				-- system wide event custom fields pivoted
				set @swcfList = '';
				select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(titleonInvoice) from ##tmpSWCF group by titleOnInvoice;
				IF left(@swcfList,1) = ','
					set @swcfList = right(@swcfList,len(@swcfList)-1);
				IF len(@swcfList) > 0 BEGIN
					set @sql = '
						select * 
						into ####tmpSWCF#local.tmpSuffix#
						from (
							select eventID, titleOnInvoice, answer
							from ##tmpSWCF
						) as cf
						PIVOT (min(answer) for titleonInvoice in (' + @swcfList + ')) as p ';
					EXEC(@sql);
				END
				ELSE
					SELECT eventID 
					INTO ####tmpSWCF#local.tmpSuffix# 
					FROM ##tmpEvents 
					WHERE 0=1;
				
				DECLARE @selectsql varchar(max) = '
					SELECT e.[Event Date], e.[Registration Type], e.[Event], e.[Event Sub Title], e.[Report Code], e.[Calendar], e.[Category], e.[Event Status], e.[Registrant Count], e.[Registrant Amount] ' + 
						case when len(@swcfList) > 0 then ', swcf.' + replace(@swcfList,',',',swcf.') else '' end + ', ROW_NUMBER() OVER(order by e.[Event Date], e.[Event]) as mcCSVorder 
					*FROM* ##tmpEvents as e 
					LEFT OUTER JOIN ####tmpSWCF#local.tmpSuffix# as swcf on swcf.eventID = e.eventID';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

				IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
					DROP TABLE ##tmp_CF_ItemIDs;
				IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
					DROP TABLE ##tmp_CF_FieldData;
				IF OBJECT_ID('tempdb..##tmpSWCF') IS NOT NULL
		      		DROP TABLE ##tmpSWCF;
				IF OBJECT_ID('tempdb..####tmpSWCF#local.tmpSuffix#') IS NOT NULL
					DROP TABLE ####tmpSWCF#local.tmpSuffix#;
				IF OBJECT_ID('tempdb..##tmpEvents') IS NOT NULL
		      		DROP TABLE ##tmpEvents;
				IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL
					DROP TABLE ##tmpEventsForFee;
				IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL
					DROP TABLE ##tmpEventsForFeeResult;
				IF OBJECT_ID('tempdb..##tmpEventsFee') IS NOT NULL
					DROP TABLE ##tmpEventsFee;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportEV('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<!--- assets functions --->
	<cffunction name="listAssets" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			local.categoryTreeName = 'Event Asset Types';
			local.assetCategoryTreeID = this.objCategories.getCategoryTreeID(this.eventAdminSiteResourceID, local.categoryTreeName);
			local.qryAssetCategories = this.objCategories.getIndentedCategoriesForTree(val(local.assetCategoryTreeID)).qryCategories;
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_assetCalendar.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- CALENDAR LIST FUNCTIONS ---> 
	<cffunction name="listCalendars" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.calendarsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getCalendars&mode=stream";
			local.appTypeID = application.objApplications.getApplicationTypeIDFromName('Events');
			local.addpageLink = CreateObject("component","model.admin.admin").buildLinkToTool(toolType='PageAdmin',mca_ta='addPage');
			local.canAddInstance = CreateObject("component","model.admin.pages.appCreationProcess").canAddAppInstance(siteID=arguments.event.getValue('mc_siteInfo.siteID'), applicationTypeID=local.appTypeID);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_listCalendars.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="listCalEvents" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.calendarID = arguments.event.getValue('cID');
			local.categoryCount = this.objAdminEvent.getCategoryCount(local.calendarID);
			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteInfo.siteID'), calendarID=local.calendarID);
			local.qryCategories = this.objAdminEvent.getCalendarCategories(event=arguments.event);
			local.fromDt = dateFormat(now(), "m/d/yyyy");
			local.toDt = dateFormat(dateAdd("d",365,now()), "m/d/yyyy");

			// get calendar details
			if (arguments.event.getValue('mc_adminToolInfo.myRights.addEvent')) {
				local.SWLbranding = arguments.event.getValue('mc_siteinfo.swlBrand');
				local.qryCalendarCategories = this.objAdminEvent.getCalendarCategories(event=arguments.event);
				local.objFeaturedImages = createObject("component","model.admin.common.modules.featuredImages.featuredImages");
				local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';

				local.qrySiteFeaturedImageConfigs = local.objFeaturedImages.getFeaturedImageConfigsForSite(siteID=arguments.event.getValue('mc_siteInfo.siteID'), excludeEmpty=1);
				local.arrConfigs = [
						{ "ftdExt":"#this.siteResourceID#_1", "controllingReferenceID":local.calendarID, "controllingReferenceType":"calendarEvent",
							"referenceID":local.calendarID, "referenceType":"defaultCalendarEvent", "resourceType":"EventAdmin", 
							"resourceTypeTitle":local.qryCalendar.calendarName, "onDeleteImageHandler":"", "onSaveImageHandler":"saveCalendarDefaultFtdImgResult", 
							"preEditHandler":"editCalendarDefaultFeaturedImage", "header":'<div class="font-weight-bold mt-2">Default Featured Image</div>', 
							"ftdImgClassList":"pt-md-3" }
					];
				local.strFeaturedImages = local.objFeaturedImages.manageFeaturedImages(orgCode=arguments.event.getValue('mc_siteInfo.orgCode'), siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), arrConfigs=local.arrConfigs);
				local.strMemberFSSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="memFieldSet", selectedValue=local.qryCalendar.fieldSetID, fieldLabel='Member Field Set when showing Registrant Roster');
			}

			// Get listing Paths ---------------------------------------------------------------------------- ::
			local.eventsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getEventsOnCalendar&cID=#local.calendarID#&mode=stream";

			local.categoriesListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getCategories&cID=#local.calendarID#&mode=stream";
			// Get Link Paths ---------------------------------------------------------------------------- ::
			local.evAddLink = buildCurrentLink(arguments.event,"addEvent") & "&bc=c";
			local.evExportLink = buildCurrentLink(arguments.event,"exportEvents") & "&mode=stream";
			local.deleteEventPromptLink = buildCurrentLink(arguments.event,"deleteEventPrompt") & "&mode=direct";

			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML(local.qryCalendar.calendarName) });
		</cfscript>

		<cfstoredproc procedure="ev_getSyndicationRules" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#local.qryCalendar.calendarID#">
			<cfprocresult name="local.qrySyndRules">
		</cfstoredproc>

		<cfset local.calendarEventListFilter = CreateObject("component","event").getCalendarEventsFilter(local.calendarID)>
		<cfset local.categoriesJSON = CreateObject("component","event").getCategoriesForCalendar(calID=local.calendarID, includeAll=false, returnJSON=true)>
		<!--- Setup GL Account Widget for Revenue GL --->
		<cfset local.strRevenueGLAcctWidgetData = {
			"label": "Revenue Account for New Events",
			"btnTxt": "Choose GL Account",
			"glatid": 3,
			"widgetMode": "GLSelector",
			"idFldName": "defaultGLAccountID",
			"idFldValue": val(local.qryCalendar.defaultGLAccountID),
			"pathFldValue": local.qryCalendar.defaultGLAccountPath,
			"pathNoneTxt": "(no account selected)"
		}>
		<cfset local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="dsp_calendar.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- CALENDAR FUNCTIONS ---> 
	<cffunction name="saveCalendar" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			// security
			if (arguments.event.getValue('cID') gt 0 and NOT arguments.event.getValue('mc_adminToolInfo.myRights.edit'))
				application.objCommon.redirect('#this.link.message#&message=1');
				
			this.objAdminEvent.updateCalendar(arguments.event);

			this.objAdminEvent.updateContent(arguments.event.getValue('regEditRefundContentID',0),this.languageID,1,'Editing Refund Policy','',arguments.event.getValue('regEditRefundContent',''));

			return returnAppStruct('Details Saved.','echo');
		</cfscript>
	</cffunction>

	<cffunction name="changeCalendar" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// load objects -----------------------------------------------------------------------------
			local.objEvent = CreateObject("component","model.events.events");
			
			// security
			// --- might need to send to message page that says they dont have right to view this page
			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.addEvent'))
				application.objCommon.redirect("#this.link.message#&message=1");

			// event details
			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
	
			// get calendar details
			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteid'), calendarID=arguments.event.getValue('cID',0));
			local.qryCalendars = CreateObject("component","model.events.calendar").getCalendars(arguments.event.getValue('mc_siteInfo.siteID'), arguments.event.getValue('cID',0));

			// recurring series event
			local.isRecurringEvent = val(local.strEvent.qryEventMeta.recurringSeriesID) GT 0;
			if (local.isRecurringEvent) {
				local.qryRecurringEvents = this.objAdminEvent.getRecurringEvents(siteID=arguments.event.getValue('mc_siteinfo.siteID'), seriesID=local.strEvent.qryEventMeta.recurringSeriesID,
					startEventID=local.strEvent.qryEventMeta.eventID, count=1);
			}
		</cfscript>	
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_changeCalendar.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveChangeCalendar" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone");
			// security 
			if (arguments.event.getValue('cID') gt 0 and NOT arguments.event.getValue('mc_adminToolInfo.myRights.edit'))
				application.objCommon.redirect('#this.link.message#&message=1');
			
			this.objAdminEvent.moveEventToCalendar(arguments.event);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.location.href = '#this.link.editEvent#&eID=#arguments.event.getValue('eID')#';
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- EVENT FUNCTIONS --->
	<cffunction name="addEvent" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// load objects
			local.objEvent = CreateObject("component","model.events.events");

			// security 
			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.addEvent'))
				application.objCommon.redirect("#this.link.message#&message=1");
	
			// get information
			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
			// need to look at how we are getting the calendar ID and might
			// add select box to pick the calendar to load to that is connected to the categories
			
			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteid'), calendarID=arguments.event.getValue('cID',0));
			local.qryCategories = CreateObject("component","model.events.calendar").getCategories(arguments.event.getValue('cID',0),this.languageID,arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryTimeZones = this.objAdminEvent.getTimeZones();

			local.categoryTreeName = 'Event Asset Types';
			local.assetCategoryTreeID = this.objCategories.getCategoryTreeID(this.eventAdminSiteResourceID, local.categoryTreeName);
			local.qryAssetCategories = this.objCategories.getIndentedCategoriesForTree(val(local.assetCategoryTreeID)).qryCategories;
			
			local.firstOfThisMonth = createDateTime(year(now()), month(now()), 1, 9, 0, 0);

			// param form values
			arguments.event.setValue('eID',0);
			arguments.event.setValue('status','A');
			arguments.event.setValue('eventTypeID',1);
			arguments.event.setValue('eventContentID',0);
			arguments.event.setValue('eventContentTitle','');
			arguments.event.setValue('eventSubTitle','');
			arguments.event.setValue('reportCode','');
			arguments.event.setValue('categoryID','');
			arguments.event.setValue('eventContent','');
			// set start to first of next month, end to 7 hours after that (9:00AM to 4:00 PM)
			arguments.event.setValue('eventStartTime',DateAdd('m',1,local.firstOfThisMonth));
			arguments.event.setValue('eventEndTime',DateAdd('h',7,arguments.event.getValue('eventStartTime')));
			arguments.event.setValue('isAllDayEvent',0);
			arguments.event.setValue('lockTimeZone',0);
			arguments.event.setValue('locationContentID',0);
			arguments.event.setValue('locationContentTitle','');
			arguments.event.setValue('locationContent','');
			arguments.event.setValue('travelContentID',0);
			arguments.event.setValue('travelContentTitle','');
			arguments.event.setValue('travelContent','');
			arguments.event.setValue('contactContentID',0);
			arguments.event.setValue('contactContentTitle','');
			arguments.event.setValue('contactContent','');
			arguments.event.setValue('cancelContentID',0);
			arguments.event.setValue('cancelContentTitle','');
			arguments.event.setValue('cancelContent','');
			arguments.event.setValue('internalNotes','');
			arguments.event.setValue('informationContentID',0);
			arguments.event.setValue('informationContent','');
			arguments.event.setValue('emailContactContent',0);
			arguments.event.setValue('emailLocationContent',0);
			arguments.event.setValue('emailCancelContent',0);
			arguments.event.setValue('emailTravelContent',0);
			arguments.event.setValue('selectedAssetCategoryID','');
			arguments.event.setValue('onlineEmbedCode','');
			arguments.event.setValue('showCredit',0);
			arguments.event.setValue('allowSubEvents',0);
			
			// get parent information --------------------------------------------------------------------------
			local.masterEventNameList = "";
			if (arguments.event.getValue('peID',0)){
				local.strParentEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('peID'),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
				arguments.event.setValue('parentEventContentTitle',local.strParentEvent.qryEventMeta.eventContentTitle);
				local.masterEventNameList = "<li><a href='" & this.link.editEvent & "&eID=" & local.strParentEvent.qryEventMeta.eventID & "' target='_blank'>" & local.strParentEvent.qryEventMeta.eventContentTitle & "</a></li>";	
				arguments.event.setValue('eventStartTime',local.strParentEvent.qryEventTimes_selected.startTime);
				arguments.event.setValue('eventEndTime',local.strParentEvent.qryEventTimes_selected.endTime);
				arguments.event.setValue('categoryID',local.strParentEvent.qryEventCategories.categoryID);
			}

			// recurring event setup
			local.showRecurringEventSettings = 1;
			if (local.showRecurringEventSettings)
				local.qrySiteAFs = this.objAdminEvent.getAdvanceFormulas(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
	
			// Build breadCrumb Trail 
			if( arguments.event.getValue('bc') EQ "c" ){
				appendBreadCrumbs(arguments.event,{ link='#this.link.listCalEvents#&cID=#arguments.event.getValue('cID',0)#', text=encodeForHTML(local.qryCalendar.calendarName) });
			} else {
				appendBreadCrumbs(arguments.event,{ link='#this.link.listEvents#', text="All Site Events" });
			}
			appendBreadCrumbs(arguments.event,{ link='', text="New Event" });
		</cfscript>

		<cfset local.strCrossEventFields = createObject("component","model.admin.common.modules.customFields.customFields").renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				viewMode='bs4', resourceType='EventAdmin', areaName='Event', csrid=this.eventAdminSiteResourceID,
				detailID=0, hideAdminOnly=0, itemType='CrossEvent', itemID=0, trItemType='', trApplicationType='', excludeNonRequiredFields=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_event.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="copyEvent" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.objEvent = CreateObject("component","model.events.events");
			
		// security
		if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent'))
			application.objCommon.redirect("#this.link.listEvents#");

		// get information --------------------------------------------------------------------------
		local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
		arguments.event.setValue('cID',local.strEvent.qryEventMeta.calendarID);
			
		// if event not found, redirect to add event ------------------------------------------------
		if (val(local.strEvent.qryEventMeta.eventID) is 0)
			application.objCommon.redirect(this.link.addEvent & "&cid=#arguments.event.getValue('cID',0)#");
		</cfscript>

		<cfsetting requesttimeout="590">

		<!--- copy event and all details --->
		<cfstoredproc procedure="ev_copyEvent" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('incSiteEventCustomField',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('incSponsor',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('incCreditOffered',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('incRate',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('incCustomField',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('incTickets',0)#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">
			<cfprocparam type="Out" cfsqltype="CF_SQL_INTEGER" variable="local.newEventID">
		</cfstoredproc>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.self.location.href = '#this.link.editevent#&eid=#local.newEventID#';
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="editEvent" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// load objects -----------------------------------------------------------------------------
			local.objEvent = CreateObject("component","model.events.events");
			local.objRegistration = createObject('component', 'model.admin.events.registration');
			local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields");

			// security
			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent'))
				application.objCommon.redirect("#this.link.listEvents#");
			
			// get information --------------------------------------------------------------------------
			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
			arguments.event.setValue('cID',val(local.strEvent.qryEventMeta.calendarID));

			// if event not found, redirect to add event ------------------------------------------------
			if (val(local.strEvent.qryEventMeta.eventID) is 0)
				application.objCommon.redirect(this.link.addEvent & "&cid=#arguments.event.getValue('cID',0)#");

			// recurring series event
			local.isRecurringEvent = val(local.strEvent.qryEventMeta.recurringSeriesID) GT 0;

			// tabs
			local.showRegistrationTab = false;
			local.showFieldsTab = false;
			local.showTicketsTab = false;
			local.showDocsTab = false;
			local.showRatesTab = false;
			local.showCreditTab = false;
			local.showFormsTab = false;
			local.showRegistrantsTab = false;
			local.showSubEventsTab = false;

			if (NOT local.isRecurringEvent) {
				local.onlineRegEvent = local.strEvent.qryEventRegMeta.recordcount and local.strEvent.qryEventRegMeta.registrationType eq "Reg";
				local.qryOrgSurveyForms = createObject("component","eventForm").getFormsByType(siteID=arguments.event.getValue('mc_siteinfo.siteid'), format='V');
				
				local.showRegistrationTab = true;

				if (NOT len(local.strEvent.qryEventMeta.altRegistrationURL) AND local.onlineRegEvent) {
					local.showFieldsTab = true;
					local.showTicketsTab = true;
					local.showRatesTab = true;
					local.showCreditTab = true;
					local.showFormsTab = local.qryOrgSurveyForms.recordCount GT 0;
					local.showSubEventsTab = NOT local.strEvent.isSubEvent;
				}

				local.showDocsTab = local.onlineRegEvent;
				local.showRegistrantsTab = local.strEvent.qryEventRegMeta.recordcount GT 0;
			}

			// validate tab
			if (local.isRecurringEvent AND arguments.event.getTrimValue("tab","details") NEQ 'details')
				location("#this.link.editEvent#&eID=#local.strEvent.qryEventMeta.eventID#", "false");

			// get supporting information --------------------------------------------------------------------------
			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteInfo.siteID'), calendarID=arguments.event.getValue('cID',0));
			local.baseTestLink = getAppBaseLink(applicationInstanceID=local.qryCalendar.applicationInstanceID);
			local.qryCategories = CreateObject("component","model.events.calendar").getCategories(arguments.event.getValue('cID',0),this.languageID,arguments.event.getValue('mc_siteInfo.siteID'));
			local.qryTimeZones 	= this.objAdminEvent.getTimeZones();
			local.categoryTreeName = 'Event Asset Types';
			local.assetCategoryTreeID = this.objCategories.getCategoryTreeID(this.eventAdminSiteResourceID, local.categoryTreeName);
			local.qryAssetCategories = this.objCategories.getIndentedCategoriesForTree(val(local.assetCategoryTreeID)).qryCategories;
			if (local.qryAssetCategories.recordcount){
				local.selectedAssetCategories = this.objAdminEvent.getSelectedAssetCategories(this.siteResourceID);
				arguments.event.setValue('selectedAssetCategoryID', local.selectedAssetCategories);
				local.qryAssetOverlap = this.objAdminEvent.checkOverlap(this.eventAdminSiteResourceID, val(arguments.event.getValue('eID')), arguments.event.getValue('selectedAssetCategoryID',''));
				local.editLink = buildLinkToTool(toolType='EventAdmin',mca_ta='editEvent');
			}
			local.showRecurringEventSettings = NOT local.isRecurringEvent AND NOT local.strEvent.qryEventRegMeta.recordCount;
			if (local.showRecurringEventSettings)
				local.qrySiteAFs = this.objAdminEvent.getAdvanceFormulas(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
			if (local.isRecurringEvent) {
				local.inImportEventsQueue = this.objAdminEvent.isRecurringEventsInImportEventsQueue(siteID=arguments.event.getValue('mc_siteinfo.siteID'), seriesID=local.strEvent.qryEventMeta.recurringSeriesID);
				local.qryRecurringEvents = this.objAdminEvent.getRecurringEvents(siteID=arguments.event.getValue('mc_siteinfo.siteID'), seriesID=local.strEvent.qryEventMeta.recurringSeriesID,
					startEventID=local.strEvent.qryEventMeta.eventID, count=3);
				local.changeRecurringEventTimeLink = buildCurrentLink(arguments.event,"changeRecurringEventTime") & "&eID=#local.strEvent.qryEventMeta.eventID#&mode=direct";
			}
			if (arguments.event.getValue("recurringevimperr",0) EQ 1 AND application.mcCacheManager.sessionValueExists(keyname="recurringEvImpErr#local.strEvent.qryEventMeta.eventID#")) {
				local.arrRecurringEventsImportErr = application.mcCacheManager.sessionGetValue(keyname="recurringEvImpErr#local.strEvent.qryEventMeta.eventID#");
				application.mcCacheManager.sessionDeleteValue('recurringEvImpErr#local.strEvent.qryEventMeta.eventID#');
			}
			local.generateAndDownloadQRCodeLink = buildCurrentLink(arguments.event,"generateAndDownloadQRCode") & "&eID=#local.strEvent.qryEventMeta.eventID#&mode=stream";
			local.strSponsors = createObject("component","model.admin.common.modules.sponsors.sponsors").getSponsorsSelector(resourceType="Event", referenceType="Events", 
									referenceID=local.strEvent.qryEventMeta.eventID, selectorID='eventSponsors', readOnly=false);
			if (val(local.qryCalendar.featureImageConfigID) gt 0) {
				local.strFeaturedImages = this.objAdminEvent.getEventFeaturedImagesStruct(orgCode=arguments.event.getValue('mc_siteInfo.orgCode'), siteCode=arguments.event.getValue('mc_siteInfo.siteCode'),
					siteResourceID=this.siteResourceID, calendarID=local.qryCalendar.calendarID, eventID=local.strEvent.qryEventMeta.eventID, title=local.strEvent.qryEventMeta.eventContentTitle);
			}
			local.strCrossEventFields = local.objResourceCustomFields.renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				viewMode='bs4', resourceType='EventAdmin', areaName='Event', csrid=this.eventAdminSiteResourceID,
				detailID=0, hideAdminOnly=0, itemType='CrossEvent', itemID=this.siteResourceID, trItemType='', trApplicationType='');
			
			local.arrEventSetupIssues = this.objAdminEvent.getEventSetupIssues(strEvent=local.strEvent);
			local.evDTRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&registrationID=#val(local.strEvent.qryEventRegMeta.registrationID)#&mode=stream"
			
			// Registration
			if (local.showRegistrationTab) {
				local.regSchedulesLink = "#local.evDTRootLink#&meth=getRegistrationSchedules";
				local.regScheduleEditLink = buildCurrentLink(arguments.event,"editRegSchedule") & "&registrationID=#val(local.strEvent.qryEventRegMeta.registrationID)#&mode=direct";
			}

			// Fields
			if (local.showFieldsTab) {
				local.eventCustomGridData = [{ 
					title="Registrant Fields", 
					intro="These fields will be shown as part of the event registration process.",
					detailID=0,
					gridext="#this.siteResourceID#_regFields",
					initGridOnLoad=false,
					controllingSRID=this.siteResourceID, 
					resourceType='Event', 
					areaName='Registrant' 
				}];
				local.strEventSpecificFieldsGrid = local.objResourceCustomFields.getGridHTML(arrGridData=local.eventCustomGridData);
			}

			// Tickets
			if (local.showTicketsTab) {
				local.strTickets = getTicketGridsStructure(registrationID=val(local.strEvent.qryEventRegMeta.registrationID), objResourceCustomFields=local.objResourceCustomFields);
			}

			// Documents
			if (local.showDocsTab) {
				local.eventDocumentsLink = "#local.evDTRootLink#&meth=getEventDocuments&eID=#local.strEvent.qryEventMeta.eventID#";
			}

			// Rates
			if (local.showRatesTab) {
				local.ratesListLink = "#local.evDTRootLink#&meth=getRates";
			}

			// Forms
			if (local.showFormsTab) {
				local.EVFormsListLink = "#local.evDTRootLink#&meth=getEventForms&eid=#local.strEvent.qryEventMeta.eventID#";
				local.editFormLink = buildCurrentLink(arguments.event,"editEventForm") & "&pid=#local.strEvent.qryEventMeta.eventID#&mode=direct";
			}
			
			// Registrants
			if (local.showRegistrantsTab) {
				if (local.strEvent.qryEventRegMeta.registrationType EQ "Reg") {
					local.qryTickets = this.objAdminEvent.getTickets(registrationID=val(local.strEvent.qryEventRegMeta.registrationID));
					local.qryCreditTypes = this.objAdminEvent.getCreditTypesOffered(eventid=local.strEvent.qryEventMeta.eventID);
					local.qryEventRates = this.objAdminEvent.getRatesByRegistrationID(registrationID=val(local.strEvent.qryEventRegMeta.registrationID));
					local.qryEventRoles = this.objAdminEvent.getEventRoles(siteResourceID=this.eventAdminSiteResourceID);
					local.qryDistinctEventRates = new Query(sql="select distinct rateID, rateName from qryEventRates", dbtype="query", qryEventRates=local.qryEventRates).execute().getResult();
					local.qryEventForms = this.objAdminEvent.getEventForms(eventID=local.strEvent.qryEventMeta.eventID);
					local.hasEventDocs = this.objAdminEvent.hasEventDocumentsForAnEvent(eventID=arguments.event.getValue('eID'));
					local.eventRegistrantsLink = "#local.evDTRootLink#&meth=getRegistrants&eID=#val(local.strEvent.qryEventMeta.eventID)#&gridmode=regTabGrid";

					local.exportRegPromptLink = "#this.link.exportRegPrompt#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";
					local.exportRegLink = "#this.link.exportReg#&mode=stream&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";
					local.exportRegTransPromptLink = "#this.link.exportRegTransPrompt#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";
					local.exportRegTransLink = "#this.link.exportRegTrans#&mode=stream&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#&rID=#val(local.strEvent.qryEventRegMeta.registrationID)#";
					local.exportOnlineMeetingLogPromptLink = "#this.link.exportOnlineMeetingLogPrompt#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";
					local.exportOnlineMeetingLogLink = "#this.link.exportOnlineMeetingLog#&mode=stream&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#&rID=#val(local.strEvent.qryEventRegMeta.registrationID)#";
					local.exportTicketPromptLink = "#this.link.exportTicketPrompt#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#&rID=#val(local.strEvent.qryEventRegMeta.registrationID)#";
					local.exportTicketLink = "#this.link.exportTicket#&mode=stream&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#&rID=#val(local.strEvent.qryEventRegMeta.registrationID)#";
					
					local.importRegPromptLink = "#this.link.importRegPrompt#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";
					local.importRegLink = "#this.link.importRegComplete#&mode=stream&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";

					local.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list');
					local.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups');

					local.massEmailRegistrants = buildCurrentLink(arguments.event,"massEmailRegistrants") & "&mode=direct";
					local.viewSigninSheetPromptLink = buildCurrentLink(arguments.event,"viewSigninSheetPrompt") & "&eID=#val(local.strEvent.qryEventMeta.eventID)#&mode=direct";
					local.doGenerateSigninSheetLink = buildCurrentLink(arguments.event,"viewSigninSheet") & "&eID=#val(local.strEvent.qryEventMeta.eventID)#&mode=direct";
					
					local.massPrintBadges = buildCurrentLink(arguments.event,"massPrintBadges") & "&defaultBadgeTemplateID=#val(local.strEvent.qryEventMeta.defaultBadgeTemplateID)#&badgeDeviceID=#val(local.strEvent.qryEventMeta.badgeDeviceID)#&mode=direct";
					local.preProcessMassUpdateLink = buildCurrentLink(arguments.event,"preProcessMassUpdateRegistrants") & "&eID=#local.strEvent.qryEventMeta.eventID#";
					local.sampleMassUpdateRegTemplate = buildCurrentLink(arguments.event,"sampleMassUpdateRegTemplate") & "&eID=#local.strEvent.qryEventMeta.eventID#&mode=stream";
					local.exportRegistrantsFormResponsesLink = buildCurrentLink(arguments.event,"exportEVRegistrantsFormResponses") & "&mode=stream&eID=#local.strEvent.qryEventMeta.eventID#";
					local.showSubmitEvaluationFormLink = buildCurrentLink(arguments.event,"showSubmitEvaluationForm") & "&mode=direct";
					local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin');
					local.refundPaymentURL = local.transAdminTool & "&mca_ta=refundPayment&tabMode=registrants&mode=direct";
					local.cleanupInvoicesRegLink = buildCurrentLink(arguments.event,"cleanupInvoicesReg") & "&mode=direct";

					local.strFieldFilters = getFieldFilterSelector(siteID=arguments.event.getValue('mc_siteinfo.siteid'), eventID=local.strEvent.qryEventMeta.eventID);

					local.badgePrinterEnabled = arguments.event.getValue('mc_siteinfo.SF_BADGEPRINTERS');
					local.hasBadgeDevices = createObject("component","model.admin.badges.device").hasBadgeDeviceForSite(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
					local.signInSheetPath = "#application.paths.localSiteComponentRoot.path##UCASE(arguments.event.getValue('mc_siteinfo.orgcode'))#/#UCASE(arguments.event.getValue('mc_siteinfo.sitecode'))#/signinsheet/default.cfm";
					local.hasCustomSigninSheet = FileExists("#local.signInSheetPath#");
					local.qryBadgeTemplates = CreateObject("component","model.admin.badges.device").getCategoriesAndTemplatesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode="BTEVENTS");

					// get the SRID and permissions of TransactionsAdmin
					local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'));
					local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'));

				} else {
					local.eventRSVPsLink = "#local.evDTRootLink#&meth=getRSVPs&eID=#val(local.strEvent.qryEventMeta.eventID)#";
					local.exportRSVPLink = "#this.link.exportRSVP#&mode=stream&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";
					local.viewSigninRSVPsheetLink = buildCurrentLink(arguments.event,"viewSigninRSVPsheet") & "&eID=#val(local.strEvent.qryEventMeta.eventID)#&mode=stream";
				}
			}
			
			// Sub Events
			if (local.showSubEventsTab) {
				local.deleteEventPromptLink = buildCurrentLink(arguments.event,"deleteEventPrompt") & "&mode=direct";
				local.subEventsLink = "#local.evDTRootLink#&meth=getEventsOnSite&peID=#val(local.strEvent.qryEventMeta.eventID)#";
				local.subeventAddLink = buildCurrentLink(arguments.event,"addEvent") & "&bc=c&cID=#arguments.event.getValue('cID')#&peID=#val(local.strEvent.qryEventMeta.eventID)#";
			}
			
			// set form vars ----------------------------------------------------------------------------
			arguments.event.setValue('eID',local.strEvent.qryEventMeta.eventID);
			arguments.event.setValue('status',local.strEvent.qryEventMeta.status);
			arguments.event.setValue('eventTypeID',local.strEvent.qryEventMeta.eventTypeID);
			arguments.event.setValue('eventContentID',local.strEvent.qryEventMeta.eventContentID);
			arguments.event.setValue('eventContentTitle',local.strEvent.qryEventMeta.eventContentTitle);
			arguments.event.setValue('eventSubTitle',local.strEvent.qryEventMeta.eventSubTitle);			
			arguments.event.setValue('hiddenFromCalendar',local.strEvent.qryEventMeta.hiddenFromCalendar);
			arguments.event.setValue('reportCode',local.strEvent.qryEventMeta.reportCode);
			arguments.event.setValue('eventUID',local.strEvent.qryEventMeta.uid);
			arguments.event.setValue('categoryID',valueList(local.strEvent.qryEventCategories.categoryID));
			arguments.event.setValue('eventContent',local.strEvent.qryEventMeta.eventContent);
			arguments.event.setValue('eventStartTime',local.strEvent.qryEventTimes_selected.startTime);
			arguments.event.setValue('eventEndTime',local.strEvent.qryEventTimes_selected.endTime);
			arguments.event.setValue('timeZoneID',local.strEvent.qryEventTimes_selected.timeZoneID);
			arguments.event.setValue('isAllDayEvent',local.strEvent.qryEventMeta.isAllDayEvent);
			arguments.event.setValue('lockTimeZone',local.strEvent.qryEventMeta.lockTimeZoneID);
			
			arguments.event.setValue('GLAccountID',local.strEvent.qryEventMeta.GLAccountID);
			local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.strEvent.qryEventMeta.GLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			arguments.event.setValue('GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded);

			local.createdByMember = application.objMember.getMemberInfo(memberID=local.strEvent.qryEventMeta.enteredByMemberID);
				
			arguments.event.setValue('locationContentID',local.strEvent.qryEventMeta.locationContentID);
			arguments.event.setValue('locationContentTitle',local.strEvent.qryEventMeta.locationContentTitle);
			arguments.event.setValue('locationContent',local.strEvent.qryEventMeta.locationContent);
			arguments.event.setValue('travelContentID',local.strEvent.qryEventMeta.travelContentID);
			arguments.event.setValue('travelContentTitle',local.strEvent.qryEventMeta.travelContentTitle);
			arguments.event.setValue('travelContent',local.strEvent.qryEventMeta.travelContent);
			arguments.event.setValue('contactContentID',local.strEvent.qryEventMeta.contactContentID);
			arguments.event.setValue('contactContentTitle',local.strEvent.qryEventMeta.contactContentTitle);
			arguments.event.setValue('contactContent',local.strEvent.qryEventMeta.contactContent);
			arguments.event.setValue('cancelContentID',local.strEvent.qryEventMeta.cancelContentID);
			arguments.event.setValue('cancelContentTitle',local.strEvent.qryEventMeta.cancelContentTitle);
			arguments.event.setValue('cancelContent',local.strEvent.qryEventMeta.cancelContent);
			arguments.event.setValue('internalNotes',local.strEvent.qryEventMeta.internalNotes);
			arguments.event.setValue('remarketingURL',local.strEvent.qryEventMeta.remarketingURL);
			arguments.event.setValue('defaultBadgeTemplateID',local.strEvent.qryEventMeta.defaultBadgeTemplateID);
			arguments.event.setValue('badgeDeviceID',local.strEvent.qryEventMeta.badgeDeviceID);
			
			arguments.event.setValue('informationContentID',local.strEvent.qryEventMeta.informationContentID);
			arguments.event.setValue('informationContentTitle',local.strEvent.qryEventMeta.informationContentTitle);
			arguments.event.setValue('informationContent',local.strEvent.qryEventMeta.informationContent);
			arguments.event.setValue('emailContactContent',val(local.strEvent.qryEventMeta.emailContactContent));
			arguments.event.setValue('emailLocationContent',val(local.strEvent.qryEventMeta.emailLocationContent));
			arguments.event.setValue('emailCancelContent',val(local.strEvent.qryEventMeta.emailCancelContent));
			arguments.event.setValue('emailTravelContent',val(local.strEvent.qryEventMeta.emailTravelContent));
			arguments.event.setValue('redirectID',val(local.strEvent.qryEventMeta.redirectID));
			arguments.event.setValue('registrationid',val(local.strEvent.qryEventRegMeta.registrationid));
			arguments.event.setValue('registrationtypeid',val(local.strEvent.qryEventRegMeta.registrationtypeid));
			arguments.event.setValue('expireContentID',local.strEvent.qryEventRegMeta.expireContentID);
			arguments.event.setValue('expireContent',local.strEvent.qryEventRegMeta.expireContent);
			arguments.event.setValue('notifyEmail',local.strEvent.qryEventRegMeta.notifyEmail);
			arguments.event.setValue('replyToEmail',local.strEvent.qryEventRegMeta.replyToEmail);
			arguments.event.setValue('registrantCap',local.strEvent.qryEventRegMeta.registrantCap);
			arguments.event.setValue('registrantCapContentID',local.strEvent.qryEventRegMeta.registrantCapContentID);
			arguments.event.setValue('registrantCapContent',local.strEvent.qryEventRegMeta.registrantCapContent);
			arguments.event.setValue('isPriceBasedOnActual',val(local.strEvent.qryEventRegMeta.isPriceBasedOnActual));
			arguments.event.setValue('bulkCountByRate',val(local.strEvent.qryEventRegMeta.bulkCountByRate));
			arguments.event.setValue('isOnlineMeeting',val(local.strEvent.qryEventRegMeta.isOnlineMeeting));
			arguments.event.setValue('onlineEmbedCode',local.strEvent.qryEventRegMeta.onlineEmbedCode);
			arguments.event.setValue('onlineEmbedOverrideLink',local.strEvent.qryEventRegMeta.onlineEmbedOverrideLink);

			arguments.event.setValue('showCredit',local.strEvent.qryEventRegMeta.showCredit);
			arguments.event.setValue('regStartTime',local.strEvent.qryEventRegMeta.startDate);
			arguments.event.setValue('regEndTime',local.strEvent.qryEventRegMeta.endDate);
			arguments.event.setValue('onlineEnterStartTime',local.strEvent.qryEventRegMeta.onlineEnterStartTime);
			arguments.event.setValue('onlineEnterEndTime',local.strEvent.qryEventRegMeta.onlineEnterEndTime);
			arguments.event.setValue('enableRealTimeRoster',local.strEvent.qryEventRegMeta.enableRealTimeRoster);
			arguments.event.setValue('expireDeadlineContentID',local.strEvent.qryEventRegMeta.registrantEditDeadlineContentID);
			arguments.event.setValue('regEditAllowed',local.strEvent.qryEventRegMeta.registrantEditAllowed);
			arguments.event.setValue('regEditDeadline',local.strEvent.qryEventRegMeta.registrantEditDeadline);
			arguments.event.setValue('regEditDeadlineContent',local.strEvent.qryEventRegMeta.registrantEditDeadlineContent);
			arguments.event.setValue('registrantEditRefundContentID',local.strEvent.qryEventRegMeta.registrantEditRefundContentID);
			arguments.event.setValue('regEditRefundContent',local.strEvent.qryEventRegMeta.registrantEditRefundContent);

			local.formattedEventDate = local.objEvent.generateEventDateString(mode='eventAdminEditEvent',
				startTime=local.strEvent.qryEventTimes_selected.startTime, endTime=local.strEvent.qryEventTimes_selected.endTime, 
				isAllDayEvent=local.strEvent.qryEventMeta.isAllDayEvent, showTimeZone=false,
				timeZoneAbbr=local.strEvent.qryEventTimes_selected.TIMEZONEABBR);

			// convert times from central (how stored in db) to default timezone of site
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			if (local.strEvent.qryEventRegMeta.recordcount and local.regTimeZone neq "US/Central") {
				local.regStartTime = local.objTZ.convertTimeZone(dateToConvert=local.strEvent.qryEventRegMeta.startDate,fromTimeZone='US/Central',toTimeZone=local.regTimeZone);
				local.regEndTime = local.objTZ.convertTimeZone(dateToConvert=local.strEvent.qryEventRegMeta.endDate,fromTimeZone='US/Central',toTimeZone=local.regTimeZone);
				local.regStartTime = ParseDateTime(Replace(Left(local.regStartTime,19), "T", " ", "one"));
				local.regEndTime = ParseDateTime(Replace(Left(local.regEndTime,19), "T", " ", "one"));

				arguments.event.setValue('regStartTime',local.regStartTime);
				arguments.event.setValue('regEndTime',local.regEndTime);
				if (len(local.strEvent.qryEventRegMeta.onlineEnterStartTime)) {
					local.onlineEnterStartTime = local.objTZ.convertTimeZone(dateToConvert=local.strEvent.qryEventRegMeta.onlineEnterStartTime,fromTimeZone='US/Central',toTimeZone=local.regTimeZone);
					local.onlineEnterEndTime = local.objTZ.convertTimeZone(dateToConvert=local.strEvent.qryEventRegMeta.onlineEnterEndTime,fromTimeZone='US/Central',toTimeZone=local.regTimeZone);
					local.onlineEnterStartTime = ParseDateTime(Replace(Left(local.onlineEnterStartTime,19), "T", " ", "one"));
					local.onlineEnterEndTime = ParseDateTime(Replace(Left(local.onlineEnterEndTime,19), "T", " ", "one"));

					arguments.event.setValue('onlineEnterStartTime',local.onlineEnterStartTime);
					arguments.event.setValue('onlineEnterEndTime',local.onlineEnterEndTime);
				}
				if (val(local.strEvent.qryEventRegMeta.registrantEditAllowed) AND len(local.strEvent.qryEventRegMeta.registrantEditDeadline)) {
					local.registrantEditDeadline = local.objTZ.convertTimeZone(dateToConvert=local.strEvent.qryEventRegMeta.registrantEditDeadline, fromTimeZone='US/Central', toTimeZone=local.regTimeZone);
					local.registrantEditDeadline = ParseDateTime(Replace(Left(local.registrantEditDeadline,19), "T", " ", "one"));
					arguments.event.setValue('regEditDeadline',local.registrantEditDeadline);
				}
			}
			if (NOT len(arguments.event.getValue('onlineEnterStartTime')))
				arguments.event.setValue('onlineEnterStartTime',arguments.event.getValue('regStartTime'));
			if (NOT len(arguments.event.getValue('onlineEnterEndTime')))
				arguments.event.setValue('onlineEnterEndTime',arguments.event.getValue('regEndTime'));

			// Build breadCrumb Trail -------------------------------------------------------------------
			if( arguments.event.getValue('bc') EQ "c" ){
				appendBreadCrumbs(arguments.event,{ link='#this.link.listCalEvents#&cID=#arguments.event.getValue('cID',0)#', text=encodeForHTML(local.qryCalendar.calendarName) });
			} else {
				appendBreadCrumbs(arguments.event,{ link='#this.link.listEvents#', text="All Site Events" });
			}
			appendBreadCrumbs(arguments.event,{ link='', text=len(local.strEvent.qryEventMeta.eventContentTitle) GT 60 ? encodeForHTML('#left(local.strEvent.qryEventMeta.eventContentTitle,60)#...') : encodeForHTML(local.strEvent.qryEventMeta.eventContentTitle) });

			// Event permissions -----------------------------------------------------------------------
			local.rightsXML = local.strEvent.qryEventPerms.rightsXML;
			local.xmlRights = xmlParse(local.rightsXML);
		</cfscript>
		
		<cfif local.showRatesTab>
			<!--- merchant profiles --->
			<cfstoredproc procedure="ev_getMerchantProfilesByRegistrationID" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.strEvent.qryEventRegMeta.registrationid)#">
				<cfprocresult name="local.qryCurrentProfiles">
			</cfstoredproc>
					
			<!--- Setup GL Account Widget for Revenue GL --->
			<cfset local.strRevenueGLAcctWidgetData = {
				"label": "Revenue GL Account",
				"btnTxt": "Choose GL Account",
				"glatid": 3,
				"widgetMode": "GLSelector",
				"idFldName": "evGLAccountID",
				"idFldValue": val(arguments.event.getValue('GLAccountID',0)),
				"pathFldValue": arguments.event.getValue('GLAccountPath',''),
				"pathNoneTxt": "(no account selected)"
			}>
			<cfset local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData)>
		</cfif>
		
		<!---Get Parent Event data, if any --->
		<cfquery name="local.qryParentEvent" dbtype="query">
			select *
			from [local].strEvent.qryParentEvent
			where eventID = <cfqueryparam value="#arguments.event.getValue('peID',0)#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>
		<cfset arguments.event.setValue('peID',arguments.event.getValue('peID',0))>	
		<cfset arguments.event.setValue('parentEventContentTitle',local.qryParentEvent.eventContentTitle)>
		<cfset local.masterEventNameList = "">		
		<cfif val(arguments.event.getValue('peID',0)) or local.strEvent.isSubEvent>		
			<cfset local.qryGetMasterEvent = this.objAdminEvent.getMasterEvents(event=arguments.event, eventID=local.strEvent.qryEventMeta.eventID)>
			<cfloop query="local.qryGetMasterEvent">
				<cfset local.tempHiperLink = "<li><a href='#this.link.editEvent#&eID=#local.qryGetMasterEvent.eventID#' target='_blank'>#local.qryGetMasterEvent.eventTitle#</a></li>">
				<cfset local.masterEventNameList = listAppend(local.masterEventNameList,local.tempHiperLink,"|")>
			</cfloop>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_event.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editEventForm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objEVForm = createObject("component","eventForm")>

		<cfset local.eventFormID = arguments.event.getValue('efid',0)>

		<cfset local.qryForm = local.objEVForm.getEventForm(siteID=arguments.event.getValue('mc_siteinfo.siteid'), eventFormID=local.eventFormID)>
		<cfset local.qrySections = local.objEVForm.getEventFormSectionsAndQCount(formID=val(local.qryForm.formID))>
		
		<cfset local.formLink = buildCurrentLink(arguments.event,"saveEventForm") & "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_EVForm.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveEventForm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset createObject("component","eventForm").updateEventForm(event=arguments.event)>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					top.eventFormsTable.draw(false);
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getFieldFilterSelector" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">

		<cfset var local = structNew()>
		<cfset local.strReturn = { qryFields="", fieldSelectArea="", fieldSelectControls="" }>

		<!--- role fields --->
		<cfset local.qryEventRoles = this.objAdminEvent.getEventRoles(siteResourceID=this.eventAdminSiteResourceID)>
		<cfset local.objResourceCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.strReturn.qryFields = queryNew("fieldID,role,fieldText,displayTypeCode,dataTypeCode,fieldTypeCode,supportAmt,supportQty,thisField,itemType","varchar,varchar,varchar,varchar,varchar,varchar,bit,bit,varchar,varchar")>

		<cfif arguments.eventID gt 0>
			<!--- registrant fields --->
			<cfset local.qryEventRegStepFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.siteid, resourceType='Event', areaName='Registrant', csrid=this.siteResourceID, detailID=0, hideAdminOnly=0)>
			<cfset local.xmlEventRegStepFields = xmlParse(local.qryEventRegStepFieldsXML.returnXML).xmlRoot>

			<cfif arrayLen(local.xmlEventRegStepFields.xmlChildren)>
				<cfloop array="#local.xmlEventRegStepFields.xmlChildren#" index="local.thisfield">
					<cfif len(local.thisfield.xmlattributes.fieldReference)>
						<cfif queryAddRow(local.strReturn.qryFields)>
							<cfset querySetCell(local.strReturn.qryFields, "fieldID", local.thisfield.xmlattributes.fieldID)>
							<cfset querySetCell(local.strReturn.qryFields, "role", "Registrant Fields")>
							<cfset querySetCell(local.strReturn.qryFields, "fieldText", local.thisfield.xmlattributes.fieldReference)>
							<cfset querySetCell(local.strReturn.qryFields, "displayTypeCode", local.thisfield.xmlattributes.displayTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "dataTypeCode", local.thisfield.xmlattributes.dataTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "fieldTypeCode", local.thisfield.xmlattributes.fieldTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "supportAmt", local.thisfield.xmlattributes.supportAmt)>
							<cfset querySetCell(local.strReturn.qryFields, "supportQty", local.thisfield.xmlattributes.supportQty)>
							<cfset querySetCell(local.strReturn.qryFields, "thisField", local.thisfield)>
							<cfset querySetCell(local.strReturn.qryFields, "itemType", "EventRegCustom")>
						</cfif>
					</cfif>
				</cfloop>
			</cfif>
		</cfif>

		<cfloop query="local.qryEventRoles">
			<cfset local.qryEventRoleFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.siteid, resourceType='EventAdmin', areaName='Role', csrid=this.eventAdminSiteResourceID, detailID=local.qryEventRoles.categoryID, hideAdminOnly=0)>
			<cfset local.xmlEventRoleCustomFields = xmlParse(local.qryEventRoleFieldsXML.returnXML).xmlRoot>
			<cfif arrayLen(local.xmlEventRoleCustomFields.xmlChildren)>
				<cfloop array="#local.xmlEventRoleCustomFields.xmlChildren#" index="local.thisfield">
					<cfif len(local.thisfield.xmlattributes.fieldReference)>
						<cfif queryAddRow(local.strReturn.qryFields)>
							<cfset querySetCell(local.strReturn.qryFields, "fieldID", local.thisfield.xmlattributes.fieldID)>
							<cfset querySetCell(local.strReturn.qryFields, "role", local.qryEventRoles.categoryName)>
							<cfset querySetCell(local.strReturn.qryFields, "fieldText", local.thisfield.xmlattributes.fieldReference)>
							<cfset querySetCell(local.strReturn.qryFields, "displayTypeCode", local.thisfield.xmlattributes.displayTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "dataTypeCode", local.thisfield.xmlattributes.dataTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "fieldTypeCode", local.thisfield.xmlattributes.fieldTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "supportAmt", local.thisfield.xmlattributes.supportAmt)>
							<cfset querySetCell(local.strReturn.qryFields, "supportQty", local.thisfield.xmlattributes.supportQty)>
							<cfset querySetCell(local.strReturn.qryFields, "thisField", local.thisfield)>
							<cfset querySetCell(local.strReturn.qryFields, "itemType", "EventRole")>
						</cfif>
					</cfif>
				</cfloop>
			</cfif>
		</cfloop>

		<cfif arguments.eventID is 0>
			<cfset local.qryCrossEventFieldsXML = local.objResourceCustomFields.getFieldsXML(siteID=arguments.siteid, resourceType='EventAdmin', areaName='Event', csrid=this.eventAdminSiteResourceID, detailID=0, hideAdminOnly=0)>
			<cfset local.xmlCrossEventCustomFields = xmlParse(local.qryCrossEventFieldsXML.returnXML).xmlRoot>
			<cfif arrayLen(local.xmlCrossEventCustomFields.xmlChildren)>
				<cfloop array="#local.xmlCrossEventCustomFields.xmlChildren#" index="local.thisfield">
					<cfif len(local.thisfield.xmlattributes.fieldReference)>
						<cfif queryAddRow(local.strReturn.qryFields)>
							<cfset querySetCell(local.strReturn.qryFields, "fieldID", local.thisfield.xmlattributes.fieldID)>
							<cfset querySetCell(local.strReturn.qryFields, "role", "Cross-Event Fields")>
							<cfset querySetCell(local.strReturn.qryFields, "fieldText", local.thisfield.xmlattributes.fieldReference)>
							<cfset querySetCell(local.strReturn.qryFields, "displayTypeCode", local.thisfield.xmlattributes.displayTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "dataTypeCode", local.thisfield.xmlattributes.dataTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "fieldTypeCode", local.thisfield.xmlattributes.fieldTypeCode)>
							<cfset querySetCell(local.strReturn.qryFields, "supportAmt", local.thisfield.xmlattributes.supportAmt)>
							<cfset querySetCell(local.strReturn.qryFields, "supportQty", local.thisfield.xmlattributes.supportQty)>
							<cfset querySetCell(local.strReturn.qryFields, "thisField", local.thisfield)>
							<cfset querySetCell(local.strReturn.qryFields, "itemType", "CrossEvent")>
						</cfif>
					</cfif>
				</cfloop>
			</cfif>
		</cfif>

		<cfsavecontent variable="local.fieldSelectOptions">
			<cfoutput query="local.strReturn.qryFields" group="role">
				<optgroup label="#local.strReturn.qryFields.role#">
				<cfoutput>
					<option value="#local.strReturn.qryFields.fieldID#_#local.strReturn.qryFields.displayTypeCode#_#local.strReturn.qryFields.dataTypeCode#_#local.strReturn.qryFields.itemType#">#local.strReturn.qryFields.fieldText#</option>
				</cfoutput>
				</optgroup>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.fieldSelectArea">
			<cfoutput>
			<div class="row">
				<div class="col-md-12">
					<div id="divRFieldArea"></div>
					<div id="divRFieldAreaControl" class="mt-2 ml-2">
						<a href="##" onClick="showFieldValueArea();return false;"><i class="fa-solid fa-circle-plus fa-lg mr-2"></i>Add field to filter</a>
					</div>
				</div>
			</div>
			<script id="mc_evrFieldFilterTempate" type="text/x-handlebars-template">
				<div id="divMCRFilterField{{newItem}}" class="divMCRFilterFields row m-2">
					<div class="pr-2">
						<a href="##" onClick="hideFieldValueArea({{newItem}});return false;">
							<i class="fa-solid fa-circle-minus fa-lg"></i>
						</a>
					</div>
					<div id="rFieldIDselected{{newItem}}" class="col-md-4 px-0 px-md-2 d-none"></div>
					<div class="col-md-4 pl-0 pl-md-2">
						<select name="rFieldID{{newItem}}" id="rFieldID{{newItem}}" class="form-control form-control-sm rfieldselector" onChange="copyFieldValueArea({{newItem}});">
							<option value=""></option>
						</select>
					</div>
					<div id="rFieldData{{newItem}}" class="col-md-7 p-0 mcEvRFieldData"></div>
					<div id="rFieldDataErr{{newItem}}" class="d-flex text-danger align-items-center pl-md-2"></div>
				</div>
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.fieldSelectControls">
			<cfoutput><select name="mcEvRFilterFields" id="mcEvRFilterFields" class="form-control form-control-sm"></cfoutput>
			<cfoutput query="local.strReturn.qryFields" group="role">
				<optgroup label="#local.strReturn.qryFields.role#">
				<cfoutput>
					<option value="#local.strReturn.qryFields.fieldID#_#local.strReturn.qryFields.displayTypeCode#_#local.strReturn.qryFields.dataTypeCode#_#local.strReturn.qryFields.itemType#">#local.strReturn.qryFields.fieldText#</option>
				</cfoutput>
				</optgroup>
			</cfoutput>
			<cfoutput></select></cfoutput>

			<cfoutput>
			<div id="divRFieldArea" style="display:none;">
				<cfloop query="local.strReturn.qryFields">
					<div id="divRF_#local.strReturn.qryFields.fieldID#_#local.strReturn.qryFields.displayTypeCode#_#local.strReturn.qryFields.dataTypeCode#_#local.strReturn.qryFields.itemType#">
						<cfswitch expression="#local.strReturn.qryFields.displayTypeCode#">
							<cfcase value="TEXTBOX">
								<div class="row">
									<div class="col-md-3 pr-md-0">
										<select id="RF_#local.strReturn.qryFields.fieldID#_exp" name="RF_#local.strReturn.qryFields.fieldID#_exp" class="form-control form-control-sm mcevrfieldexpression" onChange="changeExpression($(this));">
											<option value="eq">equals</option>
											<option value="exists">has a value</option>
											<option value="not_exists">does not have a value</option>
										</select>
									</div>
									<div class="col-md-9">
										<input type="text" size="20" autocomplete="off" id="RF_#local.strReturn.qryFields.fieldID#" name="RF_#local.strReturn.qryFields.fieldID#" value="" data-dataTypeCode="#local.strReturn.qryFields.dataTypeCode#" data-displayTypeCode="#local.strReturn.qryFields.displayTypeCode#" class="form-control form-control-sm" onBlur="validateFieldText($(this));">
									</div>
								</div>
							</cfcase>
							<cfcase value="SELECT,CHECKBOX,RADIO">
								<cfset local.tmpXML = XMLParse(local.strReturn.qryFields.thisField)>
								<div class="row">
									<div class="col-md-3 pr-md-0">
										<select id="RF_#local.strReturn.qryFields.fieldID#_exp" name="RF_#local.strReturn.qryFields.fieldID#_exp" class="form-control form-control-sm mcevrfieldexpression" onChange="changeExpression($(this));">
											<option value="eq">equals</option>
											<option value="exists">has a value</option>
											<option value="not_exists">does not have a value</option>
										</select>
									</div>
									<div class="col-md-9">
										<select id="RF_#local.strReturn.qryFields.fieldID#" name="RF_#local.strReturn.qryFields.fieldID#" class="form-control form-control-sm mcevrfieldexpression" data-dataTypeCode="#local.strReturn.qryFields.dataTypeCode#" data-displayTypeCode="#local.strReturn.qryFields.displayTypeCode#" onChange="validateFieldSelect($(this));">
											<option value=""></option>
											<cfloop array="#local.tmpXML.xmlRoot.xmlchildren#" index="local.thisoption">
												<option value="#local.thisoption.xmlattributes.valueID#">#local.thisoption.xmlattributes.fieldValue#</option>
											</cfloop>
										</select>
									</div>
								</div>
							</cfcase>
							<cfcase value="TEXTAREA">
								<div class="row">
									<div class="col-md-3 pr-md-0">
										<select id="RF_#local.strReturn.qryFields.fieldID#_exp" name="RF_#local.strReturn.qryFields.fieldID#_exp" class="form-control form-control-sm mcevrfieldexpression" onChange="changeExpression($(this));">
											<option value="contains">contains</option>
											<option value="exists">has a value</option>
											<option value="not_exists">does not have a value</option>
										</select>
									</div>
									<div class="col-md-9">
										<input type="text" size="30" autocomplete="off" id="RF_#local.strReturn.qryFields.fieldID#" name="RF_#local.strReturn.qryFields.fieldID#" value="" data-dataTypeCode="#local.strReturn.qryFields.dataTypeCode#" data-displayTypeCode="#local.strReturn.qryFields.displayTypeCode#" class="form-control form-control-sm" onBlur="validateFieldText($(this));">
									</div>
								</div>
							</cfcase>
							<cfcase value="DATE">
								<div class="row">
									<div class="col-md-3 pr-md-0">
										<select id="RF_#local.strReturn.qryFields.fieldID#_exp" name="RF_#local.strReturn.qryFields.fieldID#_exp" class="form-control form-control-sm mcevrfieldexpression" onChange="changeExpression($(this));">
											<option value="between">between</option>
											<option value="exists">has a value</option>
											<option value="not_exists">does not have a value</option>
										</select>
									</div>
									<div class="col-md-9" id="RF_#local.strReturn.qryFields.fieldID#" data-displayTypeCode="#local.strReturn.qryFields.displayTypeCode#">
										<div class="row">
											<div class="col pr-md-0">
												<div class="input-group input-group-sm">
													<input type="text" size="12" autocomplete="off" id="RF_#local.strReturn.qryFields.fieldID#_lower" name="RF_#local.strReturn.qryFields.fieldID#_lower" value="" data-dataTypeCode="#local.strReturn.qryFields.dataTypeCode#" data-displayTypeCode="#local.strReturn.qryFields.displayTypeCode#" class="form-control form-control-sm dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="RF_#local.strReturn.qryFields.fieldID#_lower"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
											<div class="col-auto d-flex flex-shrink-1 px-1 align-self-center">and</div>
											<div class="col pl-md-0">
												<div class="input-group input-group-sm">
													<input type="text" size="12" autocomplete="off" id="RF_#local.strReturn.qryFields.fieldID#_upper" name="RF_#local.strReturn.qryFields.fieldID#_upper" value="" data-dataTypeCode="#local.strReturn.qryFields.dataTypeCode#" data-displayTypeCode="#local.strReturn.qryFields.displayTypeCode#" class="form-control form-control-sm dateControl">
													<div class="input-group-append">
														<span class="input-group-text cursor-pointer calendar-button" data-target="RF_#local.strReturn.qryFields.fieldID#_upper"><i class="fa-solid fa-calendar"></i></span>
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</cfcase>
							<cfcase value="DOCUMENT">
								<select id="RF_#local.strReturn.qryFields.fieldID#_exp" name="RF_#local.strReturn.qryFields.fieldID#_exp" class="form-control form-control-sm mcevrfieldexpression">
									<option value="exists">has document on file</option>
									<option value="not_exists">does not have document on file</option>
								</select>
							</cfcase>
						</cfswitch>
					</div>
				</cfloop>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.strReturn.fieldSelectJS">
			<cfoutput>
			function showFieldValueArea() {
				var divcount = $('##divRFieldArea div').length;
				var obj = { newItem:divcount++ }; 

				var evrFieldFilterTemplateSource = $('##mc_evrFieldFilterTempate').html();
				var evrFieldFilterTemplate = Handlebars.compile(evrFieldFilterTemplateSource);
				$('##divRFieldArea').append(evrFieldFilterTemplate(obj));
				$('##rFieldID'+obj.newItem).append($('##mcEvRFilterFields').html());
			}
			function copyFieldValueArea(x) {
				var sel = $('##rFieldID'+x);
				var divSelected = $('##rFieldIDselected'+x);
				var divControl = $('##rFieldData'+x);
				var divErr = $('##rFieldDataErr'+x);
				if (sel.val() == '') {
					divSelected.addClass('d-none').html('');
					divControl.html('');
					divErr.html('');
				} else {
					var selSelected = $('##rFieldID'+x+' :selected');
	    			var selOptionLabel = selSelected.text();
	    			var selGroupLabel = selSelected.parent().attr('label');
					divSelected.removeClass('d-none').html(selGroupLabel + ': ' + selOptionLabel);

					sel.parent().addClass('d-none');

					var rfarea = $('##divRF_'+sel.val());
					divControl.html(rfarea.clone().html());
					divControl.find('div[data-displayTypeCode="DATE"]').each(function(i) { 
						mca_setupDatePickerRangeFields($(this).attr('id')+'_lower',$(this).attr('id')+'_upper');
						mca_setupCalendarIcons('frmFilter');
					});
				}
			}
			function hideFieldValueArea(x) {
				$('##divMCRFilterField'+x).html('').hide();
			}
			function validateFieldText(el) {
				var dt = el.attr('data-dataTypeCode');
				var val = el.val();
				if (dt == 'STRING' && val.length == 0) {
					el.closest('.mcEvRFieldData').next().html('<i class="fa-solid fa-triangle-exclamation fa-lg"></i>');
				} else if (dt == 'DECIMAL2' && !validateFieldText_Decimal(val)) {
					el.closest('.mcEvRFieldData').next().html('<i class="fa-solid fa-triangle-exclamation fa-lg"></i>');
				} else if (dt == 'INTEGER' && !validateFieldText_Integer(val)) {
					el.closest('.mcEvRFieldData').next().html('<i class="fa-solid fa-triangle-exclamation fa-lg"></i>');
				} else {
					el.closest('.mcEvRFieldData').next().html('');
				}
			}
			function validateFieldText_Integer(val) {
				var fldval = Number($.trim(val));
				if (fldval != '') {
					if (fldval !== parseInt(fldval)) return false;
					else return true;
				} else {
					return false;
				}
			}
			function validateFieldText_Decimal(val) {
				var fldval = Number($.trim(val));
				if (fldval != '') {
					if (fldval !== parseFloat(fldval)) return false;
					else return true;
				} else {
					return false;
				}
			}
			function validateFieldSelect(el) {
				if (el.val() == '') {
					el.closest('.mcEvRFieldData').next().html('<i class="fa-solid fa-triangle-exclamation fa-lg"></i>');
				} else {
					el.closest('.mcEvRFieldData').next().html('');	
				}
			}
			function validateFieldDate(elLower,elUpper) {
				if ($('##'+elLower).val() == '' || $('##'+elUpper).val() == '') {
					$('##'+elLower).closest('.mcEvRFieldData').next().html('<i class="fa-solid fa-triangle-exclamation fa-lg"></i>');
				} else {
					$('##'+elLower).closest('.mcEvRFieldData').next().html('');	
				}
			}
			function validateFields() {
				$('##divRFieldArea div select.rfieldselector').not(':disabled').each(function(i) { 
					var sel = $(this);
					if (sel.val() != '') {
						sel.parent().next().find('input[data-displayTypeCode="TEXTBOX"], input[data-displayTypeCode="TEXTAREA"]').each(function(j) { 
							var exp = $(this).closest('.mcEvRFieldData').find('select.mcevrfieldexpression').val();
							if (!(exp == 'exists' || exp == 'not_exists')) validateFieldText($(this));
						});
						sel.parent().next().find('select[data-displayTypeCode="SELECT"], select[data-displayTypeCode="CHECKBOX"], select[data-displayTypeCode="RADIO"]').each(function(j) { 
							var exp = $(this).closest('.mcEvRFieldData').find('select.mcevrfieldexpression').val();
							if (!(exp == 'exists' || exp == 'not_exists')) validateFieldSelect($(this));
						});
						sel.parent().next().find('div[data-displayTypeCode="DATE"]').each(function(k) { 
							var exp = $(this).closest('.mcEvRFieldData').find('select.mcevrfieldexpression').val();
							if (!(exp == 'exists' || exp == 'not_exists')) validateFieldDate($(this).attr('id')+'_lower',$(this).attr('id')+'_upper');
						});
					}
				});
				if ($('##divRFieldArea i.fa-triangle-exclamation').length == 0) return true;
				else return false;
			}
			function changeExpression(el) {
				var sel = el.val();
				if (sel == 'exists' || sel == 'not_exists') {
					el.parent().next().find('input,select').val('');
					el.parent().next().hide();
				} else {
					el.parent().next().show();
				} 
				el.closest('.mcEvRFieldData').next().html('');
			}
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.strReturn>
	</cffunction>
	
	<cffunction name="editSubEvent" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.subeventID = int(val(arguments.event.getValue('eid',0)))>
		<cfset local.parenteventID = int(val(arguments.event.getValue('peid',0)))>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubEventSettings">
			select forceSelect, sendStaffConfirmation, sendRegConfirmation
			from dbo.ev_subEvents
			where parentEventID = #local.parenteventID#
			and eventID = #local.subeventID#
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_subEventSettings.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSubEventSettings" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.subeventID = int(val(arguments.event.getValue('eid',0)))>
		<cfset local.parenteventID = int(val(arguments.event.getValue('peid',0)))>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySubEventSettings">
			set nocount on

			declare @subEventID int

			select @subEventID = subeventID 
			from dbo.ev_subEvents 
			where parentEventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.parenteventID#">
			and eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.subeventID#">

			update dbo.ev_subEvents
			set forceSelect = <cfqueryparam value="#arguments.event.getValue('forceSelect',0)#" cfsqltype="CF_SQL_INTEGER">,
				sendStaffConfirmation = <cfqueryparam value="#arguments.event.getValue('sendStaffConfirmation',0)#" cfsqltype="CF_SQL_BIT">,
				sendRegConfirmation = <cfqueryparam value="#arguments.event.getValue('sendRegConfirmation',0)#" cfsqltype="CF_SQL_BIT">
			where subEventID = @subEventID

			<cfif arguments.event.getValue('forceSelect',0) is 0>
				delete from dbo.ev_rateMappings
				where subEventID = @subEventID
			</cfif>

			set nocount off
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfoutput><script type="text/javascript">top.reloadLinkEvents();</script></cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="linkEvent" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.calendarID = arguments.event.getValue('cid',0)>
		<cfset local.eventID = arguments.event.getValue('eid',0)>
		<cfset local.taskType = arguments.event.getValue('taskType','link')>
		<cfset local.startFrom = dateformat(dateAdd('yyyy',-1,now()),'m/d/yyyy')>
		<cfset local.startTo = dateformat(now(),'m/d/yyyy')>
		<cfset local.qryCalendars = CreateObject("component","model.events.calendar").getCalendars(arguments.event.getValue('mc_siteInfo.siteID'))>

		<cfset local.linkEventsListLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getLinkEventsOnSite&cid=#local.calendarID#&eid=#local.eventID#&taskType=#local.taskType#&mode=stream"/>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_linkEvent.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveEventDetails" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone");
			// security 
			if (arguments.event.getValue('eID',0) gt 0 and NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent'))
				application.objCommon.redirect('#this.link.message#&message=1');

			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteid'), calendarID=arguments.event.getValue('cID',0));
			local.baseTestLink = getAppBaseLink(applicationInstanceID=local.qryCalendar.applicationInstanceID);
			local.isRecurringEvent = false;
			local.updateEventTimeSettings = true;
		</cfscript>

		<cfset local.crossEventCustomFieldsXML = createObject("component","model.admin.common.modules.customFields.customFields").getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				resourceType='EventAdmin', areaName='Event', csrid=this.eventAdminSiteResourceID, detailID=0, hideAdminOnly=0)>
		<cfset local.crossEventCustomFieldArr = xmlParse(local.crossEventCustomFieldsXML.returnXML).xmlRoot.xmlChildren>

		<!--- put cross-event custom fields and field types into array --->
		<cfset local.arrCrossEventCustomFields = []>
		<cfif arrayLen(local.crossEventCustomFieldArr)>
			<cfloop array="#local.crossEventCustomFieldArr#" index="local.thisfield">
				<cfset local.tmpAtt = local.thisfield.xmlattributes>
				<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
										displayTypeCode=local.tmpAtt.displayTypeCode, 
										dataTypeCode=local.tmpAtt.dataTypeCode, 
										value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','') }>
				<cfset arrayAppend(local.arrCrossEventCustomFields,local.tmpStr)>
			</cfloop>
		</cfif>

		<cfif arguments.event.getValue('eID',0) gt 0>
			<cfset local.qryEvent = this.objAdminEvent.getEventTimeDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=arguments.event.getValue('eID'))>
			<cfset local.isRecurringEvent = val(local.qryEvent.recurringSeriesID) GT 0>
			<cfif local.isRecurringEvent>
				<cfset local.updateEventTimeSettings = false>
				<cfset arguments.event.setValue('isAllDayEvent',local.qryEvent.isAllDayEvent)>
				<cfset arguments.event.setValue('lockTimeZone',val(local.qryEvent.lockTimeZoneID) ? 1 : 0)>
				<cfset arguments.event.setValue('eventTimeZoneID',val(local.qryEvent.timeZoneID))>
				<cfset arguments.event.setValue('eventStartTime', local.qryEvent.isAllDayEvent ? DateFormat(local.qryEvent.startTime,'m/d/yyyy') : DateTimeFormat(local.qryEvent.startTime,'m/d/yyyy - h:nn tt'))>
				<cfset arguments.event.setValue('eventEndTime', local.qryEvent.isAllDayEvent ? DateFormat(local.qryEvent.endTime,'m/d/yyyy') : DateTimeFormat(local.qryEvent.endTime,'m/d/yyyy - h:nn tt'))>
			<cfelseif val(local.qryEvent.registrationID) EQ 0 AND val(arguments.event.getValue('recurrenceAFID',0)) AND len(arguments.event.getValue('recurrenceEndsOn',''))>
				<cfset arguments.event.setValue('convertToRecurringEvent',1)>
				<cfset local.isRecurringEvent = true>
			</cfif>
		</cfif>

		<cfset local.arrTimeZoneIDs = listToArray(local.objTSTZ.getTZIDList())>
		<cfif arguments.event.getValue('isAllDayEvent',0)>
			<cfset local.startDateTime = ParseDateTime(arguments.event.getValue('eventStartTime'))>
			<cfset local.endDateTime = ParseDateTime("#arguments.event.getValue('eventEndTime')# 23:59:59.997")>
		<cfelse>
			<cfset local.startDateTime = ParseDateTime("#replace(arguments.event.getValue('eventStartTime'),' - ',' ')#")>
			<cfset local.endDateTime = ParseDateTime("#replace(arguments.event.getValue('eventEndTime'),' - ',' ')#")>
			<cfset local.structAllEventTimes = getEventTimeinAllTimeZones(local.startDateTime,local.endDateTime,arguments.event.getValue('eventTimeZoneID',0))>
		</cfif>

		<cfif arguments.event.getValue('eID',0) gt 0>
			<cfif arguments.event.getValue('lockTimeZone',0)>
				<cfset local.lockTimeZoneID = arguments.event.getValue('eventTimeZoneID',0)>
			<cfelse>
				<cfset local.lockTimeZoneID = 0>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateEvent">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @eventID int, @timeID int, @uid uniqueidentifier, @newuid uniqueidentifier, @recordedByMemberID int,
						@orgID int, @calendarID int, @msgjson varchar(max), @defaultLanguageID int, @eventTitle varchar(200);
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;

					-- Get orgID and calendarID for audit logging
					SELECT @orgID = s.orgID, @defaultLanguageID = s.defaultLanguageID
					FROM dbo.sites s
					INNER JOIN dbo.ev_events e ON e.siteID = s.siteID
					WHERE e.eventID = @eventID;

					SELECT @calendarID = ce.sourceCalendarID
					FROM dbo.ev_calendarEvents ce
					WHERE ce.sourceEventID = @eventID;
					
					-- Get event title for audit message
					SELECT @eventTitle = ISNULL(NULLIF(ec.contentTitle, ''), 'Event ID ' + CAST(@eventID AS varchar(10)))
					FROM dbo.ev_events e
					CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) ec
					WHERE e.eventID = @eventID;

					IF OBJECT_ID('tempdb..##tmpContentAuditLogData') IS NOT NULL
						DROP TABLE ##tmpContentAuditLogData;
					CREATE TABLE ##tmpContentAuditLogData (
						[rowCode] varchar(20) PRIMARY KEY,
						[Event Title] varchar(max),
						[Event Content] varchar(max),
						[Contact Content Title] varchar(max),
						[Contact Content] varchar(max),
						[Location Content Title] varchar(max),
						[Location Content] varchar(max),
						[Cancel Content Title] varchar(max),
						[Cancel Content] varchar(max),
						[Travel Content Title] varchar(max),
						[Travel Content] varchar(max),
						[Information Content Title] varchar(max),
						[Information Content] varchar(max),
						[Event Start Time] varchar(max),
						[Event End Time] varchar(max),
						[All Day Event] varchar(max),
						[Categories] varchar(max),
						[Asset Categories] varchar(max),
						[Redirect Name] varchar(max),
						[Redirect URL] varchar(max),
						[Event Sub Title] varchar(max),
						[Lock Time Zone] varchar(max),
						[Status] varchar(max),
						[Report Code] varchar(max),
						[Internal Notes] varchar(max),
						[Hidden From Calendar] varchar(max),
						[Email Contact Content] varchar(max),
						[Email Location Content] varchar(max),
						[Email Cancel Content] varchar(max),
						[Email Travel Content] varchar(max),
						[Remarketing URL] varchar(max),
						[Event Type] varchar(max),
						[Alt Registration URL] varchar(max),
						[GL Account] varchar(max),
					);

					-- Discover and add event custom fields to audit table dynamically
					DECLARE @crossEventFieldUsageID int, @siteResourceID int, @eventAdminSiteResourceID int;

					-- Get usage ID for CrossEvent custom fields and siteResourceIDs
					SELECT @crossEventFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Event',NULL);
					SELECT @siteResourceID = siteResourceID FROM dbo.ev_events WHERE eventID = @eventID;
					SELECT @eventAdminSiteResourceID = dbo.fn_getSiteResourceIDForResourceType('EventAdmin', @siteID);

					-- Add custom field columns to audit table using reusable stored procedure
					EXEC dbo.cf_populateAuditLogWithCustomFields
						@operation = 'ALTER_TABLE',
						@auditLogTableName = '##tmpContentAuditLogData',
						@usageID = @crossEventFieldUsageID,
						@controllingSiteResourceID = @eventAdminSiteResourceID;

					-- Insert DATATYPECODE row with standard fields
					INSERT INTO ##tmpContentAuditLogData ([rowCode], [Event Title], [Event Content], [Contact Content Title], [Contact Content],
						[Location Content Title], [Location Content], [Cancel Content Title], [Cancel Content],
						[Travel Content Title], [Travel Content], [Information Content Title], [Information Content],
						[Event Start Time], [Event End Time], [All Day Event], [Categories], [Asset Categories], [Redirect Name], [Redirect URL],
						[Event Sub Title], [Lock Time Zone], [Status], [Report Code], [Internal Notes], [Hidden From Calendar],
						[Email Contact Content], [Email Location Content], [Email Cancel Content], [Email Travel Content], [Remarketing URL],
						[Event Type], [Alt Registration URL], [GL Account])
					VALUES ('DATATYPECODE', 'STRING', 'CONTENTOBJ', 'STRING', 'CONTENTOBJ', 'STRING', 'CONTENTOBJ', 'STRING', 'CONTENTOBJ', 'STRING',
							'CONTENTOBJ', 'STRING', 'CONTENTOBJ', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING',
							'STRING', 'STRING', 'STRING', 'CONTENTOBJ', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING',
							'STRING', 'STRING', 'STRING');

					-- Add custom field datatypes to DATATYPECODE row using reusable stored procedure
					EXEC dbo.cf_populateAuditLogWithCustomFields
						@operation = 'DATATYPECODE',
						@auditLogTableName = '##tmpContentAuditLogData',
						@usageID = @crossEventFieldUsageID,
						@controllingSiteResourceID = @eventAdminSiteResourceID;

					-- Execute dynamic OLDVAL INSERT with standard fields
					INSERT INTO ##tmpContentAuditLogData ([rowCode], [Event Title], [Event Content], [Contact Content Title], [Contact Content],
						[Location Content Title], [Location Content], [Cancel Content Title], [Cancel Content],
						[Travel Content Title], [Travel Content], [Information Content Title], [Information Content],
						[Event Start Time], [Event End Time], [All Day Event], [Categories], [Asset Categories], [Redirect Name], [Redirect URL],
						[Event Sub Title], [Lock Time Zone], [Status], [Report Code], [Internal Notes], [Hidden From Calendar],
						[Email Contact Content], [Email Location Content], [Email Cancel Content], [Email Travel Content], [Remarketing URL],
						[Event Type], [Alt Registration URL], [GL Account])
					SELECT 'OLDVAL',
						ISNULL(ec.contentTitle, ''),
						ISNULL(ec.rawContent, ''),
						ISNULL(cc.contentTitle, ''),
						ISNULL(cc.rawContent, ''),
						ISNULL(lc.contentTitle, ''),
						ISNULL(lc.rawContent, ''),
						ISNULL(cnc.contentTitle, ''),
						ISNULL(cnc.rawContent, ''),
						ISNULL(tc.contentTitle, ''),
						ISNULL(tc.rawContent, ''),
						ISNULL(ic.contentTitle, ''),
						ISNULL(ic.rawContent, ''),
						CASE WHEN e.isAllDayEvent = 1 THEN FORMAT(t.startTime, 'M/d/yyyy') ELSE FORMAT(t.startTime, 'M/d/yyyy - h:mm tt') END,
						CASE WHEN e.isAllDayEvent = 1 THEN FORMAT(t.endTime, 'M/d/yyyy') ELSE FORMAT(t.endTime, 'M/d/yyyy - h:mm tt') END,
						CASE WHEN e.isAllDayEvent = 1 THEN 'Yes' ELSE 'No' END,
						ISNULL(categories.categoryList, ''),
						ISNULL(assetCategories.assetCategoryList, ''),
						ISNULL(redirect.redirectName, ''),
						ISNULL(redirect.redirectURL, ''),
						ISNULL(e.eventSubTitle, ''),
						CASE WHEN e.lockTimeZoneID IS NOT NULL THEN 'Yes' ELSE 'No' END,
						CASE WHEN e.[status] = 'A' THEN 'Active' ELSE 'Inactive' END,
						e.reportCode,
						ISNULL(e.internalNotes, ''),
						CASE WHEN e.hiddenFromCalendar = 1 THEN 'Yes' ELSE 'No' END,
						CASE WHEN e.emailContactContent = 1 THEN 'Yes' ELSE 'No' END,
						CASE WHEN e.emailLocationContent = 1 THEN 'Yes' ELSE 'No' END,
						CASE WHEN e.emailCancelContent = 1 THEN 'Yes' ELSE 'No' END,
						CASE WHEN e.emailTravelContent = 1 THEN 'Yes' ELSE 'No' END,
						ISNULL(e.remarketingURL, ''),
						ISNULL(et.eventType, ''),
						ISNULL(e.altRegistrationURL, ''),
						ISNULL(gl.accountName, '')
					FROM dbo.ev_events e
					CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) ec
					CROSS APPLY dbo.fn_getContent(e.contactContentID, @defaultLanguageID) cc
					CROSS APPLY dbo.fn_getContent(e.locationContentID, @defaultLanguageID) lc
					CROSS APPLY dbo.fn_getContent(e.cancellationPolicyContentID, @defaultLanguageID) cnc
					CROSS APPLY dbo.fn_getContent(e.travelContentID, @defaultLanguageID) tc
					CROSS APPLY dbo.fn_getContent(e.informationContentID, @defaultLanguageID) ic
					LEFT JOIN dbo.ev_times t ON t.eventID = e.eventID AND t.timeZoneID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eventTimeZoneID',0)#">
					OUTER APPLY (
						SELECT STUFF((SELECT ', ' + c.category
							FROM dbo.ev_eventCategories ec2
							INNER JOIN dbo.ev_categories c ON c.categoryID = ec2.categoryID
							WHERE ec2.eventID = e.eventID
							ORDER BY ec2.categoryOrder, c.category
							FOR XML PATH('')), 1, 2, '') AS categoryList
					) categories
					OUTER APPLY (
						SELECT STUFF((SELECT ', ' + cat.categoryName
							FROM dbo.cms_categorySiteResources csr
							INNER JOIN dbo.cms_categories cat ON cat.categoryID = csr.categoryID
							WHERE csr.siteResourceID = e.siteResourceID
							ORDER BY cat.categoryName
							FOR XML PATH('')), 1, 2, '') AS assetCategoryList
					) assetCategories
					LEFT JOIN dbo.siteRedirects redirect ON redirect.redirectID = e.redirectID
					LEFT JOIN dbo.ev_eventTypes et ON et.eventTypeID = e.eventTypeID
					LEFT JOIN dbo.tr_GLAccounts gl ON gl.GLAccountID = e.GLAccountID
					WHERE e.eventID = @eventID;

					-- Add custom field OLDVAL data using reusable stored procedure
					EXEC dbo.cf_populateAuditLogWithCustomFields
						@operation = 'OLDVAL',
						@auditLogTableName = '##tmpContentAuditLogData',
						@usageID = @crossEventFieldUsageID,
						@controllingSiteResourceID = @eventAdminSiteResourceID,
						@itemID = @siteResourceID,
						@itemType = 'CrossEvent';

					BEGIN TRAN;
						-- update event details 
						EXEC dbo.ev_updateEvent @eventID=@eventID,
							@eventTypeID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eventTypeID')#">,
							@enteredByMemberID=<cfqueryparam cfsqltype="cf_sql_integer" value="#session.cfcUser.memberData.memberID#">,
							@eventSubTitle=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('eventSubTitle')#">,
							<cfif local.lockTimeZoneID gt 0>
								@lockTimeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.lockTimeZoneID#">,
							<cfelse>
								@lockTimeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" null="TRUE">,
							</cfif>
							@isAllDayEvent=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('isAllDayEvent',0)#">,
							@status=<cfqueryparam cfsqltype="cf_sql_char" value="#arguments.event.getValue('status')#">,
							@reportCode=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('reportCode','')#">,
							@internalNotes=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getTrimValue('internalNotes','')#">,
							@hiddenFromCalendar=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('hiddenFromCalendar',0)#">,
							@emailContactContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailContactContent',0)#">,
							@emailLocationContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailLocationContent',0)#">,
							@emailCancelContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailCancelContent',0)#">,
							@emailTravelContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailTravelContent',0)#">,
							@remarketingURL=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('remarketingURL','')#">;

						-- superuser update uid
						<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser) and len(arguments.event.getTrimValue('evUID',''))>
							select @uid = [uid] from dbo.ev_events where eventID = @eventID;
							set @newuid = <cfqueryparam cfsqltype="cf_sql_idstamp" value="#trim(arguments.event.getTrimValue('evUID'))#">;

							IF @uid <> @newuid AND NOT EXISTS (select eventID from dbo.ev_events where uid = @newuid and eventID <> @eventID)
								update dbo.ev_events
								set [uid] = @newuid
								where eventID = @eventID;
						</cfif>

						EXEC dbo.ev_saveEventCategories @eventID=@eventID, @recordedByMemberID=@recordedByMemberID,
							@categoryIDList=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="0#arguments.event.getValue('categoryID')#">;

						EXEC dbo.ev_refreshCalendarEventsCache @siteID=@siteID;

						EXEC dbo.ev_refreshCalendarEventsCategoryIDList @siteID=@siteID, @eventID=@eventID;

						-- cross-event custom fields
						<cfif arrayLen(local.arrCrossEventCustomFields)>
							#this.objAdminEvent.updateEventCustomFields(siteResourceID=this.siteResourceID, arrCrossEventCustomFields=local.arrCrossEventCustomFields)#
						</cfif>

						<cfif local.updateEventTimeSettings>
							UPDATE dbo.ev_events 
							SET defaultTimeID = null, lockedTimeID = null 
							WHERE eventID=@eventID;

							DELETE FROM dbo.ev_times
							WHERE eventID=@eventID;

							<cfif arguments.event.getValue('isAllDayEvent',0)>
								<cfloop array="#local.arrTimeZoneIDs#" index="local.tzid">
									EXEC dbo.ev_createTime @eventID=@eventID,
										@timeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.tzID#">,
										@startTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startDateTime#">,
										@endTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endDateTime#">,
										@timeID=@timeID OUTPUT;
								</cfloop>
							<cfelse>
								<cfloop collection="#local.structAllEventTimes#" item="local.key">
									EXEC dbo.ev_createTime @eventID=@eventID,
										@timeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.structAllEventTimes[local.key].id#">,
										@startTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.structAllEventTimes[local.key].starttime#">,
										@endTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.structAllEventTimes[local.key].endtime#">,
										@timeID=@timeID OUTPUT;
								</cfloop>
							</cfif>
						</cfif>

						-- asset categories
						DELETE FROM dbo.cms_categorySiteResources 
						WHERE siteResourceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#this.siteResourceID#">;

						<cfif len(trim(arguments.event.getValue('eventAssetCategoryIDs','')))>
							<cfloop list="#arguments.event.getValue('eventAssetCategoryIDs','')#" index="local.insertCatID">
								INSERT INTO dbo.cms_categorySiteResources (CategoryID, siteResourceID)
								VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.insertCatID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#this.siteResourceID#">)
							</cfloop>
						</cfif>

						EXEC dbo.cms_updateContent 
							@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eventContentID',0)#">,
							@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#" null="no">,
							@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('eventContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('eventContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent 
							@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('contactContentID',0)#">,
							@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#" null="no">,
							@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('contactContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('contactContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent 
							@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('locationContentID',0)#">,
							@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#" null="no">,
							@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('locationContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('locationContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent 
							@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('cancelContentID',0)#">,
							@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#" null="no">,
							@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('cancelContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('cancelContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent 
							@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('travelContentID',0)#">,
							@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#" null="no">,
							@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('travelContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('travelContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent 
							@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('informationContentID',0)#">,
							@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#" null="no">,
							@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('informationContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('informationContent','')#" null="no">,
							@memberID=@recordedByMemberID;

						-- Execute dynamic NEWVAL INSERT with standard fields
						INSERT INTO ##tmpContentAuditLogData ([rowCode], [Event Title], [Event Content], [Contact Content Title], [Contact Content],
							[Location Content Title], [Location Content], [Cancel Content Title], [Cancel Content],
							[Travel Content Title], [Travel Content], [Information Content Title], [Information Content],
							[Event Start Time], [Event End Time], [All Day Event], [Categories], [Asset Categories], [Redirect Name], [Redirect URL],
							[Event Sub Title], [Lock Time Zone], [Status], [Report Code], [Internal Notes], [Hidden From Calendar],
							[Email Contact Content], [Email Location Content], [Email Cancel Content], [Email Travel Content], [Remarketing URL],
							[Event Type], [Alt Registration URL], [GL Account])
						SELECT 'NEWVAL',
							ISNULL(ec.contentTitle, ''),
							ISNULL(ec.rawContent, ''),
							ISNULL(cc.contentTitle, ''),
							ISNULL(cc.rawContent, ''),
							ISNULL(lc.contentTitle, ''),
							ISNULL(lc.rawContent, ''),
							ISNULL(cnc.contentTitle, ''),
							ISNULL(cnc.rawContent, ''),
							ISNULL(tc.contentTitle, ''),
							ISNULL(tc.rawContent, ''),
							ISNULL(ic.contentTitle, ''),
							ISNULL(ic.rawContent, ''),
							CASE WHEN e.isAllDayEvent = 1 THEN FORMAT(t.startTime, 'M/d/yyyy') ELSE FORMAT(t.startTime, 'M/d/yyyy - h:mm tt') END,
							CASE WHEN e.isAllDayEvent = 1 THEN FORMAT(t.endTime, 'M/d/yyyy') ELSE FORMAT(t.endTime, 'M/d/yyyy - h:mm tt') END,
							CASE WHEN e.isAllDayEvent = 1 THEN 'Yes' ELSE 'No' END,
							ISNULL(categories.categoryList, ''),
							ISNULL(assetCategories.assetCategoryList, ''),
							ISNULL(redirect.redirectName, ''),
							ISNULL(redirect.redirectURL, ''),
							ISNULL(e.eventSubTitle, ''),
							CASE WHEN e.lockTimeZoneID IS NOT NULL THEN 'Yes' ELSE 'No' END,
							CASE WHEN e.[status] = 'A' THEN 'Active' ELSE 'Inactive' END,
							e.reportCode,
							ISNULL(e.internalNotes, ''),
							CASE WHEN e.hiddenFromCalendar = 1 THEN 'Yes' ELSE 'No' END,
							CASE WHEN e.emailContactContent = 1 THEN 'Yes' ELSE 'No' END,
							CASE WHEN e.emailLocationContent = 1 THEN 'Yes' ELSE 'No' END,
							CASE WHEN e.emailCancelContent = 1 THEN 'Yes' ELSE 'No' END,
							CASE WHEN e.emailTravelContent = 1 THEN 'Yes' ELSE 'No' END,
							ISNULL(e.remarketingURL, ''),
							ISNULL(et.eventType, ''),
							ISNULL(e.altRegistrationURL, ''),
							ISNULL(gl.accountName, '')
						FROM dbo.ev_events e
						CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) ec
						CROSS APPLY dbo.fn_getContent(e.contactContentID, @defaultLanguageID) cc
						CROSS APPLY dbo.fn_getContent(e.locationContentID, @defaultLanguageID) lc
						CROSS APPLY dbo.fn_getContent(e.cancellationPolicyContentID, @defaultLanguageID) cnc
						CROSS APPLY dbo.fn_getContent(e.travelContentID, @defaultLanguageID) tc
						CROSS APPLY dbo.fn_getContent(e.informationContentID, @defaultLanguageID) ic
						LEFT JOIN dbo.ev_times t ON t.eventID = e.eventID AND t.timeZoneID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eventTimeZoneID',0)#">
						OUTER APPLY (
							SELECT STUFF((SELECT ', ' + c.category
								FROM dbo.ev_eventCategories ec2
								INNER JOIN dbo.ev_categories c ON c.categoryID = ec2.categoryID
								WHERE ec2.eventID = e.eventID
								ORDER BY ec2.categoryOrder, c.category
								FOR XML PATH('')), 1, 2, '') AS categoryList
						) categories
						OUTER APPLY (
							SELECT STUFF((SELECT ', ' + cat.categoryName
								FROM dbo.cms_categorySiteResources csr
								INNER JOIN dbo.cms_categories cat ON cat.categoryID = csr.categoryID
								WHERE csr.siteResourceID = e.siteResourceID
								ORDER BY cat.categoryName
								FOR XML PATH('')), 1, 2, '') AS assetCategoryList
						) assetCategories
						LEFT JOIN dbo.siteRedirects redirect ON redirect.redirectID = e.redirectID
						LEFT JOIN dbo.ev_eventTypes et ON et.eventTypeID = e.eventTypeID
						LEFT JOIN dbo.tr_GLAccounts gl ON gl.GLAccountID = e.GLAccountID
						WHERE e.eventID = @eventID;

						-- Add custom field NEWVAL data using reusable stored procedure
						EXEC dbo.cf_populateAuditLogWithCustomFields
							@operation = 'NEWVAL',
							@auditLogTableName = '##tmpContentAuditLogData',
							@usageID = @crossEventFieldUsageID,
							@controllingSiteResourceID = @eventAdminSiteResourceID,
							@itemID = @siteResourceID,
							@itemType = 'CrossEvent';

						-- Generate audit log message for content changes
						EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpContentAuditLogData', @msg=@msgjson OUTPUT;

						IF ISNULL(@msgjson, '') <> '' BEGIN
							DECLARE @crlf varchar(2) = CHAR(13) + CHAR(10);
							DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
							SET @msgjson = STRING_ESCAPE('Event [' + @eventTitle + '] has been updated.', 'json') + @crlf
								+ 'The following changes have been made:' + @crlf + @msgjson;

							EXEC dbo.ev_insertAuditLog @orgID = @orgID, @siteID = @siteID, @areaCode = 'EVENT', @msgjson = @msgjson, @evKeyMapJSON = @evKeyMapJSON, @isImport = 0, @enteredByMemberID = @recordedByMemberID;
						END

						-- Clean up temp audit table
						IF OBJECT_ID('tempdb..##tmpContentAuditLogData') IS NOT NULL
							DROP TABLE ##tmpContentAuditLogData;

						-- Final cleanup of custom fields temp tables (in case they weren't cleaned up earlier)
						IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') IS NOT NULL
							DROP TABLE ##tmp_CF_ItemIDs;
						IF OBJECT_ID('tempdb..##tmp_CF_FieldData') IS NOT NULL
							DROP TABLE ##tmp_CF_FieldData;
					COMMIT TRAN;

					<cfif local.isRecurringEvent AND arguments.event.getValue('convertToRecurringEvent',0) EQ 1>
						DECLARE @recurringEventsImportResult xml;

						EXEC dbo.ev_queueCreatingRecurringEvents @siteID=@siteID, @createdFromEventID=@eventID, 
							@afID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('recurrenceAFID')#">,
							@endDate=<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('recurrenceEndsOn')#">,
							@recordedByMemberID=@recordedByMemberID, @recurringEventsImportResult=@recurringEventsImportResult OUTPUT;

						SELECT 1 AS success, @recurringEventsImportResult AS recurringEventsImportResult, 
							@recurringEventsImportResult.value('count(/import/errors/error)','int') AS recurringEventsImportErrCount;
					<cfelseif local.isRecurringEvent AND arguments.event.getValue('updateUpcomingRecurringEvents',0) EQ 1>
						DECLARE @recurringEventsImportResult xml;

						EXEC dbo.ev_updateUpcomingRecurringEvents @siteID=@siteID, @copyFromEventID=@eventID, @recordedByMemberID=@recordedByMemberID, 
							@recurringEventsImportResult=@recurringEventsImportResult OUTPUT;

						SELECT 1 AS success, @recurringEventsImportResult AS recurringEventsImportResult, 
							@recurringEventsImportResult.value('count(/import/errors/error)','int') AS recurringEventsImportErrCount;
					<cfelse>
						SELECT 1 AS success;
					</cfif>

				END TRY
				BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfif arguments.event.valueExists('newRedirectName') and arguments.event.getTrimValue('currentRedirectName') NEQ arguments.event.getTrimValue('newRedirectName')>
				<cfif len(arguments.event.getTrimValue('newRedirectName'))>
					<cfset this.objAdminEvent.addRedirect(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=arguments.event.getValue('eID'), 
						redirectName=arguments.event.getTrimValue('newRedirectName'), redirectURL="#arguments.event.getValue('mc_siteInfo.scheme')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?#local.baseTestLink#&evAction=showDetail&eid=#arguments.event.getValue('eid')#", oldRedirectID=val(arguments.event.getValue('redirectID',0)))>
				<cfelseif val(arguments.event.getValue('redirectID')) gt 0>
					<cfset CreateObject("component","model.admin.alias.alias").deleteAlias(redirectIDList=arguments.event.getValue('redirectID'),siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
				</cfif>
			</cfif>

			<cfset local.processConditions = false>
			<cfset local.updateSearchText = false>

			<cfif compare(arguments.event.getValue('oldEventContentTitle',''),arguments.event.getTrimValue('eventContentTitle'))
					OR ListSort(arguments.event.getValue('oldCategoryIDList',0),'numeric','asc') NEQ ListSort(arguments.event.getValue('categoryID'),'numeric','asc')>
				<cfset local.processConditions = true>
				<cfset local.updateSearchText = true>
			<cfelseif arguments.event.getValue('oldEventStartTime','') NEQ arguments.event.getTrimValue('eventStartTime')
					OR compare(arguments.event.getValue('oldReportCode',''),arguments.event.getTrimValue('reportCode'))>
				<cfset local.processConditions = true>
			</cfif>

			<cfif NOT local.updateSearchText>
				<cfset local.arrContentCompare = [ { "key":"eventContentID", "formFieldName":"eventContent" }, { "key":"locationContentID", "formFieldName":"locationContent" }]>
				<cfloop array="#local.arrContentCompare#" index="local.thisStr">
					<cfset local.oldEventContent = application.objCMS.getStaticContent(contentID=arguments.event.getValue('#local.thisStr.key#',0), languageID=this.languageID, skipMerge=1).rawContent>
					<cfif compare(local.oldEventContent, arguments.event.getTrimValue('#local.thisStr.formFieldName#',''))>
						<cfset local.updateSearchText = true>
						<cfbreak>
					</cfif>
				</cfloop>
			</cfif>

			<cfif local.updateSearchText>
				<cfset this.objAdminEvent.queueUpdateSearchText(siteID=arguments.event.getValue('mc_siteInfo.siteID'), eventID=arguments.event.getValue('eID'))>
			</cfif>

			<cfif local.processConditions>
				<cfquery name="local.qryProcessConditions" datasource="#application.dsn.membercentral.dsn#">
					SET NOCOUNT ON;

					DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					select c.orgID, null, c.conditionID
					from dbo.ams_virtualGroupConditions as c 
					where c.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
					and c.fieldCode = 'ev_entry'
					and exists (select 1 
								from dbo.ev_registrants as er 
								inner join dbo.ev_registration as r on r.registrationid = er.registrationid 
									and r.siteID = @siteID
								where r.eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#"> 
								and r.[status] = 'A'
								and er.[status] = 'A');

					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
				</cfquery>
			</cfif>

			<cfif local.isRecurringEvent 
					AND (arguments.event.getValue('convertToRecurringEvent',0) EQ 1 OR arguments.event.getValue('updateUpcomingRecurringEvents',0) EQ 1)
					AND val(local.qryUpdateEvent.recurringEventsImportErrCount)>
				<cfset application.mcCacheManager.sessionSetValue(keyname="recurringEvImpErr#arguments.event.getValue('eID')#", value=XMLSearch(local.qryUpdateEvent.recurringEventsImportResult,"/import/errors/error"))>
			</cfif>

		<cfelse>
			<cfset local.qryAssetCategories = this.objCategories.getIndentedCategoriesForTree(val(this.objCategories.getCategoryTrees(this.eventAdminSiteResourceID).categoryTreeID)).qryCategories>
			<cfset local.isRecurringEvent = val(arguments.event.getValue('recurrenceAFID',0)) AND len(arguments.event.getValue('recurrenceEndsOn',''))>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryNewEvent">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					DECLARE @siteID int, @eventID int, @timeID int, @siteResourceId int, @eventContentID int, @locationContentID int, 
						@travelContentID int, @contactContentID int, @cancelContentID int, @informationContentID int,
						@recordedByMemberID int, @languageID int;

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">;
					SET @languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#">;

					BEGIN TRAN;

						EXEC dbo.ev_createEvent @siteID=@siteID,
							@calendarID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('cID')#">,
							@eventTypeID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eventTypeID')#">,
							@enteredByMemberID=@recordedByMemberID,
							@eventSubTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('eventSubTitle')#">,
							<cfif arguments.event.getValue('lockTimeZoneID',0)>
								@lockTimeZoneID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('lockTimeZoneID')#">,
							<cfelse>
								@lockTimeZoneID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" null="yes">,
							</cfif>
							@isAllDayEvent=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('isAllDayEvent',0)#">,
							@altRegistrationURL=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" null="yes">,
							@status=<cfqueryparam cfsqltype="cf_sql_char" value="#arguments.event.getValue('status')#">,
							@reportCode=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('reportCode','')#">,
							@hiddenFromCalendar=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('hiddenFromCalendar',0)#">,
							@emailContactContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailContactContent',0)#">,
							@emailLocationContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailLocationContent',0)#">,
							@emailCancelContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailCancelContent',0)#">,
							@emailTravelContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailTravelContent',0)#">,
							<cfif val(arguments.event.getValue('peID',0))>
								@parentEventID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('peID')#">,
							<cfelse>
								@parentEventID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" null="true">,
							</cfif>
							@eventID=@eventID OUTPUT;

						EXEC dbo.ev_saveEventCategories @eventID=@eventID, @recordedByMemberID=@recordedByMemberID,
							@categoryIDList=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="0#arguments.event.getValue('categoryID')#">;

						EXEC dbo.ev_refreshCalendarEventsCache @siteID=@siteID;

						EXEC dbo.ev_refreshCalendarEventsCategoryIDList @siteID=@siteID, @eventID=@eventID;

						SELECT @eventID=eventID, @siteResourceId=siteResourceId, @eventContentID=eventContentID, @locationContentID=locationContentID, @travelContentID=travelContentID, 
							@contactContentID=contactContentID, @cancelContentID=cancellationPolicyContentID, @informationContentID=informationContentID
						FROM dbo.ev_events 
						WHERE eventID = @eventID;

						-- cross-event custom fields
						<cfif arrayLen(local.arrCrossEventCustomFields)>
							#this.objAdminEvent.insertEventCustomFields(arrCrossEventCustomFields=local.arrCrossEventCustomFields)#
						</cfif>

						<cfif arguments.event.getValue('isAllDayEvent',0)>
							<cfloop array="#local.arrTimeZoneIDs#" index="local.tzid">
								EXEC dbo.ev_createTime @eventID=@eventID,
									@timeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.tzID#">,
									@startTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startDateTime#">,
									@endTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endDateTime#">,
									@timeID=@timeID OUTPUT;
							</cfloop>
						<cfelse>
							<cfloop collection="#local.structAllEventTimes#" item="local.key">
								EXEC dbo.ev_createTime @eventID=@eventID,
									@timeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.structAllEventTimes[local.key].id#">,
									@startTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.structAllEventTimes[local.key].starttime#">,
									@endTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.structAllEventTimes[local.key].endtime#">,
									@timeID=@timeID OUTPUT;
							</cfloop>
						</cfif>

						<cfif local.qryAssetCategories.recordcount and listlen(arguments.event.getValue('eventAssetCategoryIDs',''))>
							<cfloop list="#arguments.event.getValue('eventAssetCategoryIDs','')#" index="local.insertCatID">
								INSERT INTO dbo.cms_categorySiteResources (CategoryID, siteResourceID)
								VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.insertCatID#">, @siteResourceId);
							</cfloop>
						</cfif>

						EXEC dbo.cms_updateContent @contentID=@eventContentID, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('eventContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc='',
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('eventContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent @contentID=@contactContentID, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('contactContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc='',
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('contactContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent @contentID=@locationContentID, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('locationContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc='',
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('locationContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent @contentID=@cancelContentID, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('cancelContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc='',
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('cancelContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent @contentID=@travelContentID, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('travelContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc='',
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('travelContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.cms_updateContent @contentID=@informationContentID, @languageID=@languageID, @isHTML=1,
							@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('informationContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
							@contentDesc='',
							@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('informationContent')#" null="no">,
							@memberID=@recordedByMemberID;

						EXEC dbo.ev_queueEventSearchText @siteID=@siteID, @eventID=@eventID;
					COMMIT TRAN;

					<cfif local.isRecurringEvent>
						DECLARE @errCount int, @recurringEventsImportResult xml;

						EXEC dbo.ev_queueCreatingRecurringEvents @siteID=@siteID, @createdFromEventID=@eventID, 
							@afID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('recurrenceAFID')#">,
							@endDate=<cfqueryparam cfsqltype="CF_SQL_DATE" value="#arguments.event.getValue('recurrenceEndsOn')#">,
							@recordedByMemberID=@recordedByMemberID, @recurringEventsImportResult=@recurringEventsImportResult OUTPUT;

						SELECT @errCount = @recurringEventsImportResult.value('count(/import/errors/error)','int');
					</cfif>

					SELECT @eventID AS eventID<cfif local.isRecurringEvent>, @recurringEventsImportResult AS recurringEventsImportResult, @errCount AS errCount</cfif>;
				
				END TRY
				BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfset arguments.event.setValue('eID',local.qryNewEvent.eventid)>
			<cfset arguments.event.setValue('isNewEvent',1)>

			<cfif local.isRecurringEvent AND val(local.qryNewEvent.errCount)>
				<cfset application.mcCacheManager.sessionSetValue(keyname="recurringEvImpErr#local.qryNewEvent.eventid#", value=XMLSearch(local.qryNewEvent.recurringEventsImportResult,"/import/errors/error"))>
			</cfif>

			<cfif listFindNoCase("online,rsvp,alturl", arguments.event.getValue('eventRegistrationOption',''))>
				<cfswitch expression="#arguments.event.getValue('eventRegistrationOption')#">
					<cfcase value="online">
						<cfset arguments.event.setValue('registrationTypeID','1')>
						<cfset insertRegistration(event=arguments.event)>
					</cfcase>
					<cfcase value="rsvp">
						<cfset arguments.event.setValue('registrationTypeID','2')>
						<cfset insertRegistration(event=arguments.event)>
					</cfcase>
					<cfcase value="alturl">
						<cflocation url="#this.link.editEvent#&eID=#local.qryNewEvent.eventid#&tab=registration&alturl=1" addtoken="no">
					</cfcase>
				</cfswitch>
			</cfif>
		</cfif>
		
		<cfset local.tmpEventParams = "">
		<cfif val(arguments.event.getValue('peID',0))>
			<cfset local.tmpEventParams = "&peID=" & arguments.event.getValue('peID')>
		</cfif>
		<cfif local.isRecurringEvent AND application.mcCacheManager.sessionValueExists(keyname="recurringEvImpErr#arguments.event.getValue('eID')#")>
			<cfset local.tmpEventParams = "&recurringevimperr=1">
		</cfif>
		
		<cflocation url="#this.link.editEvent#&eID=#arguments.event.getValue('eID')##local.tmpEventParams#" addtoken="no">			
	</cffunction>
	
	<cffunction name="saveLinkEvent" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.calendarID = arguments.event.getValue('cid',0)>
		<cfset local.parentEventID = arguments.event.getValue('pid',0)>
		<cfset local.eidlist = arguments.event.getValue('eidlist','')>

		<cfset arguments.event.setValue('registrationTypeID','1')>
		<cfset arguments.event.setValue('cid', local.calendarID)>
		<cfset arguments.event.setValue('redirectPage', "0")>
		
		<cfif arguments.event.getValue("taskType","") eq "link">			
			<cfif listLen(local.eidlist)>

				<cfset local.objEvent = CreateObject("component","model.events.events")>
				<cfset local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
				<cfif local.strEvent.qryEventRegMeta.recordCount>
					<cfset this.objAdminEvent.deleteRegistration(local.strEvent.qryEventRegMeta.registrationID)>
				</cfif>
				<cfif local.strEvent.qryEventMeta.altRegistrationURL NEQ ''>
					<cfset this.objAdminEvent.deleteAltURL(arguments.event.getValue('eID'))>
				</cfif>

				<cfset arguments.event.paramValue('expirationContent','')>
				<cfset arguments.event.paramValue('replyToEmail',session.cfcuser.memberdata.email)>
				<cfset arguments.event.paramValue('notifyEmail','')>
				<cfset arguments.event.paramValue('redirectPage','1')>

				<cfset arguments.event.setValue('regStartTime',CreateDateTime(year(now()),month(now()),day(now()),Hour(now()),0,0))>
				// set start to next hour, end to end of event
				<cfset arguments.event.setValue('regStartTime',DateAdd('h',1,arguments.event.getValue('regStartTime')))>
				<cfif local.strEvent.qryEventMeta.isAllDayEvent EQ 1>
					<cfset arguments.event.setValue('regEndTime',CreateDateTime(year(local.strEvent.qryEventTimes_selected.endTime),month(local.strEvent.qryEventTimes_selected.endTime),day(local.strEvent.qryEventTimes_selected.endTime),17,0,0))>
				<cfelse>
					<cfset arguments.event.setValue('regEndTime',local.strEvent.qryEventTimes_selected.endTime)>
				</cfif>

				// if reg end time is earlier than start time, just use start time + 1 hr.
				<cfif arguments.event.getValue('regEndTime') lt arguments.event.getValue('regStartTime')>
					<cfset arguments.event.setValue('regEndTime',DateAdd('h',1,arguments.event.getValue('regStartTime')))>
				</cfif>

				<cfquery datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						DECLARE @hasRegistration int, @registrationID int, @eventID int, @registrationTypeID int, @startDate datetime, @endDate datetime, 
							@registrantCap int, @replyToEmail varchar(200), @notifyEmail varchar(200), @isPriceBasedOnActual bit, @bulkCountByRate bit, 
							@languageID int, @isHTML bit, @contentTitle varchar(200), @contentDesc varchar(400), @rawcontent varchar(max), @memberID int, 
							@expirationContentID int, @siteID int;
						
						SET @registrationTypeID = <cfqueryparam value="#arguments.event.getValue('registrationTypeID')#" cfsqltype="CF_SQL_INTEGER">;
						SET @startDate = <cfqueryparam value="#arguments.event.getValue('regStartTime')#" cfsqltype="CF_SQL_TIMESTAMP">;
						SET @endDate = <cfqueryparam value="#arguments.event.getValue('regEndTime')#" cfsqltype="CF_SQL_TIMESTAMP">;						
						SET @registrantCap = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="" null="yes">;
						SET @replyToEmail = <cfqueryparam value="#arguments.event.getValue('replyToEmail')#" cfsqltype="CF_SQL_VARCHAR">;
						SET @notifyEmail = <cfqueryparam value="#arguments.event.getValue('notifyEmail')#" cfsqltype="CF_SQL_VARCHAR">;
						SET @isPriceBasedOnActual = <cfqueryparam cfsqltype="CF_SQL_BIT" value="1">;
						SET @bulkCountByRate = <cfqueryparam cfsqltype="CF_SQL_BIT" value="0">;
						SET @languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#">; 
						SET @isHTML = <cfqueryparam cfsqltype="CF_SQL_BIT" value="1">;
						SET @contentTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="">;
						SET @contentDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="">;
						SET @rawcontent = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('expirationContent')#">;
						SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#">;
						SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;

						BEGIN TRAN;
							<cfloop list="#local.eidlist#" index="local.thisItem">								
								INSERT INTO ev_subEvents (parentEventID, eventID)
								VALUES (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.parentEventID#">, <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisItem#">);

								SET @hasRegistration = 0;

								SELECT @hasRegistration = case when er.registrationID is NULL then 0 else 1 end
								FROM dbo.ev_events AS e
								LEFT OUTER JOIN dbo.ev_registration AS er 
									INNER JOIN dbo.ev_registrationTypes AS ert ON er.registrationTypeID = ert.registrationTypeID 
									ON er.eventID = e.eventID 
									AND er.siteID = @siteID 
									AND er.status = 'A'
								WHERE e.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.thisItem#">;

								IF ISNULL(@hasRegistration,0) !=1  BEGIN		
									SET @eventID = <cfqueryparam value="#local.thisItem#" cfsqltype="CF_SQL_INTEGER">;

									EXEC dbo.ev_createRegistration @eventID=@eventID, @registrationTypeID=@registrationTypeID, @startDate=@startDate,
										@endDate=@endDate, @registrantCap=@registrantCap, @replyToEmail=@replyToEmail, @notifyEmail=@notifyEmail,
										@isPriceBasedOnActual=@isPriceBasedOnActual, @bulkCountByRate=@bulkCountByRate, @registrationID=@registrationID OUTPUT;

									SELECT @expirationContentID = expirationContentID
									FROM dbo.ev_registration
									WHERE registrationID = @registrationID;

									EXEC dbo.cms_updateContent @contentID=@expirationContentID, @languageID=@languageID, @isHTML=@isHTML, 
										@contentTitle=@contentTitle, @contentDesc=@contentDesc, @rawcontent=@rawcontent, @memberID=@memberID;
								END		
							</cfloop>
						COMMIT TRAN;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>
		<cfelseif arguments.event.getValue("taskType","") eq "copyrates" and local.eidlist gt 0 and local.eidlist neq local.parentEventID>
			<cfset this.objAdminEvent.copyRatesfromEvent(siteID=arguments.event.getValue('mc_siteinfo.siteID'), parentEventID=local.parentEventID, copyFromEventID=local.eidlist)>
		</cfif>
			
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<cfif arguments.event.getValue("taskType","") eq "copyrates">
					top.reloadRatesTable();
					top.MCModalUtils.hideModal();
				<cfelse>
					top.reloadLinkEvents();
				</cfif>
			 </script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>		
	
	<cffunction name="previewConfirmationEmail" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.objEvent = CreateObject("component","model.events.events")>
		<cfset local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>

		<cfset local.defaultCurrencyType = "">
		<cfif arguments.event.getValue('mc_siteInfo.showCurrencyType') is 1>
			<cfset local.defaultCurrencyType = " #arguments.event.getValue('mc_siteInfo.defaultCurrencyType')#">
		</cfif>
		
		<cfset local.eventtime = local.objEvent.generateEventDateString(mode='eventConfirmation',
				startTime=local.strEvent.qryEventTimes_selected.startTime, endTime=local.strEvent.qryEventTimes_selected.endTime,
				isAllDayEvent=local.strEvent.qryEventMeta.isAllDayEvent, showTimeZone=true,
				timeZoneAbbr=local.strEvent.qryEventTimes_selected.timeZoneAbbr)>

		<cfset local.tdStyle = "font:normal 12px Verdana,Helvetica,Arial,sans-serif;color:##333;">

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_previewEmailConfirmation.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- CREDIT --->
	<cffunction name="manageAttendanceCredit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		if (val(arguments.event.getValue('eID',0)) lte 0 and NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent'))
			application.objCommon.redirect('#this.link.message#&message=1');
		</cfscript>

		<cfset local.qryRegistrants = this.objAdminEvent.getRegistrantsFromFilters(event=arguments.event, mode="regTabCreditGrid")>
		<cfset local.strEvent = CreateObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
		<cfset local.qryCreditsGrid = CreateObject("component","model.admin.credit.credit").getCreditOfferedGrid('Events',arguments.event.getValue('eID',0))>
		<cfset local.importCreditsPromptLink = "#this.link.importCreditsPrompt#&mode=direct&eID=#arguments.event.getValue('eID',0)#">
		<cfset local.importCreditsLink = "#this.link.importCreditsComplete#&mode=stream&eID=#arguments.event.getValue('eID',0)#">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_registrantCredits.cfm">
		</cfsavecontent>	
			
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageSubEventAttendanceCredit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		// security 
		if (val(arguments.event.getValue('eID',0)) lte 0 and NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent'))
			application.objCommon.redirect('#this.link.message#&message=1');
		</cfscript>
		
		<cfquery name="local.qrySubEvent" datasource="#application.dsn.membercentral.dsn#">
			select * from ev_subEvents where eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eID')#">
		</cfquery>
		
		<cfset local.isMaster = local.qrySubEvent.recordCount ? 0 : 1>
		
		<cfquery name="local.qryRegistrants" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;
			
			declare @RTID int, @regMemberID int, @registrantID int, @eventID int, @memberName varchar (100), @siteID int, @languageID int;
			
			set @RTID = dbo.fn_getResourceTypeID('Community');
			set @registrantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('_rid')#">;
			set @eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eID')#">;
			set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
			set @languageID = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.defaultLanguageID')#" cfsqltype="CF_SQL_INTEGER">;
			
			select @memberName = m2.lastName + ', ' + m2.firstName, @regMemberID = m2.memberID
			from  dbo.ev_registrants as er
			inner join dbo.ams_members as m on m.memberID = er.memberID
				and er.registrantID = @registrantID
			inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID;
			
			-- get events on site
			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
				DROP TABLE ##tmpEventsOnSite;
			CREATE TABLE ##tmpEventsOnSite (calendarID int, eventID int, [status] char(1), isPastEvent bit,
				startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
				displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
				displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, altRegistrationURL varchar(300),
				eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200), 
				categoryIDList varchar(max));
			EXEC dbo.ev_getEventsOnSite @siteID=@siteID, @startDate=null, @endDate=null, @categoryIDList='';

			SELECT e.eventid, e.status, tmp.eventTitle, tmp.eventSubTitle, tmp.calendarID, 
				CASE 
				WHEN e.altRegistrationURL IS NULL AND ert.registrationType  = '' THEN ''
				WHEN e.altRegistrationURL IS NOT NULL THEN 'Alt'
				ELSE ert.registrationType 
				END as regType,
				ai.applicationInstanceName + 
					case 
					WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
					ELSE ''
					END as calendarName,
				tmp.startTime, tmp.endTime,
				hasRegistration = case when er.registrationID is null then 0 else 1 end,
				er.registrationTypeID,
				numRegistrants = case 
					when er.registrationID is null then 0
					WHEN er.registrationTypeID = 1 then (select count(*) from dbo.ev_registrants where registrationid = er.registrationID and status = 'A') 
					ELSE (select count(*) from dbo.ev_rsvp where registrationid = er.registrationID) 
					end,
				isSubEvent = 0,
				isRegistered = (case when reg.registrantID is null then 0 else 1 end),
				reg.registrantID,
				reg.attended,
				parentEventID = 0,
				@regMemberID as regMemberID,
				@memberName as memberName,
				cast(isnull((
						select 
							credit.offeringTypeID, 
							isnull(ast.ovTypeName,cat.typeName) as creditType, 
							isnull(ca.authorityName,'') as authority, 
							credit.creditAwarded, 
							credit.creditValueAwarded
						from 
							dbo.crd_requests as credit
							inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = credit.offeringTypeID
							inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
							inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
							inner join dbo.crd_authorities as ca on ca.authorityId = cat.authorityID
						where 
							credit.registrantID = reg.registrantID
				for XML AUTO, ROOT('credits')
				),'<credits/>') as xml) as creditsXML,
				cast(isnull((
				select
					ect.offeringTypeID,
					ect.creditValue,
					isnull(ast.ovTypeName,cat.typeName) as creditType,
					isnull(ca.authorityName,'') as authority
				from
					dbo.crd_offeringTypes as ect 
					inner join dbo.crd_offerings as eo on 
						eo.offeringID = ect.offeringID 
						and eo.eventID = e.eventID
					inner join dbo.crd_authoritySponsorTypes as ast on 
						ast.ASTID = ect.ASTID
					inner join dbo.crd_authorityTypes as cat on 
						cat.typeID = ast.typeID
					inner join dbo.crd_authorities as ca on 
						ca.authorityId = cat.authorityID
					for XML AUTO, ROOT('credits')
				),'') as xml) as eventCreditsXML
			FROM dbo.ev_events as e
			INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
			LEFT OUTER JOIN dbo.ev_registration as er 
				INNER JOIN dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID
				inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID
					and reg.registrantID = @registrantID
					and reg.status = 'A'
				on er.siteID = @siteID and er.eventID = e.eventID and er.status = 'A'
			INNER JOIN dbo.ev_calendars as c on c.siteID = @siteID and c.calendarid = tmp.calendarID
			inner join dbo.cms_applicationInstances as ai on c.applicationInstanceID = ai.applicationInstanceID 
				and ai.siteID = @siteID
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and ai.siteResourceID = sr.siteResourceID
			inner join dbo.cms_siteResources as parentResource on parentResource.siteID = @siteID 
				and parentResource.siteResourceID = sr.parentSiteResourceID
			left outer join dbo.cms_siteResources as grandparentResource
				inner join dbo.cms_applicationInstances as CommunityInstances on CommunityInstances.siteID = @siteID
					and communityInstances.siteResourceID = grandParentResource.siteResourceID
				on grandparentResource.siteID = @siteID
				and grandparentResource.siteResourceID = parentResource.parentSiteResourceID
				and grandparentResource.resourceTypeID = @RTID
			WHERE e.eventID = @eventID
			and e.status = 'A'

			<cfif local.isMaster>
					union all
				SELECT e.eventid, e.status, tmp.eventTitle, tmp.eventSubTitle, tmp.calendarID, 
					CASE 
					WHEN e.altRegistrationURL IS NULL AND ert.registrationType  = '' THEN ''
					WHEN e.altRegistrationURL IS NOT NULL THEN 'Alt'
					ELSE ert.registrationType 
					END as regType,
					ai.applicationInstanceName + 
						case 
						WHEN communityInstances.applicationInstanceName is not null THEN ' (' + communityInstances.applicationInstanceName + ')'
						ELSE ''
						END as calendarName,
					tmp.startTime, tmp.endTime,
					hasRegistration = case when er.registrationID is null then 0 else 1 end,
					er.registrationTypeID,
					numRegistrants = case 
						when er.registrationID is null then 0
						WHEN er.registrationTypeID = 1 then (select count(*) from dbo.ev_registrants where registrationid = er.registrationID and status = 'A') 
						ELSE (select count(*) from dbo.ev_rsvp where registrationid = er.registrationID) 
						end,
					isSubEvent = 1,
					isRegistered = (case when reg.registrantID is null then 0 else 1 end),
					reg.registrantID,
					attended =  isNull(reg.attended,0),
					@eventID as parentEventID,
					@regMemberID as regMemberID,
					@memberName as memberName,
					cast(isnull((
							select 
								credit.offeringTypeID, 
								isnull(ast.ovTypeName,cat.typeName) as creditType, 
								isnull(ca.authorityName,'') as authority, 
								credit.creditAwarded, 
								credit.creditValueAwarded
							from 
								dbo.crd_requests as credit
								inner join dbo.crd_offeringTypes as ect on ect.offeringTypeID = credit.offeringTypeID
								inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
								inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
								inner join dbo.crd_authorities as ca on ca.authorityId = cat.authorityID
							where 
								credit.registrantID = reg.registrantID
					for XML AUTO, ROOT('credits')
					),'<credits/>') as xml) as creditsXML,
					cast(isnull((
					select
						ect.offeringTypeID,
						ect.creditValue,
						isnull(ast.ovTypeName,cat.typeName) as creditType,
						isnull(ca.authorityName,'') as authority
					from
						dbo.crd_offeringTypes as ect 
						inner join dbo.crd_offerings as eo on 
							eo.offeringID = ect.offeringID 
							and eo.eventID = e.eventID
						inner join dbo.crd_authoritySponsorTypes as ast on 
							ast.ASTID = ect.ASTID
						inner join dbo.crd_authorityTypes as cat on 
							cat.typeID = ast.typeID
						inner join dbo.crd_authorities as ca on 
							ca.authorityId = cat.authorityID
						for XML AUTO, ROOT('credits')
					),'') as xml) as eventCreditsXML
				FROM dbo.ev_events as e
				INNER JOIN ##tmpEventsOnSite as tmp on tmp.eventID = e.eventID
				LEFT OUTER JOIN dbo.ev_registration as er 
					inner join dbo.ev_registrationTypes as ert on er.registrationTypeID = ert.registrationTypeID
					inner join dbo.ev_registrants as reg on reg.registrationID = er.registrationID
						and reg.memberID = @regMemberID	
						and reg.status = 'A'
					on er.siteID = @siteID and er.eventID = e.eventID and er.status = 'A'
				INNER JOIN dbo.ev_calendars as c on c.siteID = @siteID and c.calendarid = tmp.calendarID
				inner join dbo.cms_applicationInstances as ai on c.applicationInstanceID = ai.applicationInstanceID 
					and ai.siteID = @siteID
				inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and ai.siteResourceID = sr.siteResourceID
				inner join dbo.cms_siteResources as parentResource on parentResource.siteID = @siteID
					and parentResource.siteResourceID = sr.parentSiteResourceID
				left outer join dbo.cms_siteResources as grandparentResource
					inner join dbo.cms_applicationInstances as CommunityInstances on CommunityInstances.siteID = @siteID 
						and communityInstances.siteResourceID = grandParentResource.siteResourceID
					on grandparentResource.siteID = @siteID 
					and grandparentResource.siteResourceID = parentResource.parentSiteResourceID
					and grandparentResource.resourceTypeID = @RTID
				inner join dbo.ev_subEvents as subEvent on subEvent.eventID = e.eventID
					and subEvent.parentEventID = @eventID
				WHERE e.siteID = @siteID
				and e.status = 'A'
			</cfif>;
			
			IF OBJECT_ID('tempdb..##tmpEventsOnSite') IS NOT NULL 
				DROP TABLE ##tmpEventsOnSite;
		</cfquery>
		
		<cfquery name="local.qryNonBlankRegistrants" dbtype="query">
			select cast(eventID as varchar) + '|' + cast(registrantID as varchar) + '|' + cast(regMemberID as varchar) + '|' + cast(attended as varchar) as eventRegAttendIDs 
			from [local].qryRegistrants 
			where registrantID is not NULL
		</cfquery>
		
		<cfset local.strEvent = CreateObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
		<cfset local.qryCreditsGrid = CreateObject("component","model.admin.credit.credit").getCreditOfferedGrid('Events',arguments.event.getValue('eID',0))>
		<cfset local.regOfferingTypeIDList = "">
		<cfset local.newRegOfferingTypeIDList = "">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_registrantCreditSubEvent.cfm">
		</cfsavecontent>		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveAttendanceCredit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfquery name="local.qryRegistrants" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;

			SELECT r.registrantID, r.memberID
			FROM dbo.ev_registrants as r
			INNER JOIN dbo.ev_registration as rn ON rn.registrationID = r.registrationID AND rn.status = 'A'
				AND rn.siteID = @siteID
			WHERE rn.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eID')#">
			<cfif arguments.event.valueExists('_rid')>
				AND r.registrantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('_rid')#">
			<!--- this cant be a cfqueryparam because it could hit the 2100 parameter limit in sql --->
			<cfelseif arguments.event.valueExists('registrantListFiltered')>
				AND r.registrantID IN (0#arguments.event.getValue('registrantListFiltered')#)
			</cfif>
			AND r.status = 'A';
		</cfquery>
		<cfif local.qryRegistrants.recordCount>
			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#" result="local.sqlupdate">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @siteID int, @requestID int, @creditValue decimal(6,2), @offeringTypeID int, @lastDateToComplete smalldatetime, @eventID int;

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;

					-- get event end date for the lastDateToComplete credits
					SELECT @lastDateToComplete = max(t.endTime), @eventID = max(reg.eventID)
					from dbo.ev_registrants as r
					inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID and reg.siteID = @siteID
					inner join dbo.ev_times as t on t.eventid = reg.eventid
					where r.registrantID = #local.qryRegistrants.registrantID#;

					BEGIN TRAN;

					<cfloop query="local.qryRegistrants">
						<!--- attended --->
						<cfset local.thisRegistrantAttended = ListFind(arguments.event.getValue('attendedList',''), local.qryRegistrants.registrantID)>
						
						update dbo.ev_registrants
						set attended = #min(local.thisRegistrantAttended, 1)#
						where registrantID = #local.qryRegistrants.registrantID#;
						
						<!--- if not attended, remove any awarded credit --->
						<cfif local.thisRegistrantAttended IS 0>
							delete from dbo.crd_requests
							where registrantID = #local.qryRegistrants.registrantID#;
						<cfelse>
							<cfswitch expression="#arguments.event.getValue('SELQUICKACT','')#">
								<cfcase value="removeCredit">
									SET @offeringTypeID = #int(val(arguments.event.getValue('sel_remove_offeringTypeID',0)))#;
									SET @requestID = null;
									SELECT @requestID = requestID from dbo.crd_requests where registrantID = #local.qryRegistrants.registrantID# and offeringTypeID = @offeringTypeID;
										
									if exists (select requestID from dbo.crd_requests where requestID = @requestID)
										delete from dbo.crd_requests
										where requestID = @requestID;
								</cfcase>
								<cfcase value="awardCredit">
									<cfloop list="#arguments.event.getValue('sel_standard_offeringTypeID',0)#" index="local.thisofferingTypeID">
										SET @requestID = null;
										SET @offeringTypeID = #int(val(local.thisofferingTypeID))#;
										SELECT @creditValue = creditValue from dbo.crd_offeringTypes where offeringTypeID = @offeringTypeID;
			
										EXEC dbo.crd_addRequest @siteID=@siteID, @applicationType='Events', @itemID=#local.qryRegistrants.registrantID#, @offeringTypeID=@offeringTypeID,
											@idnumber='', @lastDateToComplete=@lastDateToComplete, @creditAwarded=1, @creditValueAwarded=@creditValue, @requestID=@requestID OUTPUT;
									</cfloop>
								</cfcase>
								<cfcase value="awardOVCredit">
									SET @requestID = null;
									SET @offeringTypeID = #int(val(arguments.event.getValue('sel_ov_offeringTypeID',0)))#;
									SET @creditValue = #val(arguments.event.getValue('txt_ov_CreditValue',0))#;

									EXEC dbo.crd_addRequest @siteID=@siteID, @applicationType='Events', @itemID=#local.qryRegistrants.registrantID#, @offeringTypeID=@offeringTypeID,
										@idnumber='', @lastDateToComplete=@lastDateToComplete, @creditAwarded=1, @creditValueAwarded=@creditValue, @requestID=@requestID OUTPUT;
								</cfcase>
							</cfswitch>
						</cfif>
					</cfloop>

					COMMIT TRAN;

					-- process conditions
					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					select m.orgID, m.memberID, c.conditionID
					from membercentral.dbo.ams_members as m
					inner join dbo.ams_virtualGroupConditions as c on c.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
						and c.fieldCode = 'ev_entry'
					where m.memberID in (#valueList(local.qryRegistrants.memberid)#);

					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadEventGrid();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveSubEventAttendanceCredit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.evRegAttendedArr = arrayNew(1)>
		<cfset local.evRegOfferingArr = arrayNew(1)>
		<cfset local.evNotRegOfferingArr = arrayNew(1)>
		<cfset local.toRegisterList = "">
		<cfset local.registeredList = "">
		
		<!--- Populate registrants (people already registered) and attendance array --->
		<cfloop list="#arguments.event.getValue('registrantList','')#" index="local.thisItem">
			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.eventID = listGetat(local.thisItem,1,"|" )>
			<cfset local.tempStr.registrantID = listGetat(local.thisItem,2,"|" )>
			<cfset local.tempStr.memberID = listGetat(local.thisItem,3,"|" )> 
			<cfset local.tempStr.attended = listGetat(local.thisItem,4,"|" )>
			<cfset arrayAppend(local.evRegAttendedArr,local.tempStr)>
			<cfset local.registeredList = listAppend(local.registeredList,local.tempStr.eventID)>
		</cfloop>
		
		<!--- Populate registrants, attendance and offering type array --->
		<cfloop list="#arguments.event.getValue('regOfferingTypeIDList','')#" index="local.thisItem">
			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.eventID = listGetat(local.thisItem,1,"|" )>
			<cfset local.tempStr.registrantID = listGetat(local.thisItem,2,"|" )>
			<cfset local.tempStr.offeringTypeID = listGetat(local.thisItem,3,"|" )> 
			<cfset local.tempStr.attended = ListFind(arguments.event.getValue('attendedList',''), local.tempStr.eventID)>
			<cfset arrayAppend(local.evRegOfferingArr,local.tempStr)>
		</cfloop>
		
		<!--- Populate events, offering type array --->
		<cfloop list="#arguments.event.getValue('newRegOfferingTypeIDList','')#" index="local.thisItem">
			<cfset local.tempStr = structNew()>
			<cfset local.tempStr.eventID = listGetat(local.thisItem,1,"|" )>
			<cfset local.tempStr.offeringTypeID = listGetat(local.thisItem,3,"|" )> 
			<cfset local.tempStr.attended = ListFind(arguments.event.getValue('attendedList',''), local.tempStr.eventID)>
			<cfset arrayAppend(local.evNotRegOfferingArr,local.tempStr)>
		</cfloop>
		
		<!--- Finalize list of items to be registered --->
		<cfloop list="#arguments.event.getValue('attendedList','')#" index="local.thisItem">
			<cfif not listFind(local.registeredList, local.thisItem)>
				<cfset local.toRegisterList = listAppend(local.toRegisterList,local.thisItem)>
			</cfif>
		</cfloop>
		
		<cftry>
			<cfquery name="local.qryUpdate" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @requestID int, @creditValue decimal(6,2), @offeringTypeID int, @registrationID int, @registrantID int,
						@lastDateToComplete smalldatetime, @dateRegistered smalldatetime, @memberID int, @siteID int,
						@eventID int, @recordedByMemberID int, @statsSessionID int, @itemID int, @identificationMethodID int;

					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;
					SET @recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberid#">;
					SET @statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">;
					SELECT @identificationMethodID = methodID from dbo.ev_regIdentificationMethods where code = 'CP_ADMIN';
				
					BEGIN TRAN;

					<cfloop array="#local.evRegAttendedArr#" index="local.thisItem">
						<cfset local.thisRegistrantAttended = ListFind(arguments.event.getValue('attendedList',''), local.thisItem.eventID)>

						set @registrantID = #val(local.thisItem.registrantID)#;
						set @eventID = #val(local.thisItem.eventID)#;
		
						-- get event end date for the lastDateToComplete credits
						SELECT @lastDateToComplete = max(t.endTime), @eventID = max(reg.eventID)
						from dbo.ev_registrants as r
						inner join dbo.ev_registration as reg on reg.siteID = @siteID and reg.registrationID = r.registrationID
						inner join dbo.ev_times as t on t.eventid = reg.eventid
						inner join dbo.ev_events e on e.siteID = @siteID and e.eventID = reg.eventID
						where r.registrantID = @registrantID;

						update dbo.ev_registrants
					 	set attended = #min(local.thisRegistrantAttended, 1)#
					 	where registrantID = @registrantID;
					
						<!--- if not attended, remove any awarded credit --->
						<cfif not local.thisRegistrantAttended>
							delete from dbo.crd_requests
							where registrantID = @registrantID;
						<cfelse>
							<cfloop array="#local.evRegOfferingArr#" index="local.evRegOfferingItem"> 	
								<cfif local.evRegOfferingItem.registrantID eq local.thisItem.registrantID>			
									SET @requestID = null;
									SET @itemID = #local.evRegOfferingItem.registrantID#;
									SET @offeringTypeID = #local.evRegOfferingItem.offeringTypeID#;
									SET @creditValue = #val(arguments.event.getValue('creditValueAwarded_#local.evRegOfferingItem.offeringTypeID#_new',0))#;
									
									delete from dbo.crd_requests
									where registrantID = @registrantID
									and offeringTypeID = @offeringTypeID;
			
									IF @creditValue > 0 BEGIN
										EXEC dbo.crd_addRequest @siteID=@siteID, @applicationType='Events', @itemID=@itemID, @offeringTypeID=@offeringTypeID, @idnumber='',
											@lastDateToComplete=@lastDateToComplete, @creditAwarded=1, @creditValueAwarded=@creditValue, @requestID=@requestID OUTPUT;
									END
								</cfif>
							</cfloop>
						
							<!---Adding credits  --->
							<cfloop array="#local.evNotRegOfferingArr#" index="local.thisArrItem">
								<cfif local.thisArrItem.eventID eq local.thisItem.eventID>
									SET @offeringTypeID = #local.thisArrItem.offeringTypeID#;
									SET @creditValue = #val(arguments.event.getValue('notReg_creditValueAwarded_#local.thisArrItem.offeringTypeID#_new',0))#;
												
									IF @creditValue > 0 BEGIN
										SET @requestID = NULL;
										SET @itemID = #local.thisItem.registrantID#;

										EXEC dbo.crd_addRequest @siteID=@siteID, @applicationType='Events', @itemID=@itemID, @offeringTypeID=@offeringTypeID,
											@idnumber='', @lastDateToComplete=@lastDateToComplete, @creditAwarded=1, @creditValueAwarded=@creditValue, @requestID=@requestID OUTPUT;
									END
								</cfif>
							</cfloop>
						</cfif>

						<cfswitch expression="#arguments.event.getValue('SELQUICKACT','')#">
							<cfcase value="removeCredit">
								SET @offeringTypeID = #int(val(arguments.event.getValue('sel_remove_offeringTypeID',0)))#;
								SET @requestID = null;
								SELECT @requestID = requestID from dbo.crd_requests where registrantID = #local.thisItem.registrantID# and offeringTypeID = @offeringTypeID;
									
								if exists (select requestID from dbo.crd_requests where requestID = @requestID) 
									delete from dbo.crd_requests
									where requestID = @requestID;
							</cfcase>
						</cfswitch>
					</cfloop>
				
					<!--- Add registrants --->
					<cfloop list="#local.toRegisterList#" index="local.thisItem">
						set @eventID = #val(local.thisItem)#;
						set @memberID = #val(arguments.event.getValue('mid',0))#;
					
						select @registrationID = r.registrationID, @dateRegistered = endDate
						from dbo.ev_registration as r
						inner join dbo.ev_events as e on e.eventID = r.eventID 
							and e.siteID = @siteID
							and e.eventID = @eventID 
						where r.siteID = @siteID;
					
						insert into dbo.ev_registrants (registrationID, memberID, recordedOnSiteID, rateID, dateRegistered, status, attended, identificationMethodID)
						values (@registrationID, @memberID, @siteID, NULL, @dateRegistered, 'A', 1, @identificationMethodID);
							select @registrantID = SCOPE_IDENTITY();
					
						<!--- Adding credits  --->
						<cfloop array="#local.evNotRegOfferingArr#" index="local.thisArrItem">
							<cfif local.thisArrItem.eventID eq local.thisItem>
								SET @offeringTypeID = #local.thisArrItem.offeringTypeID#;
								SET @creditValue = #val(arguments.event.getValue('notReg_creditValueAwarded_#local.thisArrItem.offeringTypeID#_new',0))#;
								
								IF @creditValue > 0 BEGIN
									SET @requestID = NULL;

									EXEC dbo.crd_addRequest @siteID=@siteID, @applicationType='Events', @itemID=@registrantID, @offeringTypeID=@offeringTypeID,
										@idnumber='', @lastDateToComplete=@dateRegistered, @creditAwarded=1, @creditValueAwarded=@creditValue, @requestID=@requestID OUTPUT;
								END
							</cfif>
						</cfloop>
					</cfloop>

					COMMIT TRAN;

					<!--- process event conditions for this member --->
					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					SELECT distinct c.orgID, #arguments.event.getValue('mid',0)#, c.conditionID
					from dbo.ams_virtualGroupConditions as c
					where c.orgID = #arguments.event.getValue('mc_siteInfo.orgID')#
					and c.fieldCode = 'ev_entry';

					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL
						DROP TABLE ##tblMCQRun;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
		</cfcatch>
		</cftry>

		<cfset local.thelink = "#this.link.manageSubEventAttendanceCredit#&eid=#arguments.event.getValue('eID')#" & (arguments.event.valueExists('_rid') ? "&_rid=#arguments.event.getValue('_rid')#" : "")>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadPage(true);
				self.location.href='#JSStringFormat(local.thelink)#';
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- certificates --->
	<cffunction name="doEmailCert" access="private" output="false" returntype="void">
		<cfargument name="Event" type="any" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="emailOverride" type="string" required="no" default="">
		<cfargument name="customText" type="string" required="no" default="">
		
		<cfset var local = structNew()>
		<cfset local.objCertificate = CreateObject("component","certificate")>
		<cfsetting requesttimeout="300">
		
		<cfset local.strCertificate = local.objCertificate.generateCertificate(registrantID=arguments.registrantID)>
		<cfif StructKeyExists(local.strCertificate,"certificatePath")>
			<!--- wait one second for the pdf to appear on the server --->
			<cfloop from="1" to="5" index="local.count">
				<cfif NOT FileExists("#local.strCertificate.certificatePath#")>
					<cfset createObject("java","java.lang.Thread").sleep(1000)>
				<cfelse>
					<cfbreak>
				</cfif>
			</cfloop>
				
			<cfif FileExists("#local.strCertificate.certificatePath#")>
				<cfscript>
				local.strEmailContent = local.objCertificate.generateCertificateEmail(registrantID=arguments.registrantID, customText=arguments.customtext);

				local.strReturn = application.objEmailWrapper.sendMailESQ(
										emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
										emailto=[{ name=local.strEmailContent.registrantName, email=len(arguments.emailOverride) gt 0 ? arguments.emailOverride : local.strEmailContent.emailto }],
										emailreplyto=len(local.strEmailContent.replyto) gt 0 ? local.strEmailContent.replyto : '',
										emailsubject=local.strEmailContent.subject,
										emailtitle=local.strEmailContent.emailTitle,
										emailhtmlcontent=local.strEmailContent.htmlemail,
										emailAttachments=[{ file="certificate.pdf", folderpath=local.strCertificate.certificateFolderPath }],
										siteID=arguments.event.getValue('mc_siteinfo.siteid'),
										memberID=local.strEmailContent.memberID,
										messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCERT"),
										sendingSiteResourceID=this.siteResourceID
									);
				</cfscript>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="emailCertificates" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();

			if (val(arguments.event.getValue('eID',0)) lte 0 and NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent')) {
				application.objCommon.redirect('#this.link.message#&message=1');
			}

			local.strFilters = structNew();
			structInsert(local.strFilters, 'eID', arguments.event.getValue('eID'));
			structInsert(local.strFilters, 'evDateFrom', arguments.event.getValue('evDateFrom',''));
			structInsert(local.strFilters, 'evDateTo', arguments.event.getValue('evDateTo',''));
			structInsert(local.strFilters, 'evCalendar', arguments.event.getValue('evCalendar',0));
			structInsert(local.strFilters, 'evCategory', arguments.event.getValue('evCategory',0));
			structInsert(local.strFilters, 'evKeyword', arguments.event.getValue('evKeyword',''));
			structInsert(local.strFilters, 'evReportCode', arguments.event.getValue('evReportCode',''));
			structInsert(local.strFilters, 'evEventType', arguments.event.getValue('evEventType','all'));
			structInsert(local.strFilters, 'rDateFrom', arguments.event.getValue('rDateFrom',''));
			structInsert(local.strFilters, 'rDateTo', arguments.event.getValue('rDateTo',''));
			structInsert(local.strFilters, 'rAttended', arguments.event.getValue('rAttended',''));
			structInsert(local.strFilters, 'rBillFrom', arguments.event.getValue('rBillFrom',''));
			structInsert(local.strFilters, 'rBillTo', arguments.event.getValue('rBillTo',''));
			structInsert(local.strFilters, 'rDuesFrom', arguments.event.getValue('rDuesFrom',''));
			structInsert(local.strFilters, 'rDuesTo', arguments.event.getValue('rDuesTo',''));
			structInsert(local.strFilters, 'rEvRole', arguments.event.getValue('rEvRole',''));
			structInsert(local.strFilters, 'rEvFormCompletion', arguments.event.getValue('rEvFormCompletion',''));
			structInsert(local.strFilters, 'rAssocType', arguments.event.getValue('rAssocType',''));
			structInsert(local.strFilters, 'rAssociatedMemberID', arguments.event.getValue('rAssociatedMemberID',''));
			structInsert(local.strFilters, 'rAssociatedGroupID', arguments.event.getValue('rAssociatedGroupID',''));
			structInsert(local.strFilters, 'rCreditType', arguments.event.getValue('rCreditType',''));
			structInsert(local.strFilters, 'rStatus', arguments.event.getValue('rStatus',0));
			structInsert(local.strFilters, 'rEvRate', arguments.event.getValue('rEvRate',''));

			local.rc = arguments.event.getCollection();
			for (local.thisFormField in local.rc) {
				if (left(local.thisFormField,8) eq "rFieldID" and len(local.rc[local.thisFormField])) {
					local.arrCFID = listToArray(local.rc[local.thisFormField],'_');
					structInsert(local.strFilters, local.thisFormField, local.rc[local.thisFormField]);
					
					if (isValid("integer",local.arrCFID[1]) and structKeyExists(local.rc,"RF_#local.arrCFID[1]#_exp") and len(local.rc['RF_#local.arrCFID[1]#_exp'])) {
						structInsert(local.strFilters, "RF_#local.arrCFID[1]#_exp", local.rc['RF_#local.arrCFID[1]#_exp']);
						if (structKeyExists(local.rc,"RF_#local.arrCFID[1]#") and len(local.rc['RF_#local.arrCFID[1]#'])) {
							structInsert(local.strFilters, "RF_#local.arrCFID[1]#", local.rc['RF_#local.arrCFID[1]#']);
						} else if (structKeyExists(local.rc,"RF_#local.arrCFID[1]#_lower") and structKeyExists(local.rc,"RF_#local.arrCFID[1]#_upper")) {
							structInsert(local.strFilters, "RF_#local.arrCFID[1]#_lower", local.rc['RF_#local.arrCFID[1]#_lower']);
							structInsert(local.strFilters, "RF_#local.arrCFID[1]#_upper", local.rc['RF_#local.arrCFID[1]#_upper']);
						}
					}
				}
			}

			local.strResourceTitle = { resourceTitle='Certificates', resourceTitleDesc='', templateEditorLabel='<div class="d-flex"><span>Compose your message.</span><span class="ml-auto small text-dim"><i class="fa-regular fa-paperclip fa-lg"></i> We''ll automatically include a PDF attachment of the certificate.</span></div>' };
		
			local.argumentCollection = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), resourceType='Events',
						recipientType='AttendeeCertificates', strResourceTitle=local.strResourceTitle, strFilters=local.strFilters, arrRecipientModes=arrayNew(1),
						mergeCodeInstructionsLink="#buildCurrentLink(arguments.event,'showMergeCodeInstructions')#&eid=#arguments.event.getValue('eID',0)#&incEV=1&mode=stream", emailTemplateTreeCode="ETEVENTS" };

			local.data = CreateObject("component","model.admin.common.modules.massEmails.massEmails").prepMassEmails(argumentCollection=local.argumentCollection);

			return returnAppStruct(local.data,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="viewCertificate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfscript>
		// security 
		if (val(arguments.event.getValue('eID',0)) lte 0 and NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent'))
			application.objCommon.redirect('#this.link.message#&message=1');
		</cfscript>

		<cfswitch expression="#arguments.event.getValue('certaction','')#">
			<cfcase value="loadCert">
				<cftry>
					<cfsetting requesttimeout="300">
					<cfset local.strCertificate = CreateObject("component","certificate").generateCertificate(registrantID=arguments.event.getValue('rID'))>
					<cfif len(local.strCertificate.certificateURL)>
						<cflocation url="#local.strCertificate.certificateURL#" addtoken="No">
					</cfif>
					<cfsavecontent variable="local.data">
						<cfoutput>No certificate generated.</cfoutput>
					</cfsavecontent>
					
					<cfcatch>
						<cfset application.objError.extendRequestTimeout()>
						<cfset application.objError.sendError(cfcatch=cfcatch)>
						<cfsavecontent variable="local.data">
							<cfoutput>
								<div class="m-2"><h4>Sorry</h4><div>We encountered an error loading this certificate. Try again.</div></div>
							</cfoutput>
						</cfsavecontent>
					</cfcatch>
				</cftry>
			</cfcase>
			<cfcase value="emailCert">
				<cfif isValid("regex",arguments.event.getValue('_email',''),application.regEx.email)>
					<cfset doEmailCert(event=arguments.event,registrantID=arguments.event.getValue('rID'),emailOverride=arguments.event.getValue('_email',''))>
				</cfif>
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">top.MCModalUtils.hideModal();</script>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfquery name="local.qryGetEventFromRegistrant" datasource="#application.dsn.membercentral.dsn#">
					select top 1 m.activeMemberID as memberid
					from dbo.ev_registrants as r
					inner join dbo.ams_members as m on m.memberID = r.memberID
					where r.registrantID = <cfqueryparam value="#arguments.event.getValue('rID')#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
				<cfset local.qryRegistrantInfo = CreateObject("component","eventReg").getRegistrantInfo(mid=local.qryGetEventFromRegistrant.memberID)>
				<cfset local.registrantEmail = local.qryRegistrantInfo.email>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<cfinclude template="frm_registrantCertificate.cfm">
					</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="showSubmitEvaluationForm" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.eventID = val(arguments.event.getValue('eID',0))>
		<cfset local.registrantID = val(arguments.event.getValue('rid',0))>

		<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent')>
			<cfset application.objCommon.redirect('#this.link.message#&message=1')>
		</cfif>

		<cfquery name="local.qryRegistrant" datasource="#application.dsn.membercentral.dsn#">
			select m2.firstName + ' ' + m2.lastName as registrantName, m2.company
			from  dbo.ev_registrants as er
			inner join dbo.ams_members as m on m.memberID = er.memberID
				and er.registrantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.registrantID#">
			inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID;
		</cfquery>

		<cfquery name="local.qryPendingEvals" datasource="#application.dsn.membercentral.dsn#">
			SELECT eaf.formID, f.formTitle, eaf.orderBy
			FROM dbo.ev_eventsAndForms as eaf
			INNER JOIN formbuilder.dbo.tblForms as f on f.formid = eaf.formid and f.isDeleted = 0
			LEFT OUTER JOIN dbo.ev_eventsAndFormResponses efr
				INNER JOIN formbuilder.dbo.tblResponses as fbr on fbr.responseID = efr.responseID
					AND fbr.isActive = 1
					AND fbr.dateCompleted IS NOT NULL
				ON efr.eventFormID = eaf.eventFormID
				AND efr.registrantID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.registrantID#">
			WHERE eaf.eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.eventID#">
			GROUP BY eaf.formID, f.formTitle, eaf.orderBy
			HAVING COUNT(fbr.responseID) = 0
			ORDER BY eaf.orderBy;
		</cfquery>

		<cfset local.viewEvaluationFormLink = buildCurrentLink(arguments.event,"renderEvaluationForm") & '&eID=#local.eventID#&rid=#local.registrantID#&mode=stream'>
		<cfset local.saveEvaluationFormSubmissionLink = buildCurrentLink(arguments.event,"saveEvaluationFormSubmission") & "&eID=#local.eventID#&rid=#local.registrantID#&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_submitEvaluation.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="renderEvaluationForm" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.formID = arguments.event.getValue('formID',0)>
		<cfset local.eventID = arguments.event.getValue('eid',0)>
		<cfset local.registrantID = arguments.event.getValue('rid',0)>
		
		<cfstoredproc procedure="up_getFormXML" datasource="#application.dsn.tlasites_formbuilder.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.formID#">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocparam type="In" cfsqltype="CF_SQL_LONGVARCHAR" value="<questions mode=""ALL""/>">
			<cfprocparam type="In" cfsqltype="CF_SQL_BIT" value="1">
			<cfprocresult name="local.qryFormXML" resultset="1">
			<cfprocresult name="local.qryQuestionTypesXML" resultset="2">
		</cfstoredproc>
		
		<cfscript>
		local.objForm = CreateObject("component","model.formbuilder.FBForms");
		local.xmlForm = XMLParse(local.qryFormXML.formXML);
		local.strArgs = StructNew();
		local.strArgs.XMLobj = local.xmlForm;
		local.strArgs.paging = -1;
		local.xmlForm = local.objForm.transformFormForPaging(local.strArgs);

		// insert empty response 
		local.strArgs = structNew();
		local.strArgs.FBformid = local.formID;
		local.strArgs.FBdepoID = 0;
		local.strArgs.FBorgcode = arguments.event.getValue('mc_siteInfo.orgCode');
		local.strArgs.FBholderResponse = true;
		local.strArgs.formVars = StructNew();
		local.arrQuestions = XMLSearch(local.xmlForm,"//@controlfield");
		for (local.x = 1; local.x lte arraylen(local.arrQuestions); local.x = local.x + 1)
			structInsert(local.strArgs.formvars,local.arrQuestions[local.x].xmlValue,"");
		
		local.FBResponseID = CreateObject("component","model.formbuilder.FBResponses").saveFormResponse(strArgs=local.strArgs);
		</cfscript>

		<!--- insert response --->
		<cfset CreateObject("component","model.events.events").insertEvaluationFormResponse(eventID=local.eventID, registrantID=local.registrantID, formID=local.formID, responseID=local.FBResponseID)>
		
		<cfset local.strFormDetails = CreateObject("component","model.admin.evaluations.evaluation").getFormPreviewDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), formID=local.formID)>

		<cfsavecontent variable="local.data">
			<cfif NOT local.strFormDetails.isValidForm>
				<cfoutput>
					<div class="m-2">
						<div class="alert alert-danger mb-2">Unable to render evaluation form.</div>
					</div>
				</cfoutput>
			<cfelse>
				<cfinclude template="/model/admin/evaluations/frm_previewForm.cfm">
			</cfif>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveEvaluationFormSubmission" access="public" output="false" returntype="struct" hint="Save Evaluation Submission">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent')>
			<cfset local.strResult = { "success": false, "errmsg":"You have no permission to perform this action." }>
		</cfif>

		<cfset local.strResult = CreateObject("component","event").saveEvaluationFormSubmission(event=arguments.event)>

		<cfreturn returnAppStruct(serializeJSON(local.strResult),'echo')>
	</cffunction>
	
	<!--- EVENT SYNDICATION FUNCTIONS --->
	<cffunction name="addSyndication" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// security ---------------------------------------------------------------------------------
			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.addEvent'))
				application.objCommon.redirect("#this.link.listCalEvents#&cID=#arguments.event.getValue('cid',0)#&tab=syndicatedRules");
			// get information --------------------------------------------------------------------------
			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteid'), calendarID=arguments.event.getValue('cID',0));
			local.formlink	= this.link.insertSyndication & '&cID=' & arguments.event.getValue('cID'); 
			local.qrySyndCalendars = this.objAdminEvent.getCalendarsForSyndication(siteid=arguments.event.getValue('mc_siteinfo.siteid'), calendarid=arguments.event.getValue('cID'));
			local.qryCategories = CreateObject("component","model.events.calendar").getCategories(arguments.event.getValue('cID'),this.languageID,arguments.event.getValue('mc_siteInfo.siteID'));
			// Build breadCrumb Trail -------------------------------------------------------------------
			appendBreadCrumbs(arguments.event,{ link='#this.link.listCalEvents#&cID=#arguments.event.getValue('cID',0)#', text=encodeForHTML(local.qryCalendar.calendarName) });
			appendBreadCrumbs(arguments.event,{ link='', text="New Syndicated Event" });
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_syndication.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="insertSyndication" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			this.objAdminEvent.insertSyndication(
				siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				calendarID=arguments.event.getValue('cID'),
				sourceCalendarID=arguments.event.getValue('sCalendarID',0),
				sourceCategoryID=arguments.event.getValue('sCategoryID',0),
				sourceEventID=arguments.event.getValue('sEventID',0),
				ovCategoryID=arguments.event.getValue('sovCategoryID',0)
				);
		</cfscript>
		<cflocation url="#this.link.listCalEvents#&cID=#arguments.event.getValue('cID')#&tab=syndicatedRules" addtoken="no">
	</cffunction>

	<cffunction name="deleteSyndication" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var qryDelete = "">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="qryDelete">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				DELETE FROM dbo.ev_calendarEvents
				WHERE calendarEventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('ceID')#">;
		
				EXEC dbo.ev_refreshCalendarEventsCache @siteID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
				EXEC dbo.ev_refreshCalendarEventsCategoryIDList @siteID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cflocation url="#this.link.listCalEvents#&cid=#arguments.event.getValue('cID')#&tab=syndicatedRules" addtoken="no">
	</cffunction>
	
	<!--- CATEGORY FUNCTIONS --->
	<cffunction name="insertCategory" access="public" output="no" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.urlString = arguments.event.getValue('callback','');
			local.stripedList = 'msg,category,categoryShort,calcolor,visibility';
			for( local.x=1; local.x <= listLen(local.stripedList); local.x++ ){
				local.currentItem = listGetAt(local.stripedList,local.x);
			 	local.stringPosition = listContains(local.urlString,local.currentItem,'&');
				if( local.stringPosition )
					local.urlString = listDeleteAt(local.urlString,local.stringPosition,'&');
			}
			// check to see if category name already exists
			local.catExists = this.objAdminEvent.categoryCheck(arguments.event.getValue('cid'),0,arguments.event.getValue('category'));
			if (local.catExists) 
				application.objCommon.redirect("#local.urlString#&msg=1&category=#arguments.event.getValue('category','')#&categoryShort=#arguments.event.getValue('categoryShort','')#&calcolor=#arguments.event.getValue('calcolor','')#");
			else
				this.objAdminEvent.insertCategory(
					calendarid=arguments.event.getValue('cid'),
					category=arguments.event.getValue('category'),
					categoryshort=arguments.event.getValue('categoryShort'),
					calcolor=replace(arguments.event.getValue('calcolor'),'##','','ALL'),
					visibility=arguments.event.getValue('visibility','N')
				);
			local.categoryCount	= this.objAdminEvent.getCategoryCount(arguments.event.getValue('mc_siteInfo.siteID'));
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.enableAddEventCheck(#local.categoryCount#);
				top.reloadCatGrid();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editCategory" access="public" output="no" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			if(val(arguments.event.getValue('categoryID',0))) {
				local.qryCategory = this.objAdminEvent.getCategory(arguments.event.getValue('categoryID',0));
				local.urlString = "#this.link.updateCategory#&categoryID=#local.qryCategory.categoryID#";
				// init vars
				arguments.event.paramValue('categoryID',val(local.qryCategory.categoryID));
				arguments.event.paramValue('category',local.qryCategory.category);
				arguments.event.paramValue('categoryShort',local.qryCategory.categoryShort);
				arguments.event.paramValue('calcolor',local.qryCategory.calcolor);
				arguments.event.paramValue('visibility',local.qryCategory.visibility);
			} else {
				local.urlString = "#this.link.insertCategory#&cid=#arguments.event.getValue('cid')#";
				arguments.event.paramValue('categoryID',0);
				arguments.event.paramValue('category','');
				arguments.event.paramValue('categoryShort','');
				arguments.event.paramValue('calcolor','');
				arguments.event.paramValue('visibility','N');
			}
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_category.cfm">
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateCategory" access="public" output="no" returntype="struct">
		<cfargument name="Event" type="any">	
		<cfscript>
			var local = structNew();
			local.urlString = arguments.event.getValue('callback','');

			local.stringPosition = listContains(local.urlString,'msg','&');
			if( local.stringPosition )
				local.urlString = listDeleteAt(local.urlString,local.stringPosition,'&');

			if( arguments.event.getValue('categoryID',0) is 0) 
				local.catExists = false;
			else
				local.catExists	= this.objAdminEvent.categoryCheck(arguments.event.getValue('calendarID'),arguments.event.getValue('categoryID',0),arguments.event.getValue('category',''));

			if (local.catExists)
				application.objCommon.redirect(local.urlString & '&msg=1');
			else
				local.updateResult = this.objAdminEvent.updateCategory(
					siteID=arguments.event.getValue('mc_siteinfo.siteID'),
					categoryid=arguments.event.getValue('categoryID',0),
					category=arguments.event.getValue('category',''),
					categoryshort=arguments.event.getValue('categoryShort',''),
					calcolor=replace(arguments.event.getValue('calcolor',''),'##','','ALL'),
					visibility=arguments.event.getValue('visibility','N')
				);
		</cfscript>

		<cfif compare(arguments.event.getValue('oldCategoryName',''),arguments.event.getValue('category',''))>
			<cfquery name="local.qryUpdateConditions" datasource="#application.dsn.membercentral.dsn#">
				UPDATE c
				SET c.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(c.conditionID)
				FROM dbo.ams_virtualGroupConditions as c 
				INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
				INNER JOIN dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
				WHERE c.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#
				and c.fieldCode = 'ev_entry'
				AND ck.conditionKey = 'evCategoryID'
				AND cv.conditionValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('categoryID',0)#">
			</cfquery>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadCatGrid();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<!--- REGISTRATION FUNCTIONS --->
	<cffunction name="insertRegistration" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.objEvent = CreateObject("component","model.events.events");
			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
			
			if(local.strEvent.qryEventRegMeta.recordCount){ this.objAdminEvent.deleteRegistration(local.strEvent.qryEventRegMeta.registrationID); }
			if(local.strEvent.qryEventMeta.altRegistrationURL NEQ ''){ this.objAdminEvent.deleteAltURL(arguments.event.getValue('eID')); }

			arguments.event.paramValue('eID','');
			arguments.event.paramValue('registrationTypeID','');
			arguments.event.paramValue('expirationContent','');
			arguments.event.paramValue('replyToEmail',session.cfcuser.memberdata.email);
			arguments.event.paramValue('notifyEmail','');
			arguments.event.paramValue('redirectPage','1');

			arguments.event.setValue('regStartTime',CreateDateTime(year(now()),month(now()),day(now()),Hour(now()),0,0));
			// set start to next hour, end to end of event
			arguments.event.setValue('regStartTime',DateAdd('h',1,arguments.event.getValue('regStartTime')));
			if (local.strEvent.qryEventMeta.isAllDayEvent is 1) {
				arguments.event.setValue('regEndTime',CreateDateTime(year(local.strEvent.qryEventTimes_selected.endTime),month(local.strEvent.qryEventTimes_selected.endTime),day(local.strEvent.qryEventTimes_selected.endTime),17,0,0));
			} else {
				arguments.event.setValue('regEndTime',local.strEvent.qryEventTimes_selected.endTime);
			}
			
			// if reg end time is earlier than start time, just use start time + 1 hr.
			if (arguments.event.getValue('regEndTime') lt arguments.event.getValue('regStartTime')) {
				arguments.event.setValue('regEndTime',DateAdd('h',1,arguments.event.getValue('regStartTime')));
			}

			local.registrationInfo = this.objAdminEvent.insertRegistration(
				arguments.event.getValue('eID'),
				arguments.event.getValue('registrationTypeID'),
				arguments.event.getValue('regStartTime'),
				arguments.event.getValue('regEndTime'),
				arguments.event.getValue('replyToEmail'),
				arguments.event.getValue('notifyEmail')
			);
			
			// event contentObject
			local.updateEventContent = this.objAdminEvent.updateContent(local.registrationInfo.expirationContentID,this.languageID,1,'','',arguments.event.getValue('expirationContent'));
		</cfscript>

		<cfif arguments.event.getValue('redirectPage')>
			<cflocation url="#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=registration" addtoken="False">
		</cfif>
	</cffunction>
	
	<cffunction name="saveEventRegCreditDetails" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdate">
			UPDATE dbo.ev_registration
			SET showCredit = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('showCredit',0)#">
			WHERE registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('registrationid')#">
		</cfquery>

		<cflocation url="#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=credit" addtoken="no">
	</cffunction>

	<cffunction name="saveEventRegDetails" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.startDateTime = ParseDateTime("#replace(arguments.event.getValue('regStartTime'),' - ',' ')#");
		local.endDateTime = ParseDateTime("#replace(arguments.event.getValue('regEndTime'),' - ',' ')#");
		if (len(arguments.event.getValue('onlineEnterStartTime'))) {
			local.onlinestartDateTime = ParseDateTime("#replace(arguments.event.getValue('onlineEnterStartTime'),' - ',' ')#");
			local.onlineendDateTime = ParseDateTime("#replace(arguments.event.getValue('onlineEnterEndTime'),' - ',' ')#");
		} else {
			local.onlinestartDateTime = "";
			local.onlineendDateTime = "";
		}
		if(len(arguments.event.getValue('regEditDeadline',''))) {
			local.regEditDeadlineDateTime = ParseDateTime("#replace(arguments.event.getValue('regEditDeadline'),' - ',' ')#");
		} else {
			local.regEditDeadlineDateTime = "";
		}

		// convert times from default timezone to central to store in db as central
		local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		if (local.regTimeZone neq "US/Central") {
			local.startDateTime = local.objTZ.convertTimeZone(dateToConvert=local.startDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central');
			local.endDateTime = local.objTZ.convertTimeZone(dateToConvert=local.endDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central');
			local.startDateTime = ParseDateTime(Replace(Left(local.startDateTime,19), "T", " ", "one"));
			local.endDateTime = ParseDateTime(Replace(Left(local.endDateTime,19), "T", " ", "one"));
			if (len(local.onlinestartDateTime)) {
				local.onlinestartDateTime = local.objTZ.convertTimeZone(dateToConvert=local.onlinestartDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central');
				local.onlineendDateTime = local.objTZ.convertTimeZone(dateToConvert=local.onlineendDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central');
				local.onlinestartDateTime = ParseDateTime(Replace(Left(local.onlinestartDateTime,19), "T", " ", "one"));
				local.onlineendDateTime = ParseDateTime(Replace(Left(local.onlineendDateTime,19), "T", " ", "one"));
			}
			if(len(local.regEditDeadlineDateTime)) {
				local.regEditDeadlineDateTime = local.objTZ.convertTimeZone(dateToConvert=local.regEditDeadlineDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central');
				local.regEditDeadlineDateTime = ParseDateTime(Replace(Left(local.regEditDeadlineDateTime,19), "T", " ", "one"));
			}
		}
		
		if (len(arguments.event.getValue('replyToEmail',''))) {
			local.stReplyTo = replace(replace(arguments.event.getValue('replyToEmail'),',',';','ALL'),' ','','ALL');
			local.replyToEmailArr = listToArray(local.stReplyTo,';');
			for (local.i=1; local.i lte arrayLen(local.replyToEmailArr); local.i++) {
				if (len(local.replyToEmailArr[local.i]) and not isValid("regex",local.replyToEmailArr[local.i],application.regEx.email)) {
					arrayDeleteAt(local.replyToEmailArr,local.i);
				}
			}
			arguments.event.setValue('replyToEmail',arrayToList(local.replyToEmailArr,'; '));
		}

		if (len(arguments.event.getValue('notifyEmail',''))) {
			local.stNotify = replace(replace(arguments.event.getValue('notifyEmail'),',',';','ALL'),' ','','ALL');
			local.notifyEmailArr = listToArray(local.stNotify,';');
			for (local.i=1; local.i lte arrayLen(local.notifyEmailArr); local.i++) {
				if (len(local.notifyEmailArr[local.i]) and not isValid("regex",local.notifyEmailArr[local.i],application.regEx.email)) {
					arrayDeleteAt(local.notifyEmailArr,local.i);
				}
			}
			arguments.event.setValue('notifyEmail',arrayToList(local.notifyEmailArr,'; '));
		}
		</cfscript>

		<cfif arguments.event.getValue('registrationid',0) is 0>
			<cfset local.strEvent = CreateObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
		<cfelse>
			<cfif arguments.event.getValue('isOnlineMeeting',0) EQ 0>
				<cfset arguments.event.setValue('onlineEmbedCode','')>
				<cfset arguments.event.setValue('onlineEmbedOverrideLink','')>
				<cfset local.onlinestartDateTime = ''>
				<cfset local.onlineendDateTime = ''>
			</cfif>
		</cfif>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.saveEventRegDetails">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				BEGIN TRAN;

				<cfif arguments.event.getValue('registrationid',0) is 0>
					<cfif local.strEvent.qryEventRegMeta.recordCount>
						UPDATE dbo.ev_registration
						SET status = 'D'
						WHERE registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strEvent.qryEventRegMeta.registrationID#">;
					</cfif>

					DECLARE @eventID int, @registrationTypeID int, @startDate datetime, @endDate datetime, @registrantCap int, @replyToEmail varchar(50), @notifyEmail varchar(50), @registrationID int;

					SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID',0)#">;
					SET @registrationTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrationTypeID',1)#">;
					SET @startDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.startDateTime#">;
					SET @endDate = <cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.endDateTime#">;
					SET @registrantCap = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="" null="yes">;
					SET @replyToEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('replyToEmail','')#">;
					SET @notifyEmail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('notifyEmail','')#">;

					EXEC dbo.ev_createRegistration @eventID=@eventID, @registrationTypeID=@registrationTypeID, @startDate=@startDate, 
					@endDate=@endDate, @registrantCap=@registrantCap, @replyToEmail=@replyToEmail, @notifyEmail=@notifyEmail, 
					@isPriceBasedOnActual=1, @bulkCountByRate=0, @registrationID=@registrationID OUTPUT;

					SELECT @registrationID as registrationID

				<cfelse>
					
					DECLARE @eventid int;

					SELECT @eventid = eventID
					FROM dbo.ev_registration
					WHERE registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('registrationID',0)#">
					
					UPDATE dbo.ev_registration
					SET registrationTypeID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('registrationTypeID',1)#">,
						startDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startDateTime#">,
						endDate = <cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endDateTime#">,
						<cfif val(arguments.event.getValue('registrantCap',0))>
							registrantCap = <cfqueryparam cfsqltype="cf_sql_integer" value="#val(arguments.event.getValue('registrantCap',0))#">,
						<cfelse>
							registrantCap = NULL,
						</cfif>
						replyToEmail = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('replyToEmail','')#">,
						notifyEmail = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('notifyEmail','')#">,
						isOnlineMeeting = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('isOnlineMeeting',0)#">,
						onlineEmbedCode = <cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('onlineEmbedCode','')#">,
						onlineEmbedOverrideLink = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('onlineEmbedOverrideLink','')#">,
						onlineEnterStartTime = <cfif len(local.onlinestartDateTime)><cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.onlinestartDateTime#"><cfelse>null</cfif>,
						onlineEnterEndTime = <cfif len(local.onlineendDateTime)><cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.onlineendDateTime#"><cfelse>null</cfif>,
						enableRealTimeRoster = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('enRealTimeRoster',0)#">,
						registrantEditAllowed = <cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('regEditAllowed',0)#">,
						registrantEditDeadline = <cfif arguments.event.getValue('regEditAllowed',0) and len(local.regEditDeadlineDateTime)><cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.regEditDeadlineDateTime#"><cfelse>null</cfif>
					WHERE registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('registrationID',0)#">
				</cfif>

				DECLARE @expireContentID int, @registrantCapContentID int, @expireDeadlineContentID int, @registrantEditRefundContentID int, @expireContent varchar(max), @registrantCapContent varchar(max), @regEditDeadlineContent varchar(max), @regEditRefundContent varchar(max), @languageID int, @memberID int;

				SET @expireContentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('expireContentID')#">;
				SET @registrantCapContentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrantCapContentID')#">;
				SET @expireDeadlineContentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('expireDeadlineContentID')#">;
				SET @registrantEditRefundContentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrantEditRefundContentID')#">;

				SET @expireContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('expireContent')#">;
				SET @registrantCapContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('registrantCapContent')#">;
				SET @regEditDeadlineContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('regEditDeadlineContent','')#">;
				SET @regEditRefundContent = <cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('regEditRefundContent','')#">;

				SET @languageID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.languageID#">;
				SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).orgID)#">;

				EXEC dbo.cms_updateContent @contentID=@expireContentID, @languageID=@languageID, @isHTML=1, 
				@contentTitle='Expiration Message', @contentDesc='', @rawContent=@expireContent, @memberID=@memberID;

				EXEC dbo.cms_updateContent @contentID=@registrantCapContentID, @languageID=@languageID, @isHTML=1, 
				@contentTitle='Registrant Cap Message', @contentDesc='', @rawContent=@registrantCapContent, @memberID=@memberID;
				
				EXEC dbo.cms_updateContent @contentID=@expireDeadlineContentID, @languageID=@languageID, @isHTML=1, 
				@contentTitle='Editing Expiration Message', @contentDesc='', @rawContent=@regEditDeadlineContent, @memberID=@memberID;
				
				EXEC dbo.cms_updateContent @contentID=@registrantEditRefundContentID, @languageID=@languageID, @isHTML=1, 
				@contentTitle='Editing Refund Policy', @contentDesc='', @rawContent=@regEditRefundContent, @memberID=@memberID;

				COMMIT TRAN;

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
		
		<cfif arguments.event.getValue('registrationid',0) is 0>
			<cfset arguments.event.setValue('registrationID',local.saveEventRegDetails.registrationid)>
		</cfif>

		<cflocation url="#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=registration" addtoken="no">
	</cffunction>

	<cffunction name="saveEventRegRateAndAccDetails" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()/>

		<cfif arguments.event.getValue('registrationid',0)>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.updateReg">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					DECLARE @eventID int, @registrationID int;
					DECLARE @tmpMPProfileIDs TABLE (profileID int PRIMARY KEY);

					SET @registrationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrationID')#">;
					
					SELECT @eventID = eventID 
					FROM dbo.ev_registration 
					WHERE registrationID = @registrationID;

					<cfif listLen(arguments.event.getValue('evRegMPProfiles',''))>
						INSERT INTO @tmpMPProfileIDs (profileID)
						SELECT DISTINCT listItem
						FROM dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('evRegMPProfiles')#">,',');
					</cfif>
					
					BEGIN TRAN;
						UPDATE dbo.ev_registration 
						SET registrationTypeID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrationTypeID',1)#">,						
							isPriceBasedOnActual = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('isPriceBasedOnActual',0)#">,
							bulkCountByRate = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('bulkCountByRate',0)#">					
						WHERE registrationID = @registrationID;
						
						<cfif arguments.event.getValue('registrationtypeid',0) is 1>
							UPDATE dbo.ev_events
							SET GLAccountID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('evGLAccountID',0)#">,0)
							WHERE eventID = @eventID;
						</cfif>

						-- event registration merchant profiles
						MERGE dbo.ev_registrationMerchantProfiles AS Target
						USING (
							SELECT @registrationID AS registrationID, profileID
							FROM @tmpMPProfileIDs
						) AS Source
						ON Target.registrationID = Source.registrationID AND Target.profileID = Source.profileID

						-- Insert new merchant profiles
						WHEN NOT MATCHED BY TARGET THEN
							INSERT (registrationID, profileID)
							VALUES (Source.registrationID, Source.profileID)

						-- Delete old merchant profiles for this registrationID
						WHEN NOT MATCHED BY SOURCE AND Target.registrationID = @registrationID THEN
							DELETE;
					COMMIT TRAN;
					
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>

		<cflocation url="#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=rates" addtoken="no">
	</cffunction>

	<cffunction name="saveAltURL" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfscript>

			if( len(arguments.event.getValue('altRegistrationURL','')) > 300 ){
				application.objCommon.redirect("#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=registration&altURL=1&msg=3");
			}

			if( NOT isValid("URL",arguments.event.getValue('altRegistrationURL'))){
				application.objCommon.redirect("#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=registration&altURL=1&msg=2");
			}
			
			local.objEvent = CreateObject("component","model.events.events");
			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
			// remove existing registration if it is availble
			if( local.strEvent.qryEventRegMeta.recordcount ){
				this.objAdminEvent.deleteRegistration(local.strEvent.qryEventRegMeta.registrationID);
			}
			
			this.objAdminEvent.udpateAltURL(
				arguments.event.getValue('eID'),
				arguments.event.getValue('altRegistrationURL'));
			
			application.objCommon.redirect("#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=registration&msg=1");
		</cfscript>
	</cffunction>

	<cffunction name="deleteRegistration" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			/* PAGE SECURITY ---------------------------------------------------------------------------- */
			if( NOT local.rc.mc_adminToolInfo.myRights.deleteEvent ){
				application.objCommon.redirect('#this.link.message#&message=1');
			}
			/* ------------------------------------------------------------------------------------------ */
			this.objAdminEvent.deleteRegistration(arguments.event.getValue('registrationID'));
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cflocation url="#this.link.editEvent#&eID=#arguments.event.getValue('eID')#" addtoken="False">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="emailRegistrations" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.objEventReg = CreateObject("component","eventReg");
		local.objEvent = CreateObject("component","model.events.events");
		local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);

		if (val(arguments.event.getValue('eID',0)) lte 0 and NOT arguments.event.getValue('mc_adminToolInfo.myRights.editEvent'))
			application.objCommon.redirect('#this.link.message#&message=1');
		</cfscript>
			
		<cfswitch expression="#arguments.event.getValue('emailAction','')#">
			<cfcase value="emailReg">
				<cfsetting requesttimeout="600">
				<!--- replace line breaks with brs and escape html --->
				<cfset local.customText = replace(htmlEditFormat(arguments.event.getTrimValue('customtext','')),chr(10),"<br/>","ALL")>
				<cfset local.emailsubject = arguments.event.getTrimValue('emailsubject','')>

				<cfset local.qryRegistrants = this.objAdminEvent.getRegistrantsFromFilters(event=arguments.event, mode="regTabDownload")>

				<cfloop query="local.qryRegistrants">
					<cftry>
						<cfset local.strEmailConfirmation = local.objEventReg.generateConfirmationEmail(registrantID=local.qryRegistrants.registrantID, emailMode="registrant", customText=local.customText, emailsubject=local.emailsubject, siteid=arguments.event.getValue('mc_siteinfo.siteid'))>
						<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
							<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
								emailto=local.strEmailConfirmation.arrEmailTo,
								emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
								emailsubject=local.strEmailConfirmation.mailCollection.subject,
								emailtitle=local.strEmailConfirmation.emailTitle,
								emailhtmlcontent=local.strEmailConfirmation.emailcontent,
								emailAttachments=[],
								siteID=arguments.event.getValue('mc_siteinfo.siteid'),
								memberID=local.strEmailConfirmation.memberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
								sendingSiteResourceID=this.siteResourceID
							)>
						</cfif>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch)>
					</cfcatch>
					</cftry>
				</cfloop>
				<cfsavecontent variable="local.data">
					<cfoutput>
						<script language="javascript">
							top.$("##MCModalFooter").toggleClass('d-flex d-none');
							window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
						</script>
						<div class="p-3">
							<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
								<span class="font-size-lg d-block d-40 mr-2 text-center">
									<i class="fa-solid fa-circle-check"></i>
								</span>
								<span>Confirmations have been sent.</span>
							</div>
						</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfsavecontent variable="local.data">
					<cfinclude template="frm_massEmailRegistrations.cfm">
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
		
	<!--- RATE FUNCTIONS --->
	<cffunction name="addRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.qryGetRateGroupings = this.objAdminEvent.getGroupsbyRegistrationID(arguments.event.getValue('registrationID'));
			local.qryRegistration = CreateObject("component","registration").getRegistrationByRegistrationID(registrationid=arguments.event.getValue('registrationID'));
			local.qryTicketPackagesForRate = this.objAdminEvent.getTicketPackagesForRate(registrationID=arguments.event.getValue('registrationID'), rateID=0);
		
			arguments.event.setValue('rateid',0);
			arguments.event.setValue('rateGroupingID',0);
			arguments.event.setValue('rateName','');
			arguments.event.setValue('reportCode','');
			arguments.event.setValue('rateGLAccountID',0);
			arguments.event.setValue('GLAccountPath','');
			arguments.event.setValue('rate','');
			arguments.event.setValue('isHidden',0);
			arguments.event.setValue('ratesAvailableList','');
			arguments.event.setValue('freeRateDisplay','$0.00');
			arguments.event.setValue('rateMessage','');
			arguments.event.setValue('rateMessageDisplay',0);
			
			// convert times from central (how stored in db) to default timezone of site
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			
			local.formlink = this.link.saveRate & "&registrationID=" & arguments.event.getValue('registrationID');
			
			<!--- Setup GL Account Widget for Revenue GL (Optional) --->
			local.strRevenueGLAcctWidgetData = {
				"label": "Revenue GL Override",
				"btnTxt": "Choose GL Account",
				"glatid": 3,
				"widgetMode": "GLSelector",
				"idFldName": "evRateGLAccountID",
				"idFldValue": val(arguments.event.getValue('rateGLAccountID')),
				"pathFldValue": arguments.event.getValue('GLAccountPath',''),
				"pathNoneTxt": "(No account selected; uses event's designated GL Account.)",
				"clearBtnTxt": "Clear Selected GL Account"
			};
			local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
		</cfscript>

		<!--- sub events and rates for rate mapping --->
		<cfquery name="local.qrySubEvents" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;

			select se.subeventID, et.startTime, cl.contentTitle as eventName, subRate.rateID, subRate.rateName, null as mapID
			from dbo.ev_registration as r
			inner join dbo.ev_subEvents as se on se.parentEventID = r.eventID and se.forceSelect = 1
			inner join dbo.ev_events as sube on sube.siteID = @siteID and sube.eventID = se.eventID and sube.siteID = r.siteID and sube.status = 'A' 
			inner join dbo.cms_contentLanguages as cl on cl.contentID = sube.eventContentID and cl.languageID = 1
			inner join dbo.ev_times as et on et.eventID = sube.eventID and et.timeZoneID = 3
			inner join dbo.ev_registration as subR on subR.siteID = @siteID and subR.eventID = sube.eventID and subR.status = 'A' 
			inner join dbo.ev_rates as subRate on subRate.registrationID = subR.registrationID and subRate.parentRateID is null
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = subRate.siteResourceID and sr.siteResourceStatusID = 1
			where r.siteID = @siteID 
			and r.registrationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrationID')#">
			order by et.startTime, cl.contentTitle, sube.eventID, subRate.rateOrder;
		</cfquery>
		
		<cfset local.qryRegistrationSchedule = this.objAdminEvent.getRegistrationSchedule(siteID=arguments.event.getValue('mc_siteInfo.siteid'), registrationID=arguments.event.getValue('registrationID'))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rates.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.qryRate = this.objAdminEvent.getRateByRateID(arguments.event.getValue('rateID',0));
			local.qryGetRateGroupings = this.objAdminEvent.getGroupsbyRegistrationID(arguments.event.getValue('registrationID'));
			local.qryTicketPackagesForRate = this.objAdminEvent.getTicketPackagesForRate(registrationID=arguments.event.getValue('registrationID'), rateID=arguments.event.getValue('rateID',0));

			arguments.event.setValue('rateid',val(local.qryRate.rateid));
			arguments.event.setValue('rateGroupingID',val(local.qryRate.rateGroupingID));
			arguments.event.setValue('rateName',local.qryRate.rateName);
			arguments.event.setValue('reportCode',local.qryRate.reportCode);
			arguments.event.setValue('rate',local.qryRate.rate);
			arguments.event.setValue('isHidden',local.qryRate.isHidden);
			arguments.event.setValue('rateUID',local.qryRate.uid);
			arguments.event.setValue('freeRateDisplay',local.qryRate.freeRateDisplay);
			arguments.event.setValue('rateMessage',local.qryRate.rateMessage);
			arguments.event.setValue('rateMessageDisplay',local.qryRate.rateMessageDisplay);

			arguments.event.setValue('rateGLAccountID',val(local.qryRate.GLAccountID));
			local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryRate.GLAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			arguments.event.setValue('GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded);

			// convert times from central (how stored in db) to default timezone of site
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));

			local.formlink = this.link.saveRate;

			<!--- Setup GL Account Widget for Revenue GL (Optional) --->
			local.strRevenueGLAcctWidgetData = {
				"label": "Revenue GL Override",
				"btnTxt": "Choose GL Account",
				"glatid": 3,
				"widgetMode": "GLSelector",
				"idFldName": "evRateGLAccountID",
				"idFldValue": val(arguments.event.getValue('rateGLAccountID')),
				"pathFldValue": arguments.event.getValue('GLAccountPath',''),
				"pathNoneTxt": "(No account selected; uses event's designated GL Account.)",
				"clearBtnTxt": "Clear Selected GL Account"
			};
			local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
		</cfscript>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryBulkRates">
			select r.rateID, r.bulkQty, r.rate
			from dbo.ev_rates r
			inner join dbo.cms_siteResources as sr on sr.siteResourceID = r.siteResourceID and sr.siteResourceStatusID = 1
			where r.parentRateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryRate.rateid)#">
		</cfquery>
		
		<!--- sub events and rates for rate mapping --->
		<cfquery name="local.qrySubEvents" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteid')#">;

			select se.subeventID, et.startTime, cl.contentTitle as eventName, subRate.rateID, subRate.rateName, rm.mapID
			from dbo.ev_registration as r
			inner join dbo.ev_subEvents as se on se.parentEventID = r.eventID and se.forceSelect = 1
			inner join dbo.ev_events as sube on sube.siteID = @siteID and sube.eventID = se.eventID and sube.status = 'A'
			inner join dbo.cms_contentLanguages as cl on cl.contentID = sube.eventContentID and cl.languageID = 1
			inner join dbo.ev_times as et on et.eventID = sube.eventID and et.timeZoneID = 3
			inner join dbo.ev_registration as subR on subR.siteID = @siteID and subR.eventID = sube.eventID and subR.status = 'A'
			inner join dbo.ev_rates as subRate on subRate.registrationID = subR.registrationID and subRate.parentRateID is null
			inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = subRate.siteResourceID and sr.siteResourceStatusID = 1
			left outer join dbo.ev_rateMappings as rm on rm.subEventID = se.subEventID 
				and rm.parentRateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryRate.rateid)#">
				and rm.subRateID = subRate.rateID
			where r.siteID = @siteID
			and r.registrationID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('registrationID')#">
			order by et.startTime, cl.contentTitle, sube.eventID, subRate.rateOrder;
		</cfquery>
		
		<cfset local.qryRegistrationSchedule = this.objAdminEvent.getRegistrationSchedule(siteID=arguments.event.getValue('mc_siteInfo.siteid'), registrationID=arguments.event.getValue('registrationID'))>

		<cfquery name="local.qryRatesAvailable" datasource="#application.dsn.membercentral.dsn#">
			select scheduleID from dbo.ev_ratesAvailable where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryRate.rateid)#">;
		</cfquery>

		<cfset arguments.event.setValue('ratesAvailableList',valueList(local.qryRatesAvailable.scheduleID))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rates.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
	
			local.eventID = arguments.event.getValue('eventID',0);
			local.rateID = arguments.event.getValue('rateID',0);
			local.frmFields = arguments.event.getValue('fieldnames','');

			local.arrPackages = arrayNew(1);
			for (local.thisEl in arguments.event.getCollection()) {
				if (left(local.thisEl,11) eq "packageQty_") {
					local.thisTicketPackageID = getToken(local.thisEl,2,"_");
					if (val(arguments.event.getValue('packageQty_#local.thisTicketPackageID#')) gt 0) {
						local.strTemp = { TicketPackageID=local.thisTicketPackageID, quantity=val(arguments.event.getValue('packageQty_#local.thisTicketPackageID#')) };
						arrayAppend(local.arrPackages,local.strTemp);
					}
				}
			}

			if (arguments.event.getValue('rateid',0) is 0) {
				local.retValue = this.objAdminEvent.insertRate(registrationID=arguments.event.getValue('registrationID',0), 
						rateGroupingID=arguments.event.getValue('rateGroupingID',0), 
						GLAccountID=arguments.event.getTrimValue('evRateGLAccountID',0), 
						rateName=arguments.event.getTrimValue('rateName',''),
						reportCode=arguments.event.getTrimValue('reportCode',''), 
						rate=replace(arguments.event.getValue('rate',0),'$','','ALL'),
						freeRateDisplay=arguments.event.getTrimValue('freeRateDisplay',''),
						rateMessage=arguments.event.getTrimValue('rateMessage',''),
						rateMessageDisplay=arguments.event.getTrimValue('rateMessageDisplay',0),
						isHidden=arguments.event.getValue('isHidden',0), 
						parentRateID=0, 
						qty=1, 
						scheduleID=arguments.event.getValue('scheduleID',''),
						arrPackages=local.arrPackages,
						excludeTicketPackageIDList=arguments.event.getValue('excludeTicketPackages',''));
				local.rateID = local.retValue.rateID;
			} else {
				this.objAdminEvent.updateRate(rateID=arguments.event.getValue('rateID',0), 
						rateGroupingID=arguments.event.getValue('rateGroupingID',0),
						GLAccountID=arguments.event.getTrimValue('evRateGLAccountID',0), 
						rateName=arguments.event.getTrimValue('rateName',''), 
						reportCode=arguments.event.getTrimValue('reportCode',''), 
						rate=replace(arguments.event.getValue('rate',0),'$','','ALL'),
						freeRateDisplay=arguments.event.getTrimValue('freeRateDisplay',''),
						rateMessage=arguments.event.getTrimValue('rateMessage',''),
						rateMessageDisplay=arguments.event.getTrimValue('rateMessageDisplay',0),
						isHidden=arguments.event.getValue('isHidden',0), 
						qty=1, 
						scheduleID=arguments.event.getValue('scheduleID',''),
						rateUID=arguments.event.getTrimValue('rateUID',''),
						arrPackages=local.arrPackages,
						excludeTicketPackageIDList=arguments.event.getValue('excludeTicketPackages',''));
			}
		</cfscript>
		
		<!--- handle subevent rate mappings --->
		<cfif val(local.rateID)>
			<cfquery name="local.qryDeleteSubEventRateMapping" datasource="#application.dsn.membercentral.dsn#">
				delete rm
				from dbo.ev_rates as r
				inner join dbo.ev_registration as reg on reg.registrationID = r.registrationID 
					and reg.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				inner join dbo.ev_subEvents as se on se.parentEventID = reg.eventID
				inner join dbo.ev_rateMappings as rm on rm.subEventID = se.subEventID
				where rm.parentRateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
			</cfquery>
		</cfif>
		<cfloop list="#arguments.event.getValue('rateMap','')#" index="local.thisRateMap">
			<cfset local.thisRateMapSubEID = getToken(local.thisRateMap,1,"|")>
			<cfset local.thisRateMapSubRID = getToken(local.thisRateMap,2,"|")>
			<cfquery name="local.qryInsertSubEventRateMapping" datasource="#application.dsn.membercentral.dsn#">
				insert into dbo.ev_rateMappings (subEventID, parentRateID, subRateID)
				values (<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRateMapSubEID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">, <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.thisRateMapSubRID#">)
			</cfquery>
		</cfloop>

		<!--- Bulk Rates --->
		<cfif val(local.rateID)>
			<cfloop list="#arguments.event.getValue('delBR','')#" index="local.delRateID">
				<cfset this.objAdminEvent.deleteRate(mcproxy_siteID=arguments.event.getValue('mc_siteinfo.siteid'), eventID=local.eventID, rateID=local.delRateID)>
			</cfloop>
		
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRateValues">
				select registrationID, isNull(rateGroupingID,0) as rateGroupingID, isNull(GLAccountID,0) as GLAccountID, rateName, 
					reportCode, rate, freeRateDisplay, isHidden, rateMessage, rateMessageDisplay, isNull(parentRateID,0) as parentRateID
				from dbo.ev_rates
				where rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#">
			</cfquery>
			<cfloop list="#local.frmFields#" index="local.thisField">
				<cfset local.newIdx = findNoCase('NEWBULKRATE_QTY_', local.thisField)>
				<cfset local.existIdx = findNoCase('EXISTBULKRATE_QTY_', local.thisField)>
				<cfif (local.newIdx eq 1)>
					<cfset local.brID = right(local.thisField, Len(local.thisField)-Len('NEWBULKRATE_QTY_'))>

					<cfset local.brQty = arguments.event.getValue(local.thisField, 0)>
					<cfset local.brRate = arguments.event.getValue('NEWBULKRATE_RATE_#local.brID#',0.00)>
					
					<cfset this.objAdminEvent.insertRate(registrationID=local.qryRateValues.registrationID, rateGroupingID=local.qryRateValues.rateGroupingID,
														GLAccountID=local.qryRateValues.GLAccountID, rateName=local.qryRateValues.rateName, 
														reportCode=local.qryRateValues.reportCode, rate=replace(local.brRate,'$','','ALL'), 
														freeRateDisplay=local.qryRateValues.freeRateDisplay, rateMessage=local.qryRateValues.rateMessage, 
														rateMessageDisplay=local.qryRateValues.rateMessageDisplay, isHidden=local.qryRateValues.isHidden, 
														parentRateID=local.rateID, qty=local.brQty, scheduleID=arguments.event.getValue('scheduleID',''), 
														arrPackages=arrayNew(1), excludeTicketPackageIDList='')>
						
				<cfelseif local.existIdx eq 1>
					<cfset local.brID = right(local.thisField, Len(local.thisField)-Len('EXISTBULKRATE_QTY_'))>

					<cfset local.brQty = arguments.event.getValue(local.thisField, 0)>
					<cfset local.brRate = arguments.event.getValue('EXISTBULKRATE_RATE_#local.brID#',0.00)>
						
					<cfset this.objAdminEvent.updateRate(rateID=local.brID, rateGroupingID=local.qryRateValues.rateGroupingID,
											 			GLAccountID=local.qryRateValues.GLAccountID, rateName=local.qryRateValues.rateName, 
											  			reportCode=local.qryRateValues.reportCode, rate=replace(local.brRate,'$','','ALL'),
											 			freeRateDisplay=local.qryRateValues.freeRateDisplay, rateMessage=local.qryRateValues.rateMessage, 
											 			rateMessageDisplay=local.qryRateValues.rateMessageDisplay, isHidden=local.qryRateValues.isHidden, 
											 			qty=local.brQty, scheduleID=arguments.event.getValue('scheduleID',''), rateUID='', arrPackages=arrayNew(1), 
											 			excludeTicketPackageIDList='')>
				</cfif>
			</cfloop>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadRatesTable();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="copyRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.rateID = int(val(arguments.event.getValue('rateID',0)))>
		<cfset local.registrationID = int(val(arguments.event.getValue('registrationID',0)))>
		<cfset local.qryGetRateGroupings = this.objAdminEvent.getGroupsbyRegistrationID(local.registrationID)>
	
		<cfif local.rateID eq 0 OR local.registrationID eq 0>
			<cflocation url="#this.link.editEvent#&eID=#arguments.event.getValue('eID')#&tab=rates" addtoken="no">
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRate">
			select rateName
			from dbo.ev_rates
			where rateID = #local.rateID#
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_event_ratesCopy.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveCopyRate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.rateID = int(val(arguments.event.getValue('rateID',0)))>
		<cfset local.registrationID = int(val(arguments.event.getValue('registrationID',0)))>
		<cfset local.rateName = arguments.event.getTrimValue('ratename','')>
		<cfset local.rateGroupingID = int(val(arguments.event.getValue('rateGroupingID',0)))>		
		<cfset local.copyPerms = int(val(arguments.event.getValue('selCopyPerms',0)))>

		<cfif local.rateID neq 0 AND local.registrationID neq 0>
			<cfset this.objAdminEvent.copyRateByEventRateID(siteID=arguments.event.getValue('mc_siteinfo.siteID'), parentRateID = local.rateID, registrationID = local.registrationID, rateName = local.rateName, rateGroupingID = local.rateGroupingID, copyPerms = local.copyPerms)>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					top.reloadRatesTable();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<!--- REGISTRANTS/RSVP FUNCTIONS --->
	<cffunction name="addRSVP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();

			local.objEvent = CreateObject("component","model.events.events");
			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);			

			arguments.event.setValue('cID',local.strEvent.qryEventMeta.calendarID);
			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteid'), calendarID=arguments.event.getValue('cID'));
			local.urlString = this.link.insertRSVP & "&cID=" & arguments.event.getValue('cID') & "&eID=" & arguments.event.getValue('eID') & "&tab=registrants";
			
			local.qryOrgSettings = CreateObject("component","model.admin.organization.organization").getSettings(orgID=arguments.event.getValue('mc_siteinfo.orgid'));
			local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'));

			arguments.event.paramValue('firstName','');
			arguments.event.paramValue('lastName','');
			arguments.event.paramValue('email','');
			arguments.event.paramValue('phone','');
			arguments.event.paramValue('salutation','');
			arguments.event.paramValue('company','');

			// Build breadCrumb Trail 
			if( arguments.event.getValue('bc') EQ "c" ){
				appendBreadCrumbs(arguments.event,{ link='#this.link.listCalEvents#&cID=#arguments.event.getValue('cID',0)#', text=encodeForHTML(local.qryCalendar.calendarName) });
			} else {
				appendBreadCrumbs(arguments.event,{ link='#this.link.listEvents#', text="All Site Events" });
			}
			appendBreadCrumbs(arguments.event,{ link='#this.link.editEvent#&cID=#arguments.event.getValue('cID',0)#&eID=#arguments.event.getValue('eID')#&tab=registrants', text=len(local.strEvent.qryEventMeta.eventContentTitle) GT 60 ? encodeForHTML('#left(local.strEvent.qryEventMeta.eventContentTitle,60)#...') : encodeForHTML(local.strEvent.qryEventMeta.eventContentTitle) });
			appendBreadCrumbs(arguments.event,{ link='', text="Add RSVP" });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_rsvp.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="insertRSVP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			local.objRegistration 	= CreateObject("component","model.admin.events.registration");
			/* GET EVENT INFORMATION -------------------------------------------------------------------- */
			local.regInfo = local.objRegistration.getRegistrationInfo(arguments.event.getValue('eID'),this.languageID);
			/* ------------------------------------------------------------------------------------------ */
			local.update = this.objAdminEvent.insertRSVP(
				local.regInfo.registrationID,
				arguments.event.getValue('salutation'),
				arguments.event.getValue('firstName'),
				arguments.event.getValue('lastName'),
				arguments.event.getValue('company'),
				arguments.event.getValue('email'),
				arguments.event.getValue('phone')
			);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadRSVPRegistrantTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editRSVP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objEvent = CreateObject("component","model.events.events");
			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);			
			arguments.event.setValue('cID',local.strEvent.qryEventMeta.calendarID);
			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteid'), calendarID=arguments.event.getValue('cID'));
			local.urlString = this.link.updateRSVP & "&eID=" & arguments.event.getValue('eID');
			
			local.qryRegistrant	= this.objAdminEvent.getRSVP(arguments.event.getValue('rsvpID',0));
			
			local.qryOrgSettings = CreateObject("component","model.admin.organization.organization").getSettings(orgID=arguments.event.getValue('mc_siteinfo.orgid'));
			local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'));
			
			arguments.event.paramValue('firstName',local.qryRegistrant.firstName);
			arguments.event.paramValue('lastName',local.qryRegistrant.lastName);
			arguments.event.paramValue('email',local.qryRegistrant.email);
			arguments.event.paramValue('phone',local.qryRegistrant.phone);
			arguments.event.paramValue('salutation',local.qryRegistrant.salutation);
			arguments.event.paramValue('company',local.qryRegistrant.company);

			// Build breadCrumb Trail 
			if( arguments.event.getValue('bc') EQ "c" ){
				appendBreadCrumbs(arguments.event,{ link='#this.link.listCalEvents#&cID=#arguments.event.getValue('cID',0)#', text=encodeForHTML(local.qryCalendar.calendarName) });
			} else {
				appendBreadCrumbs(arguments.event,{ link='#this.link.listEvents#', text="All Site Events" });
			}
			appendBreadCrumbs(arguments.event,{ link='#this.link.editEvent#&cID=#arguments.event.getValue('cID',0)#&eID=#arguments.event.getValue('eID')#&tab=registrants', text=len(local.strEvent.qryEventMeta.eventContentTitle) GT 60 ? encodeForHTML('#left(local.strEvent.qryEventMeta.eventContentTitle,60)#...') : encodeForHTML(local.strEvent.qryEventMeta.eventContentTitle) });
			appendBreadCrumbs(arguments.event,{ link='', text="Edit RSVP" });
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_rsvp.cfm">
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="updateRSVP" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();
			/* ------------------------------------------------------------------------------------------ */
			local.update = this.objAdminEvent.udpateRSVP(
				arguments.event.getValue('rsvpID'),
				arguments.event.getValue('salutation'),
				arguments.event.getValue('firstName'),
				arguments.event.getValue('lastName'),
				arguments.event.getValue('company'),
				arguments.event.getValue('email'),
				arguments.event.getValue('phone')
			);
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadRSVPRegistrantTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="addReg" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			local.objEvent = CreateObject("component","model.events.events");
			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);			
			arguments.event.setValue('cID',local.strEvent.qryEventMeta.calendarID);
			
			local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteid'), calendarID=arguments.event.getValue('cID'));
			local.urlString = "&cID=" & arguments.event.getValue('cID') & "&eID=" & arguments.event.getValue('eID');
				
			arguments.event.setValue('mainurl',this.link.addReg & local.urlString);
			arguments.event.setValue('mainregurl',this.link.addReg & local.urlString);
			arguments.event.setValue('removeRegistranturl',this.link.removeRegistrant);
			arguments.event.setValue('loadTicketsurl',this.link.manageRegTickets);
			arguments.event.setValue('loadTicketSelectionsurl',this.link.manageRegTicketSelections);
			arguments.event.setValue('loadConfirmationurl',this.link.loadRegConfirmation);
			if (arguments.event.getValue('bc') eq 'm') {
				arguments.event.setValue('returnregurl',this.link.editMember & "&memberID=#arguments.event.getValue('mid',0)#&tab=events");
			} else {
				arguments.event.setValue('returnregurl',this.link.editEvent & local.urlString & "&tab=registrants");
			}

			// Build breadCrumb Trail 
			if( arguments.event.getValue('bc') EQ "c" ){
				appendBreadCrumbs(arguments.event,{ link='#this.link.listCalEvents#&cID=#arguments.event.getValue('cID',0)#', text=encodeForHTML(local.qryCalendar.calendarName) });
			} else {
				appendBreadCrumbs(arguments.event,{ link='#this.link.listEvents#', text="All Site Events" });
			}
			appendBreadCrumbs(arguments.event,{ link='#this.link.editEvent#&cID=#arguments.event.getValue('cID',0)#&eID=#arguments.event.getValue('eID')#&tab=registrants', text=len(local.strEvent.qryEventMeta.eventContentTitle) GT 60 ? encodeForHTML('#left(local.strEvent.qryEventMeta.eventContentTitle,60)#...') : encodeForHTML(local.strEvent.qryEventMeta.eventContentTitle) });
			if (NOT arguments.event.valueExists('mid'))	appendBreadCrumbs(arguments.event,{ link='', text="Add Registrant" });
			else appendBreadCrumbs(arguments.event,{ link='', text="Edit Registrant" });
		</cfscript>
		<cfset local.data = CreateObject("component","eventReg").doEventReg(event=arguments.event,strEvent=local.strEvent)>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="removeReg" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.defaultCurrencyType = "">
		<cfif arguments.event.getValue('mc_siteInfo.showCurrencyType') is 1>
			<cfset local.defaultCurrencyType = " #arguments.event.getValue('mc_siteInfo.defaultCurrencyType')#">
		</cfif>

		<cfset local.removeMode = "SingleRegistrant">

		<!--- get reg info --->
		<!--- are there any payments applied to this registration? --->
		<cfquery name="local.qryRegLookup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			select top 1 r.registrantID, isnull(m2.firstname + ' ','') + case when o.hasMiddleName = 1 then isnull(m2.middlename + ' ','') else '' end + isnull(m2.lastname,'') as registrantName, 
				m2.membernumber, m.activeMemberID as memberID, r.rateID, e.eventID, e.siteResourceID as eventSRID, ai.siteResourceID as calendarSRID
			from dbo.ev_registrants as r
			inner join dbo.ev_registration as er on er.siteID = @siteID and er.registrationID = r.registrationID
			inner join dbo.ev_events as e on e.siteID = @siteID and e.eventID = er.eventID
			INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID and ce.calendarID = ce.sourceCalendarID
			INNER JOIN dbo.ev_calendars as cal on cal.siteID = @siteID and cal.calendarID = ce.calendarID
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = cal.applicationInstanceID
			inner join dbo.ams_members as m on m.memberid = r.memberid
			inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
			inner join dbo.organizations as o on o.orgID = m.orgID
			where r.registrantID = <cfqueryparam value="#arguments.event.getvalue('registrantid',0)#" cfsqltype="CF_SQL_INTEGER">;
		</cfquery>

		<cfquery name="local.qryGetRateAndEventGLAccountID" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @RateGLAccountID int, @EventGLAccountID int;

			select @RateGLAccountID = GLAccountID 
			from dbo.ev_rates 
			where rateID = <cfqueryparam value="#val(local.qryRegLookup.rateID)#" cfsqltype="CF_SQL_INTEGER">;
			
			select @EventGLAccountID = GLAccountID 
			from dbo.ev_events 
			where eventID = <cfqueryparam value="#val(local.qryRegLookup.eventID)#" cfsqltype="CF_SQL_INTEGER">;
			
			select coalesce(@RateGLAccountID,@EventGLAccountID) as GLAccountID;
		</cfquery>

		<cfset arguments.event.setValue('glAccountID',val(local.qryGetRateAndEventGLAccountID.glAccountID))>
		<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryGetRateAndEventGLAccountID.glAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		
		<cfset arguments.event.setValue('GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded)>
		<!--- Setup GL Account Widget for Revenue GL --->
		<cfset local.strRevenueGLAcctWidgetData = {
			"label": "GL Account for Cancellation Fee",
			"btnTxt": "Choose GL Account",
			"glatid": 3,
			"widgetMode": "GLSelector",
			"idFldName": "evGLAccountID",
			"idFldValue": val(arguments.event.getValue('GLAccountID',0)),
			"pathFldValue": arguments.event.getValue('GLAccountPath',''),
			"pathNoneTxt": "(no account selected)"
		}>
		<cfset local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_registrantRemove.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="removeFilteredRegistrant" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.massRemoveRegistrants = buildCurrentLink(arguments.event,"massRemoveRegistrants") & "&mode=stream">

		<cfset local.defaultCurrencyType = "">
		<cfif arguments.event.getValue('mc_siteInfo.showCurrencyType') is 1>
			<cfset local.defaultCurrencyType = " #arguments.event.getValue('mc_siteInfo.defaultCurrencyType')#">
		</cfif>

		<cfset local.removeMode = "MassRegistrants">

		<cfquery name="local.qryRegLookup" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			DECLARE @eventID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eID')#">;
			DECLARE @eventSRID int, @calendarSRID int;

			SELECT @eventSRID = e.siteResourceID, @calendarSRID = ai.siteResourceID
			FROM dbo.ev_events AS e
			INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID and ce.calendarID = ce.sourceCalendarID
			INNER JOIN dbo.ev_calendars AS c ON c.calendarID = ce.calendarID AND c.siteID = e.siteID
			inner join dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = c.applicationInstanceID
			INNER JOIN dbo.sites s ON s.siteID = e.siteID
			WHERE e.eventID = @eventID
			AND e.siteID = @siteID;

			SELECT @eventID AS eventID, @eventSRID AS eventSRID, @calendarSRID AS calendarSRID, 0 as registrantID, 0 as memberID;
		</cfquery>

		<cfquery name="local.qryGetRateAndEventGLAccountID" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @EventGLAccountID int;

			select @EventGLAccountID = GLAccountID 
			from dbo.ev_events 
			where eventID = <cfqueryparam value="#val(local.qryRegLookup.eventID)#" cfsqltype="CF_SQL_INTEGER">;
			
			select @EventGLAccountID as GLAccountID;
		</cfquery>

		<cfset arguments.event.setValue('glAccountID',val(local.qryGetRateAndEventGLAccountID.glAccountID))>
		<cfset local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryGetRateAndEventGLAccountID.glAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'))>
		
		<cfset arguments.event.setValue('GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded)>
		<!--- Setup GL Account Widget for Revenue GL --->
		<cfset local.strRevenueGLAcctWidgetData = {
			"label": "GL Account for Cancellation Fee",
			"btnTxt": "Choose GL Account",
			"glatid": 3,
			"widgetMode": "GLSelector",
			"idFldName": "evGLAccountID",
			"idFldValue": val(arguments.event.getValue('GLAccountID',0)),
			"pathFldValue": arguments.event.getValue('GLAccountPath',''),
			"pathNoneTxt": "(no account selected)"
		}>
		<cfset local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_registrantRemove.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massRemoveRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any" required="true">

		<cfset var local = structNew()>
		<cftry>
			<cfset local.tmpEventRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.event.getValue('eventSRID'), memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
			<cfset local.tmpCalendarRights = application.objSiteResource.buildRightAssignments(siteResourceID=arguments.event.getValue('calendarSRID'), memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
			<cfif NOT local.tmpEventRights.EditRegistrants AND NOT local.tmpCalendarRights.EditRegistrantsByDefault>
 				<cfthrow message="invalid request">
 			</cfif>

			<cfset local.qryRegistrants = this.objAdminEvent.getRegistrantsFromFilters(event=arguments.event, mode='massRemoveRegistrants')>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
				top.reloadPage();
				$('##divRemoveRegLoading').hide();
				$('##divRemoveRegForm').show();
				$("##warningMsg").hide();
				$("##successMsg").removeClass('d-none').delay(5000).fadeOut('slow', function() {
					top.MCModalUtils.hideModal();
				});
				</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="cleanupInvoicesReg" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.formLink = buildCurrentLink(arguments.event,"doCleanupInvoicesReg") & "&mode=stream">

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_cleanupInvoicesReg.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doCleanupInvoicesReg" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>
		<cfset local.registrantID = arguments.event.getValue('rID', 0)>
		<cfset local.AROption = arguments.event.getValue('rdoAR','C')>
		
		<cfquery name="local.qryCleanupInvReg" datasource="#application.dsn.membercentral.dsn#">
			EXEC dbo.ev_cleanupInvoicesRegistrant
				@registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.registrantID#">,
				@recordedOnSiteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">,
				@recordedByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
				@statsSessionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.statsSessionID#">,
				@AROption = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.AROption#">;
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script language="javascript">
					if (typeof top.reloadEventGrid == 'function') top.reloadEventGrid();
					else if (typeof top.reloadPage == 'function') top.reloadPage();
					top.MCModalUtils.hideModal();
				</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sendConfirmation" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.objEvent = CreateObject("component","model.events.events");
			local.objEventReg = CreateObject("component","model.admin.events.eventReg");

			local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
			local.registrantEmail = application.objMember.getMainEmail(arguments.event.getValue('mid',0)).email;
			local.registrantOverrideEmail = this.objAdminEvent.getRegistrantOverrideEmail(registrantID=arguments.event.getValue('registrantID'));
		</cfscript>
		
		<cfswitch expression="#arguments.event.getValue('confaction','')#">
			<cfcase value="emailConf">
				<!--- replace line breaks with brs and escape html --->
				<cfset local.customText = replace(htmlEditFormat(arguments.event.getTrimValue('customtext','')),chr(10),"<br/>","ALL")>
				<!--- if 1, send registrant their version --->
				<cfif listFind(arguments.event.getValue('chkEmailRegistrant',''),1)>
					<cftry>
						<cfset local.strEmailConfirmation = local.objEventReg.generateConfirmationEmail(registrantID=arguments.event.getValue('registrantID'), emailMode="registrant", customText=local.customText, emailTo=arguments.event.getTrimValue('txtEmailRegistrant1'), siteid=arguments.event.getValue('mc_siteinfo.siteid'))>
						<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
							<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
								emailto=local.strEmailConfirmation.arrEmailTo,
								emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
								emailsubject=local.strEmailConfirmation.mailCollection.subject,
								emailtitle=local.strEmailConfirmation.emailTitle,
								emailhtmlcontent=local.strEmailConfirmation.emailcontent,
								emailAttachments=[],
								siteID=arguments.event.getValue('mc_siteinfo.siteid'),
								memberID=local.strEmailConfirmation.memberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
								sendingSiteResourceID=this.siteResourceID
							)>
						</cfif>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch)>
					</cfcatch>
					</cftry>
				</cfif>
				
				<!--- if 2, send staff their version --->
				<cfif listFind(arguments.event.getValue('chkEmailRegistrant',''),2) and len(arguments.event.getTrimValue('txtEmailRegistrant2',''))>
					<cftry>
						<cfset local.strEmailConfirmation = local.objEventReg.generateConfirmationEmail(registrantID=arguments.event.getValue('registrantID'), emailMode="staff", customText=local.customText, emailTo=arguments.event.getTrimValue('txtEmailRegistrant2'), siteid=arguments.event.getValue('mc_siteinfo.siteid'))>

						<cfif arrayLen(local.strEmailConfirmation.arrEmailTo)>
							<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
								emailto=local.strEmailConfirmation.arrEmailTo,
								emailreplyto=local.strEmailConfirmation.mailCollection.replyto,
								emailsubject=local.strEmailConfirmation.mailCollection.subject,
								emailtitle=local.strEmailConfirmation.emailTitle,
								emailhtmlcontent=local.strEmailConfirmation.emailcontent,
								emailAttachments=[],
								siteID=arguments.event.getValue('mc_siteinfo.siteid'),
								memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
								sendingSiteResourceID=this.siteResourceID
							)>
						</cfif>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch)>
					</cfcatch>
					</cftry>
				</cfif>

				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						top.$("##MCModalFooter").toggleClass('d-flex d-none');
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
					</script>
					<div class="p-3">
						<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
							<span class="font-size-lg d-block d-40 mr-2 text-center">
								<i class="fa-solid fa-circle-check"></i>
							</span>
							<span>E-mail sent.</span>
						</div>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfsavecontent variable="local.data">
					<cfinclude template="frm_emailConfirmation.cfm">
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sendRegistrantMaterials" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.hasEventDocs = this.objAdminEvent.hasEventDocumentsForAnEvent(eventID=arguments.event.getValue('eID'));

			if (NOT local.hasEventDocs)
				application.objCommon.redirect("#this.link.message#&message=1&mode=direct");
		</cfscript>
		
		<cfswitch expression="#arguments.event.getValue('sendAction','')#">
			<cfcase value="emailMaterials">
				<!--- replace line breaks with brs and escape html --->
				<cfset local.customText = replace(htmlEditFormat(arguments.event.getTrimValue('customtext','')),chr(10),"<br/>","ALL")>

				<cfset local.strEmailMaterials = this.objAdminEvent.generateMaterialsEmail(siteid=arguments.event.getValue('mc_siteinfo.siteid'), registrantID=arguments.event.getValue('registrantID'), emailTo=arguments.event.getTrimValue('additionalRegEmails',''), customText=local.customText)>
				<cfset local.emailSent = false>
				<cfif arrayLen(local.strEmailMaterials.arrEmailTo)>
					<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
						emailto=local.strEmailMaterials.arrEmailTo,
						emailreplyto=local.strEmailMaterials.replyto,
						emailsubject=local.strEmailMaterials.subject,
						emailtitle=local.strEmailMaterials.emailTitle,
						emailhtmlcontent=local.strEmailMaterials.emailcontent,
						emailAttachments=[],
						siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						memberID=local.strEmailMaterials.memberID,
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVREGMATERIALS"),
						sendingSiteResourceID=this.siteResourceID
					)>
					<cfset local.emailSent = local.strResult.success>
				</cfif>
				
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						top.$("##MCModalFooter").toggleClass('d-flex d-none');
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
					</script>
					<div class="p-3">
						<cfif local.emailSent>
							<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
								<span class="font-size-lg d-block d-40 mr-2 text-center">
									<i class="fa-solid fa-circle-check"></i>
								</span>
								<span>E-mail sent.</span>
							</div>
						<cfelse>
							<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-danger mb-0" role="alert">
								<span class="font-size-lg d-block d-40 mr-2 text-center">
									<i class="fa-solid fa-triangle-exclamation"></i>
								</span>
								<span>E-mail not sent.</span>
							</div>
						</cfif>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfscript>
					local.objEvent = CreateObject("component","model.events.events");
					local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0), siteid=arguments.event.getValue('mc_siteinfo.siteid'), languageid=this.languageID);
					local.registrantEmail = application.objMember.getMainEmail(arguments.event.getValue('mid',0)).email;
					local.registrantOverrideEmail = this.objAdminEvent.getRegistrantOverrideEmail(registrantID=arguments.event.getValue('registrantID'));

					savecontent variable="local.data" {
						include template="frm_emailMaterials.cfm";
					}
				</cfscript>
			</cfdefaultcase>
		</cfswitch>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailRegistrantMaterials" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
		var local = structNew();
		local.objEventReg = CreateObject("component","eventReg");
		local.objEvent = CreateObject("component","model.events.events");

		arguments.event.paramValue('eID',0);
		local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID'), siteid=arguments.event.getValue('mc_siteinfo.siteid'), languageid=this.languageID);
		local.hasEventDocs = this.objAdminEvent.hasEventDocumentsForAnEvent(eventID=arguments.event.getValue('eID'));

		if (NOT local.hasEventDocs)
			application.objCommon.redirect('#this.link.message#&message=1&mode=direct');
		</cfscript>
		
		<cfswitch expression="#arguments.event.getValue('emailAction','')#">
			<cfcase value="emailReg">
				<cfsetting requesttimeout="600">
				<!--- replace line breaks with brs and escape html --->
				<cfset local.customText = replace(htmlEditFormat(arguments.event.getTrimValue('customtext','')),chr(10),"<br/>","ALL")>
				<cfset local.emailSubject = arguments.event.getTrimValue('emailsubject','')>

				<cfset local.qryRegistrants = this.objAdminEvent.getRegistrantsFromFilters(event=arguments.event, mode="regTabDownload")>

				<cfloop query="local.qryRegistrants">
					<cftry>
						<cfset local.strEmailMaterials = this.objAdminEvent.generateMaterialsEmail(siteid=arguments.event.getValue('mc_siteinfo.siteid'), registrantID=local.qryRegistrants.registrantID, customText=local.customText, emailSubject=local.emailSubject)>
						<cfif arrayLen(local.strEmailMaterials.arrEmailTo)>
							<cfset local.strResult = application.objEmailWrapper.sendMailESQ(
								emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
								emailto=local.strEmailMaterials.arrEmailTo,
								emailreplyto=local.strEmailMaterials.replyto,
								emailsubject=local.strEmailMaterials.subject,
								emailtitle=local.strEmailMaterials.emailTitle,
								emailhtmlcontent=local.strEmailMaterials.emailcontent,
								emailAttachments=[],
								siteID=arguments.event.getValue('mc_siteinfo.siteid'),
								memberID=local.strEmailMaterials.memberID,
								messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVREGMATERIALS"),
								sendingSiteResourceID=this.siteResourceID
							)>
						</cfif>
					<cfcatch type="Any">
						<cfset application.objError.sendError(cfcatch=cfcatch)>
					</cfcatch>
					</cftry>
				</cfloop>
			
				<cfsavecontent variable="local.data">
					<cfoutput>
					<script language="javascript">
						top.$("##MCModalFooter").toggleClass('d-flex d-none');
						window.setTimeout(function(){ top.MCModalUtils.hideModal(); },3000);
					</script>
					<div class="p-3">
						<div class="alert d-flex align-items-center px-2 py-1 align-content-center alert-success mb-0" role="alert">
							<span class="font-size-lg d-block d-40 mr-2 text-center">
								<i class="fa-solid fa-circle-check"></i>
							</span>
							<span>E-mails sent to filtered registrants.</span>
						</div>
					</div>
					</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfdefaultcase>
				<cfsavecontent variable="local.data">
					<cfinclude template="frm_massEmailRegistrantMaterials.cfm">
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="printReg" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objEventReg = CreateObject("component","model.admin.events.eventReg")>
		<cfset local.registrantID = arguments.event.getValue('registrantID',0)>
		
		<cfset local.strEmailConfirmation = local.objEventReg.generateConfirmationEmail(registrantID=local.registrantID, emailMode="staffprint", siteid=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfif arguments.event.getValue('pdf',0) is 1>
			<cfdocument format="PDF" saveAsName="EventRegistration.pdf" margintop="1" marginbottom="1" marginright="1" marginleft="1" backgroundvisible="Yes" scale="100">
				<cfoutput>
				<html>
				<head>
					<link rel="stylesheet" type="text/css" href="#replaceNoCase(application.paths.internalPlatform.url,'*SITECODE*',arguments.event.getValue('mc_siteinfo.sitecode'))#/assets/admin/css/styles.css" />
					<style>
						body { margin:0px !important; width:8in !important; }
					</style>
				</head>
				<body>
					#local.strEmailConfirmation.emailcontent#
				</body>
				</html>
				</cfoutput>
			</cfdocument>
		<cfelse>
			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					function printIt() {
						self.location.href = '#this.link.printReg#&cID=#arguments.event.getValue('cID')#&eID=#arguments.event.getValue('eID')#&mid=#arguments.event.getValue('mid')#&registrantID=#local.registrantID#&pdf=1';
					}
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">

			<cfset local.data = local.strEmailConfirmation.emailcontent>
		</cfif>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="printBadgeRegistrant" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objDevice = CreateObject("component","model.admin.badges.device")>

		<cfset local.registrantID = arguments.event.getValue('registrantID',0)>
		<cfset local.qryGetEventDefaultBadgeDetails = this.objAdminEvent.getEventDefaultBadgeDetails(eID=arguments.event.getValue('eID',0), siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.printRegistrantBadgeLink = buildCurrentLink(arguments.event,"doPrintRegistrantBadge") & "&mode=stream">
		<cfset local.qryBadgeTemplates = local.objDevice.getCategoriesAndTemplatesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode='BTEVENTS')>
		<cfset local.qryBadgeTemplateCategories = local.objDevice.getBadgeTemplateCategories(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode='BTEVENTS')>
		<cfset local.arrBadgeDevices = local.objDevice.getBadgeDevicesForSite(mcproxy_siteID=arguments.event.getValue('mc_siteinfo.siteid')).arrDevices>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_printBadgeRegistrant.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="doPrintRegistrantBadge" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.printBadge = this.objAdminEvent.printRegistrantBadge(siteID=arguments.event.getValue('mc_siteInfo.siteid'), siteCode=arguments.event.getValue('mc_siteInfo.sitecode'),
			registrantID=arguments.event.getValue('registrantID'), deviceID=arguments.event.getValue('badgeDeviceID',0), badgeTemplateID=arguments.event.getTrimValue('badgeTemplateID',0))>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfif local.printBadge>
					<div class="alert alert-success py-2">Print request sent to printer successfully.</div>
				<cfelse>
					<div class="alert alert-danger py-2">An error occurred. Try again or contact support for assistance.</div>
				</cfif>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportRSVP" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>

		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "RSVPList-#left(ReReplaceNoCase(local.strEvent.qryEventMeta.eventContentTitle,'[^A-Z0-9]','_','ALL'),30)#.csv">

		<!--- export data --->
		<cfstoredproc procedure="ev_exportRSVPs" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#" null="No">
		</cfstoredproc>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.editEvent#&eid=#arguments.event.getValue('eID')#&tab=registrants" addtoken="no">
		</cfif>	
	</cffunction>

	<cffunction name="exportRegPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsid", selectedFieldSetName='Events Download Registrants Standard', allowBlankOption=false, inlinePreviewSectionID="divExportReg",
			fieldLabel='Member Field Set with data to include in export')>

		<cfquery name="local.qryCrossEventFields" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int, @eventAdminSiteResourceID int;
			select @usageID = dbo.fn_cf_getUsageID('EventAdmin','Event',null);
			set @eventAdminSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.eventAdminSiteResourceID#">;

			select f.fieldID, f.fieldReference
			from dbo.cf_fields as f
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			where fu.usageID = @usageID
			and f.controllingSiteResourceID = @eventAdminSiteResourceID
			and len(f.fieldReference) > 0
			and f.isActive = 1
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
					else 1 end
			order by f.fieldOrder;
		</cfquery>

		<cfif arguments.event.getValue('view','') neq "regSearch">
			<cfquery name="local.qryEventRegFields" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare @usageID int, @eventID int, @eventSRID int;
				select @usageID = dbo.fn_cf_getUsageID('Event','Registrant',null);
				set @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#">;
				
				select @eventSRID = siteResourceID 
				from dbo.ev_events
				where eventID = @eventID;

				select f.fieldID, f.fieldReference
				from dbo.cf_fields as f
				inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
				where fu.parentUsageID = @usageID
				and f.controllingSiteResourceID = @eventSRID
				and len(f.fieldReference) > 0
				and f.isActive = 1
				order by f.fieldOrder;

				set nocount off;
			</cfquery>
		<cfelse>
			<cfset local.qryEventRegFields = QueryNew("fieldID,fieldReference","integer,varchar")>
		</cfif>

		<cfset local.EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>

		<cfquery name="local.qryEventRoleFields" datasource="#application.dsn.membercentral.dsn#">
			set nocount on;

			declare @usageID int, @eventAdminSRID int, @eventRolesCTID int;
			select @usageID = dbo.fn_cf_getUsageID('EventAdmin','Role',null);
			select @eventAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EventAdminSRID#">;
			select @eventRolesCTID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@eventAdminSRID,'Event Roles');

			select c.categoryID, f.fieldID, c.categoryname + ': ' + f.fieldReference as titleOnInvoice
			from dbo.cf_fields as f 
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				and f.usageID = @usageID
				and f.controllingSiteResourceID = @eventAdminSRID
			inner join dbo.cms_categories as c on c.categoryID = f.detailID
			where c.categoryTreeID = @eventRolesCTID
			and c.isActive = 1 
			and c.parentCategoryID is NULL
			and len(f.fieldReference) > 0
			and f.isActive = 1
			order by c.categoryID, f.fieldOrder;

			set nocount off;
		</cfquery>

		<cfset local.qryEventForms = this.objAdminEvent.getEventForms(eventID=arguments.event.getValue('eID'))>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_exportReg.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportReg" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.exportRegResult = { "success":false, "errmsg":"" }>
		
		<cftry>
			<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
			
			<!--- set vars --->
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cfset local.reportFileName = "RegList-#left(ReReplaceNoCase(local.strEvent.qryEventMeta.eventContentTitle,'[^A-Z0-9]','_','ALL'),30)#.csv">

			<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
			<cfset local.resultsFieldsetID = arguments.event.getValue('fsid',0)>

			<cfquery name="local.qryTicketPackageLabels" datasource="#application.dsn.membercentral.dsn#">
				select t.ticketID, dbo.fn_regexReplace(t.ticketName + '_Total Tickets','[^A-Za-z0-9_\- ]','') as ticketTotalLabel, 
					dbo.fn_regexReplace(t.ticketName + '_' + tp.ticketPackageName,'[^A-Za-z0-9_\- ]','') as ticketPackageNameExpanded
				from dbo.ev_ticketPackages as tp 
				inner join dbo.ev_tickets as t on t.ticketID = tp.ticketID
				inner join dbo.ev_registration as r on r.registrationID = t.registrationID 
					and r.siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteid')#">
				where r.eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#">
				order by t.sortOrder, t.ticketID, tp.sortOrder, tp.ticketPackageID
			</cfquery>

			<cfquery name="local.qryTicketLabel" dbtype="query">
				select distinct ticketID, ticketTotalLabel
				from [local].qryTicketPackageLabels
			</cfquery>

			<cfset local.qryRegistrants = this.objAdminEvent.getRegistrantsFromFilters(event=arguments.event, mode="regTabDownload")>
						
			<cfset local.EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>

			<cfquery name="local.qryExport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryExportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
					declare @orgID int, @siteID int, @eventID int, @eventSRID int, @eventRegFUID int, @eventContentID int, @eventName varchar(max), 
						@fieldSetID int, @eventReportCode varchar(15), @isOnlineMeeting bit, @eventAdminSRID int, @eventRolesCTID int, @fullsql varchar(max), 
						@swcfList varchar(max), @rqList varchar(max), @qList varchar(max), @creditColumns varchar(max), @eventRoleColumns varchar(max), 
						@outputFieldsXML xml;

					IF OBJECT_ID('tempdb..##tmpRegTrans') IS NOT NULL 
						DROP TABLE ##tmpRegTrans;
					CREATE TABLE ##tmpRegTrans (registrantID int PRIMARY KEY, totalRegFee decimal(14,2), regFeePaid decimal(14,2));

					IF OBJECT_ID('tempdb..##tmpRegFiltered') IS NOT NULL 
						DROP TABLE ##tmpRegFiltered;
					CREATE TABLE ##tmpRegFiltered (registrantID int PRIMARY KEY, eventID int, memberID int, registrationID int);

					IF OBJECT_ID('tempdb..##tmpEventRoles') IS NOT NULL 
						DROP TABLE ##tmpEventRoles;
					CREATE TABLE ##tmpEventRoles (categoryID int PRIMARY KEY, categoryName varchar(200));

					IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
						DROP TABLE ##tmpAuditLog;
					CREATE TABLE ##tmpAuditLog (registrantID int PRIMARY KEY, joinedprogram datetime, leftProgram datetime, totalTimeSpent int);

					IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
						DROP TABLE ##tmpMembers;
					IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
						DROP TABLE ##tmp_membersForFS;
					IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
						DROP TABLE ##tmp_CF_ItemIDs;
					IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
						DROP TABLE ##tmp_CF_FieldData;
					IF OBJECT_ID('tempdb..####tmpSWCF#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpSWCF#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..####tmpRQ#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpRQ#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..####tmpQ#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpQ#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..##tmpSWCF') IS NOT NULL 
						DROP TABLE ##tmpSWCF;
					IF OBJECT_ID('tempdb..##tmpRQA') IS NOT NULL 
						DROP TABLE ##tmpRQA;
					<cfif listLen(arguments.event.getValue('qID',''))>
						IF OBJECT_ID('tempdb..##tmpQA') IS NOT NULL 
							DROP TABLE ##tmpQA;
					</cfif>
					IF OBJECT_ID('tempdb..##tmpRegPackages') IS NOT NULL 
						DROP TABLE ##tmpRegPackages;
					IF OBJECT_ID('tempdb..##tmpRegPackageTotalCount') IS NOT NULL
						DROP TABLE ##tmpRegPackageTotalCount;
					IF OBJECT_ID('tempdb..##tmpRegTicketTotalCount') IS NOT NULL
						DROP TABLE ##tmpRegTicketTotalCount;
					IF OBJECT_ID('tempdb..####tmpEVExport#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpEVExport#local.tmpSuffix#;

					CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
					CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);
					CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
					CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);
					CREATE TABLE ##tmpRQA (registrantID int, titleOnInvoice varchar(max), answer varchar(max), INDEX IX_tbltmpRQA_registrantID (registrantID));
					<cfif listLen(arguments.event.getValue('qID',''))>
						CREATE TABLE ##tmpQA (registrantID int, titleOnInvoice varchar(128), answer varchar(max), INDEX IX_tbltmpQA_registrantID (registrantID));
					</cfif>

					set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
					set @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#">;
					SET @fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.resultsFieldsetID#">;
					set @eventAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EventAdminSRID#">;
					
					select @eventSRID = siteResourceID, @eventReportCode = reportCode, @eventContentID = eventContentID 
					from dbo.ev_events 
					where eventID = @eventID;

					select @eventName = contentTitle from dbo.cms_contentLanguages where contentID = @eventContentID and languageID = 1;
					select @isOnlineMeeting = isOnlineMeeting from dbo.ev_registration where eventID = @eventID AND siteID = @siteID AND status = 'A';
					select @eventRolesCTID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@eventAdminSRID,'Event Roles');
					
					-- registrants matching filters
					INSERT INTO ##tmpRegFiltered
					select r.registrantID, @eventID, m.activeMemberID, r.registrationID
					from dbo.ev_registrants as r
					inner join dbo.ams_members as m on m.memberID = r.memberID
					where r.registrantID in (0#ValueList(local.qryRegistrants.registrantID)#);

					-- event roles
					insert into ##tmpEventRoles
					select categoryID, categoryName
					from dbo.cms_categories
					where categoryTreeID = @eventRolesCTID
					and isActive = 1 
					and parentCategoryID is NULL;

					-- get online meeting audit log
					IF @isOnlineMeeting = 1 
						INSERT INTO ##tmpAuditLog
						select ml.registrantID, min(ml.dateCreated) as joinedprogram, max(ml.dateUpdated) as leftProgram, sum(ml.numSeconds) as totalTimeSpent
						from platformstatsMC.dbo.ev_onlineMeetingLog as ml
						inner join ##tmpRegFiltered as tmp on tmp.registrantID = ml.registrantID
						group by ml.registrantID;

					-- system wide event custom fields
					INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
					SELECT DISTINCT e.siteResourceID, 'CrossEvent'
					FROM ##tmpRegFiltered as tmp
					INNER JOIN dbo.ev_events as e on e.eventID = tmp.eventID;

					EXEC dbo.cf_getFieldData;

					SELECT e.eventID, replace(f.fieldReference,',','') as titleOnInvoice, fd.fieldValue AS answer
					INTO ##tmpSWCF
					FROM ##tmp_CF_FieldData AS fd
					INNER JOIN dbo.cf_fields AS f ON f.fieldID = fd.fieldID
					INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.siteResourceID = fd.itemID
					<cfif listLen(arguments.event.getValue('swcfid',''))>
						WHERE f.fieldID IN (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.event.getValue('swcfid')#">)
					<cfelse>
						WHERE 1=0
					</cfif>;

					-- system wide event custom fields pivoted
					set @swcfList = '';
					select @swcfList = COALESCE(@swcfList + ',', '') + quoteName(titleonInvoice) from ##tmpSWCF group by titleOnInvoice;
					IF left(@swcfList,1) = ','
						set @swcfList = right(@swcfList,len(@swcfList)-1);
					IF len(@swcfList) > 0 BEGIN
						set @fullsql = '
							select * 
							into ####tmpSWCF#local.tmpSuffix#
							from (
								select eventID, titleOnInvoice, answer
								from ##tmpSWCF
							) as cf
							PIVOT (min(answer) for titleonInvoice in (' + @swcfList + ')) as p ';
						EXEC(@fullsql);
					END
					ELSE
						SELECT eventID 
						INTO ####tmpSWCF#local.tmpSuffix# 
						FROM ##tmpRegFiltered 
						WHERE 0=1;

					-- truncate temp field tables
					TRUNCATE TABLE ##tmp_CF_ItemIDs;
					TRUNCATE TABLE ##tmp_CF_FieldData;

					-- role custom fields
					INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
					SELECT DISTINCT registrantID, 'EventRole'
					FROM ##tmpRegFiltered;

					EXEC dbo.cf_getFieldData;
					
					INSERT INTO ##tmpRQA (registrantID, titleOnInvoice, answer)
					SELECT fd.itemID AS registrantID, replace(tmpR.categoryName + '_' + f.fieldReference,',','') as titleOnInvoice, fd.fieldValue AS answer
					FROM ##tmpRegFiltered as registrants
					INNER JOIN dbo.ev_registrantCategories as regcat on regcat.registrantID = registrants.registrantID
					INNER JOIN ##tmpEventRoles as tmpR on tmpR.categoryID = regcat.categoryID
					INNER JOIN ##tmp_CF_FieldData as fd on fd.itemID = registrants.registrantID
					INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
						and f.detailID = regcat.categoryID
					<cfif listLen(arguments.event.getValue('rqID',''))>
						WHERE f.fieldID IN (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.event.getValue('rqID')#">)
					<cfelse>
						WHERE 1=0
					</cfif>;

					-- truncate temp field tables
					TRUNCATE TABLE ##tmp_CF_ItemIDs;
					TRUNCATE TABLE ##tmp_CF_FieldData;

					-- event custom fields
					INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
					SELECT DISTINCT registrantID, 'EventRegCustom'
					FROM ##tmpRegFiltered;

					EXEC dbo.cf_getFieldData;

					<cfif listLen(arguments.event.getValue('qID',''))>
						INSERT INTO ##tmpQA (registrantID, titleOnInvoice, answer)
						SELECT fd.itemID AS registrantID, replace(f.fieldReference,',','') as titleOnInvoice, fd.fieldValue as answer
						FROM ##tmp_CF_FieldData AS fd
						INNER JOIN dbo.cf_fields as f on f.fieldID = fd.fieldID
						WHERE f.fieldID IN (<cfqueryparam cfsqltype="cf_sql_integer" list="yes" value="#arguments.event.getValue('qID')#">)
					</cfif>;
					
					-- role custom fields pivoted
					set @rqList = '';
					select @rqList = COALESCE(@rqList + ',', '') + quoteName(titleonInvoice) from ##tmpRQA group by titleOnInvoice;
					IF left(@rqList,1) = ','
						select @rqList = right(@rqList,len(@rqList)-1);
					IF len(@rqList) > 0 BEGIN
						set @fullsql = '';
						select @fullsql = '
							select * 
							into ####tmpRQ#local.tmpSuffix#
							from (
								select registrantid, titleOnInvoice, answer
								from ##tmpRQA
							) as reg
							PIVOT (min(answer) for titleonInvoice in (' + @rqList + ')) as p ';
						EXEC(@fullsql);
					END
					ELSE
						SELECT registrantID 
						INTO ####tmpRQ#local.tmpSuffix# 
						FROM ##tmpRegFiltered 
						WHERE 0=1;

					<cfif listLen(arguments.event.getValue('qID',''))>
						-- event custom fields pivoted
						set @qList = '';
						select @qList = COALESCE(@qList + ',', '') + quoteName(titleonInvoice) from ##tmpQA group by titleOnInvoice;
						IF left(@qList,1) = ','
							select @qList = right(@qList,len(@qList)-1);
						IF len(@qList) > 0 BEGIN
							set @fullsql = '';
							select @fullsql = @fullsql + '
								select * 
								into ####tmpQ#local.tmpSuffix#
								from (
									select registrantid, titleOnInvoice, answer
									from ##tmpQA
								) as reg
								PIVOT (min(answer) for titleonInvoice in (' + @qList + ')) as p ';
							EXEC(@fullsql);
						END
						ELSE
							SELECT registrantID 
							INTO ####tmpQ#local.tmpSuffix# 
							FROM ##tmpRegFiltered 
							WHERE 0=1;
					</cfif>

					-- add credit columns to report if they exist.
					select @creditColumns = COALESCE(@creditColumns + ',', '') + quoteName(creditType)
					from (
						select distinct C.cred.value('@creditType','varchar(50)') as creditType
						from (
							SELECT cast(isnull((
								select ECT.offeringTypeID, ECT.ASTID, ect.creditValue, ca.authorityCode + '_' + cat.typeCode as creditType
								from dbo.crd_offeringTypes as ect
								inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
								inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
								inner join dbo.crd_authorities as ca on ca.authorityID = cat.authorityID
								where ect.offeringID = ec.offeringID
								FOR XML AUTO, ROOT('creditTypes'), TYPE
								),'<creditTypes/>') as xml) as eventCreditTypes
							FROM dbo.crd_offerings AS ec
							WHERE ec.eventID = @eventID
						) as t
						CROSS APPLY eventCreditTypes.nodes('//ect') as C(cred)
					) tmp;

					-- add event role columns to report if they exist.
					select @eventRoleColumns = COALESCE(@eventRoleColumns + ',', '') + quoteName(categoryName) from ##tmpEventRoles;
								
					-- get fieldset data
					INSERT INTO ##tmp_membersForFS (memberID)
					select distinct memberID
					from ##tmpRegFiltered;

					EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
						@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
						@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

					select rpi.registrantID, t.ticketID, dbo.fn_regexReplace(t.ticketName + '_Total Tickets','[^A-Za-z0-9_\- ]','') as ticketTotal, 
						dbo.fn_regexReplace(t.ticketName + '_' + tp.ticketPackageName,'[^A-Za-z0-9_\- ]','') as ticketPackageNameExpanded, 
						rpi.instanceID, tp.ticketCount
					into ##tmpRegPackages
					from dbo.ev_registrantPackageInstances rpi
					INNER JOIN ##tmpRegFiltered as rf on rf.registrantID = rpi.registrantID 
					inner join dbo.ev_ticketPackages as tp on tp.ticketPackageID = rpi.ticketPackageID
					inner join dbo.ev_tickets as t on t.ticketID = tp.ticketID
					inner join dbo.ev_registration as r on r.registrationID = t.registrationID 
						and r.siteID = @siteID 
						and r.eventID = @eventID
					where rpi.status = 'A';

					<cfif local.qryTicketPackageLabels.recordcount>
						WITH tmpRegPackageCount AS (
							select registrantID
								<cfloop query="local.qryTicketPackageLabels">
									, [#local.qryTicketPackageLabels.ticketPackageNameExpanded#]
								</cfloop>
							from ##tmpRegPackages
							pivot(count(instanceID) for ticketPackageNameExpanded in (
								<cfloop query="local.qryTicketPackageLabels">
									[#local.qryTicketPackageLabels.ticketPackageNameExpanded#]<cfif local.qryTicketPackageLabels.currentRow neq local.qryTicketPackageLabels.recordCount>, </cfif>
								</cfloop>
							)) pvt
						)
						select registrantID
							<cfloop query="local.qryTicketPackageLabels">
								, isNull(sum([#local.qryTicketPackageLabels.ticketPackageNameExpanded#]),0) as [#local.qryTicketPackageLabels.ticketPackageNameExpanded#]
							</cfloop>
						into ##tmpRegPackageTotalCount
						from tmpRegPackageCount
						group by registrantID;
					<cfelse>
						select registrantID
						into ##tmpRegPackageTotalCount
						from ##tmpRegPackages;
					</cfif>

					<cfif local.qryTicketLabel.recordcount>
						WITH tmpRegTicketCount AS (
							select registrantID
								<cfloop query="local.qryTicketLabel">
									, [#local.qryTicketLabel.ticketTotalLabel#]
								</cfloop>
							from ##tmpRegPackages
							pivot(sum(ticketCount) for ticketTotal in (
								<cfloop query="local.qryTicketLabel">
									[#local.qryTicketLabel.ticketTotalLabel#]<cfif local.qryTicketLabel.currentRow neq local.qryTicketLabel.recordCount>, </cfif>
								</cfloop>
							)) pvt
						)
						select registrantID
							<cfloop query="local.qryTicketLabel">
								, isNull(sum([#local.qryTicketLabel.ticketTotalLabel#]),0) as [#local.qryTicketLabel.ticketTotalLabel#]
							</cfloop>
						into ##tmpRegTicketTotalCount
						from tmpRegTicketCount
						group by registrantID;
					<cfelse>
						select registrantID
						into ##tmpRegTicketTotalCount
						from ##tmpRegPackages;
					</cfif>

					-- get registrant transactions by event
					IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL 
						DROP TABLE ##tmpEventsForFee; 
					IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL 
						DROP TABLE ##tmpEventsForFeeResult; 
					create table ##tmpEventsForFee (eventID int PRIMARY KEY);
					create table ##tmpEventsForFeeResult (eventID int, registrantID int, transactionID int, TransactionIDForRateAdjustment int);

					insert into ##tmpEventsForFee (eventID)
					values (@eventID);

					EXEC dbo.ev_registrantTransactionsByEventBulk;

					INSERT INTO ##tmpRegTrans (registrantID, totalRegFee, regFeePaid)
					select fees.registrantID, sum(ts.cache_amountAfterAdjustment), sum(ts.cache_activePaymentAllocatedAmount)
					from ##tmpEventsForFeeResult as fees
					inner join dbo.tr_transactionSales as ts on ts.orgID = @orgID and ts.transactionID = fees.transactionid
					group by fees.registrantID;

					<cfif val(arguments.event.getValue('evformid',0))>
						DECLARE @finalResponseTable varchar(60) = '####EVFBFinalResp' + replace(NEWID(),'-','');

						EXEC dbo.ev_getRegistrantFormResponses
							@formID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('evformid')#">,
							@onlyFormData=1,
							@finalResponseTable=@finalResponseTable;
					</cfif>
					
					-- combine with reg data 
					set @fullsql = 'SELECT ' + cast(@eventID as varchar(10)) + ' as [EventID], r.registrantID as [RegistrantID], ''' + replace(@eventName,'''','''''') + ''' as [EventTitle], ''' + replace(isNull(@eventReportCode,''),'''','''''') + ''' as [EventReportCode], 
						CASE WHEN r.status = ''D'' THEN ''Removed'' ELSE ''Active'' END as [RegistrantStatus],
						r.DATEREGISTERED as [DateRegistered], 
						CASE WHEN r.status <> ''D'' AND r.attended = 1 THEN ''Attended'' ELSE ''Did Not Attend'' END as [Attended], 
						evregeo.email as [RegistrantEmailOverride],
						CASE when r.isFlagged = 1 then ''Yes'' else ''No'' end as [IsFlagged], 
						r.internalNotes as [InternalNotes], ';

					set @fullsql=@fullsql + case when len(@swcfList)>0 then 'swcf.' + replace(@swcfList,',',',swcf.') + ', ' else '' end;

					if @creditColumns <> '' 
						set @fullsql=@fullsql + '
							CASE WHEN r.status <> ''D'' THEN '''' + CAST((isnull(creditAwarded.sumCreditAwarded,0) + isnull(subEventCreditAwarded.sumCreditAwarded,0)) as varchar(10)) + '' credits earned'' ELSE ''None'' END as [Total Credit], 
							custom.*, ';

					if @eventRoleColumns <> '' set @fullsql=@fullsql + ' eventroles.*, ';
					set @fullsql=@fullsql + case when len(@rqList)>0 then 'rtq.' + replace(@rqList,',',',rtq.') + ', ' else '' end;
					set @fullsql=@fullsql + 'regFee.totalRegFee as [Amount Billed], regFee.totalRegFee-regFee.regFeePaid as [Amount Due], regFee.regFeePaid as [Amount Paid], rategroup.rateGrouping as [Event Rate Group], rates.rateName as [Event Rate], rates.reportCode as [Event Rate Report Code], ';

					set @fullsql=@fullsql + 
						<cfloop query="local.qryTicketPackageLabels">
							'tmpP.[#local.qryTicketPackageLabels.ticketPackageNameExpanded#], ' +
						</cfloop>
						<cfloop query="local.qryTicketLabel">
							'tmpT.[#local.qryTicketLabel.ticketTotalLabel#], ' +
						</cfloop>
						' ';

					if @isOnlineMeeting = 1 
						set @fullsql=@fullsql + ' [Total Time Spent] = CONVERT(CHAR(8),DATEADD(second,auditlog.totaltimespent,0),108), 
												  [Joined Program] = auditlog.joinedprogram, [Left Program] = auditlog.leftprogram, ';

					<cfif listLen(arguments.event.getValue('qID',''))>
						set @fullsql=@fullsql + case when len(@qList)>0 then 'tq.' + replace(@qList,',',',tq.') + ', ' else '' end;
					</cfif>

					set @fullsql=@fullsql + ' m2.* ';

					<cfif val(arguments.event.getValue('evformid',0))>
						SET @fullsql = @fullsql + ', EvFR.* ';
					</cfif>

					set @fullsql=@fullsql + '
						INTO ####tmpEVExport#local.tmpSuffix#
						FROM dbo.ev_registrants as r
						INNER JOIN ##tmpRegFiltered as rf on rf.registrantID = r.registrantID 
						INNER JOIN dbo.ams_members as m on m.memberid = r.memberid
						INNER JOIN ##tmpMembers as m2 ON m2.memberID = m.activeMemberID
						LEFT OUTER JOIN ##tmpRegTrans as regFee on regFee.registrantID = r.registrantID
						LEFT OUTER JOIN dbo.ev_rates as rates on rates.rateID = r.rateID
						LEFT OUTER JOIN dbo.ev_rateGrouping as rategroup on rategroup.rateGroupingID = rates.rateGroupingID
						LEFT OUTER JOIN ####tmpSWCF#local.tmpSuffix# as swcf on swcf.eventID = rf.eventID 
						LEFT OUTER JOIN ####tmpRQ#local.tmpSuffix# as rtq on rtq.registrantid = r.registrantid 
						<cfif listLen(arguments.event.getValue('qID',''))>
							LEFT OUTER JOIN ####tmpQ#local.tmpSuffix# as tq on tq.registrantid = r.registrantid
						</cfif>
						<cfif val(arguments.event.getValue('evformid',0))>
							LEFT OUTER JOIN ' + @finalResponseTable + ' AS EvFR ON EvFR.EvFRRegistrantID = r.registrantID
						</cfif>
						LEFT OUTER JOIN dbo.ams_emailAppOverrides as evregeo on evregeo.itemType = ''eventreg'' and evregeo.itemID = r.registrantID	';
					
					set @fullsql=@fullsql + '
						LEFT OUTER JOIN ##tmpRegPackageTotalCount as tmpP on tmpP.registrantID = r.registrantID
						LEFT OUTER JOIN ##tmpRegTicketTotalCount as tmpT on tmpT.registrantID = r.registrantID ';

					if @creditColumns <> '' 
						set @fullsql = @fullsql + '	
							LEFT OUTER JOIN (
								select rc.registrantID, isnull(sum(rc.creditValueAwarded),0) as sumCreditAwarded
								from dbo.crd_requests as rc
								INNER JOIN ##tmpRegFiltered as rf on rf.registrantID = rc.registrantID 
								where rc.creditAwarded = 1
								group by rc.registrantID
							) as creditAwarded on creditAwarded.registrantID = r.registrantID
							LEFT OUTER JOIN ( 
								select er.memberID, isnull(sum(cr.creditValueAwarded),0) as sumCreditAwarded
								from dbo.crd_requests as cr
								inner join dbo.ev_registrants as er on er.registrantID = cr.registrantID
								inner join dbo.ev_registration as reg on reg.registrationID = er.registrationID and reg.siteID = ' + cast(@siteID as varchar(10)) + ' 
								inner join dbo.ev_subEvents as subEvent on subEvent.eventID = reg.eventID
								and subEvent.parentEventID = ' + cast(@eventID as varchar(10)) + ' and reg.status = ''A'' 
								group by er.memberID 
							) as subEventCreditAwarded on subEventCreditAwarded.memberID = m.activeMemberID
							OUTER apply (
								select '+@creditColumns+'
								from (
									select rc.registrantID, isnull(rc.creditValueAwarded,0) as creditValueAwarded, ca.authorityCode + ''_'' + cat.typeCode as creditType
									from dbo.crd_requests as rc
									INNER JOIN ##tmpRegFiltered as rf2 on rf2.registrantID = rc.registrantID 
									inner join dbo.crd_offeringTypes ect on ect.offeringTypeID = rc.offeringTypeID
									inner join dbo.crd_authoritySponsorTypes as ast on ast.ASTID = ect.ASTID
									inner join dbo.crd_authorityTypes as cat on cat.typeID = ast.typeID
									inner join dbo.crd_authorities as ca on ca.authorityID = cat.authorityID
									where rc.creditAwarded = 1
									and rc.registrantID = r.registrantID
								) tmp
								PIVOT (min(creditValueAwarded) for creditType in ('+@creditColumns+')) as pvt
							) custom
							';

					if @eventRoleColumns <> ''
						set @fullsql = @fullsql + 
						'					
						OUTER apply (
							select '+@eventRoleColumns+'
							from (
								select tmpR.categoryName
								from dbo.ev_registrantCategories er
								inner join ##tmpEventRoles as tmpR on tmpR.categoryID = er.categoryID
								where er.registrantID = r.registrantID
							) tmp
							PIVOT (min(categoryName) for categoryName in ('+@eventRoleColumns+')) as pvt
						) eventroles
						';
					
					if @isOnlineMeeting = 1 set @fullsql = @fullsql + ' LEFT OUTER JOIN ##tmpAuditLog as auditlog on auditlog.registrantID = r.registrantID	';

					-- put into temp table for export
					EXEC(@fullsql);

					EXEC tempdb..sp_rename '####tmpEVExport#local.tmpSuffix#.memberID', 'MemberCentralID', 'COLUMN';

					<cfif val(arguments.event.getValue('evformid',0))>
						IF EXISTS(SELECT TOP 1 column_id FROM tempdb.sys.columns WHERE [name] = 'EvFRRegistrantID' AND object_id = object_id('tempdb..####tmpEVExport#local.tmpSuffix#')) 
							ALTER TABLE ####tmpEVExport#local.tmpSuffix# DROP COLUMN EvFRRegistrantID;
					</cfif>

					DECLARE @selectsql varchar(max) = '
						SELECT *, ROW_NUMBER() OVER(order by [RegistrantStatus], [DateRegistered]) as mcCSVorder 
						*FROM* ####tmpEVExport#local.tmpSuffix#';
					EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.strFolder.folderPathUNC#\#local.reportFileName#', @returnColumns=0;

					IF OBJECT_ID('tempdb..##tmpEventRoles') IS NOT NULL 
						DROP TABLE ##tmpEventRoles;
					IF OBJECT_ID('tempdb..##tmpAuditLog') IS NOT NULL 
						DROP TABLE ##tmpAuditLog;
					IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
						DROP TABLE ##tmpMembers;
					IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
						DROP TABLE ##tmp_membersForFS;
					IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
						DROP TABLE ##tmp_CF_ItemIDs;
					IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
						DROP TABLE ##tmp_CF_FieldData;
					IF OBJECT_ID('tempdb..####tmpSWCF#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpSWCF#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..####tmpRQ#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpRQ#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..####tmpQ#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpQ#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..##tmpSWCF') IS NOT NULL 
						DROP TABLE ##tmpSWCF;
					IF OBJECT_ID('tempdb..##tmpRQA') IS NOT NULL 
						DROP TABLE ##tmpRQA;
					<cfif listLen(arguments.event.getValue('qID',''))>
					IF OBJECT_ID('tempdb..##tmpQA') IS NOT NULL 
						DROP TABLE ##tmpQA;
					</cfif>
					IF OBJECT_ID('tempdb..####tmpEVExport#local.tmpSuffix#') IS NOT NULL 
						DROP TABLE ####tmpEVExport#local.tmpSuffix#;
					IF OBJECT_ID('tempdb..##tmpRegTrans') IS NOT NULL 
						DROP TABLE ##tmpRegTrans;
					IF OBJECT_ID('tempdb..##tmpRegFiltered') IS NOT NULL 
						DROP TABLE ##tmpRegFiltered;
					IF OBJECT_ID('tempdb..##tmpRegPackages') IS NOT NULL 
						DROP TABLE ##tmpRegPackages;
					IF OBJECT_ID('tempdb..##tmpRegPackageTotalCount') IS NOT NULL
						DROP TABLE ##tmpRegPackageTotalCount;
					IF OBJECT_ID('tempdb..##tmpRegTicketTotalCount') IS NOT NULL
						DROP TABLE ##tmpRegTicketTotalCount;
					IF OBJECT_ID('tempdb..##tmpEventsForFee') IS NOT NULL 
						DROP TABLE ##tmpEventsForFee; 
					IF OBJECT_ID('tempdb..##tmpEventsForFeeResult') IS NOT NULL 
						DROP TABLE ##tmpEventsForFeeResult;
					<cfif val(arguments.event.getValue('evformid',0))>
						IF OBJECT_ID('tempdb..' + @finalResponseTable) IS NOT NULL
							EXEC('DROP TABLE ' + @finalResponseTable);
					</cfif>

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
			<cfset local.exportRegResult.success = true>
		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Column names in each table must be unique", cfcatch.detail)>
				<cfset local.exportRegResult.errmsg = "Column names in the registrants export must be unique.">
			<cfelse>
				<cfset application.objerror.senderror(cfcatch=cfcatch)>
				<cfset local.exportRegResult.errmsg = "We were unable to download the filtered registrants.">
			</cfif>
			<cfset local.exportRegResult.success = false>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					<cfif local.exportRegResult.success>
						doDownloadReg('#local.stDownloadURL#');
					<cfelseif len(local.exportRegResult.errmsg)>
						showDownloadRegError('#jsStringFormat(local.exportRegResult.errmsg)#');
					<cfelse>
						showDownloadRegError('We were unable to download filtered registrants.');
					</cfif>
				</script>
			</cfoutput>	
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="generateAndDownloadQRCode" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.strEvent = CreateObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID'),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
			<cfset local.qryCalendar = this.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteInfo.siteID'), calendarID=local.strEvent.qryEventMeta.calendarID)>
			<cfset local.baseTestLink = getAppBaseLink(applicationInstanceID=local.qryCalendar.applicationInstanceID)>
			<cfset local.qrText = "#arguments.event.getValue('mc_siteInfo.scheme')#://#arguments.event.getValue('mc_siteInfo.mainhostname')#/?#local.baseTestLink#&evAction=showDetail&eid=#arguments.event.getValue('eID')#">

			<cfset local.strQRFolder = application.objDocDownload.createHoldingFolder()>
			<cfset local.strQRCode = createObject("component","model.system.common.platformMergeTagLibrary").qrcode(text=local.qrText, width=1000, height=1000, format='struct')>
			<cfset structInsert(local.strQRCode, 'imagedir', local.strQRFolder.folderPath)>
			<cfset structInsert(local.strQRCode, 'imagefn', 'QR_#local.strEvent.qryEventMeta.reportCode#.png')>
			
			<cfhttp method="GET" url="#local.strQRCode.dataStruct.qrcode[1].url#&width=#local.strQRCode.dataStruct.qrcode[1].width#&height=#local.strQRCode.dataStruct.qrcode[1].height#" path="#local.strQRCode.imagedir#" file="#local.strQRCode.imagefn#" timeout="60" throwonerror="true" getasbinary="true" />

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strQRCode.imagedir#/#local.strQRCode.imagefn#")>			
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strQRCode.imagedir#/#local.strQRCode.imagefn#", displayName=local.strQRCode.imagefn, deleteSourceFile=1)>
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					$('.fa-solid.fa-spinner.fa-spin').removeClass('fa-solid fa-spinner fa-spin').addClass('fa-light fa-qrcode');
					<cfif local.data.success>
						self.location.href='/tsdd/' + '#local.stDownloadURL#';
					<cfelse>
						mca_showAlert('err_eventdetails','We were unable to Generate QR Code',true);
					</cfif>
				</script>
			</cfoutput>	
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportTicketPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsid", selectedFieldSetName='Events Download Registrants Standard', allowBlankOption=false, inlinePreviewSectionID="exportTicketContainer", fieldLabel='Member Field Set with data to include in export')>

		<cfset local.qryTickets = this.objAdminEvent.getTickets(registrationID=arguments.event.getValue('rID',0))>

		<cfsavecontent variable="local.data">
			<cfoutput><cfinclude template="frm_exportTicket.cfm"></cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportTicket" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>

		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "Ticket-#left(ReReplaceNoCase(local.strEvent.qryEventMeta.eventContentTitle,'[^A-Z0-9]','_','ALL'),30)#.csv">

		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>
		<cfset local.resultsFieldsetID = arguments.event.getValue('fsid',0)>

		<!--- export data --->
		<cfquery name="local.qryExport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryExportResult">
			set nocount on;
			
			declare @ticketID int, @csrid int, @tpFieldUsageID int, @tFieldUsageID int, @tpcfList varchar(max), @fullsql varchar(max),
				@orgID int, @fieldSetID int, @outputFieldsXML xml;

			IF OBJECT_ID('tempdb..##tmpTicketPackageCF') IS NOT NULL 
				DROP TABLE ##tmpTicketPackageCF;
			IF OBJECT_ID('tempdb..##tmp_CF_ItemIDs') is not null
				DROP TABLE ##tmp_CF_ItemIDs;
			IF OBJECT_ID('tempdb..##tmp_CF_FieldData') is not null
				DROP TABLE ##tmp_CF_FieldData;
			IF OBJECT_ID('tempdb..##tmpTicketPackageCFData') IS NOT NULL 
				DROP TABLE ##tmpTicketPackageCFData;
			IF OBJECT_ID('tempdb..####tmpTicketPackageCFDataPvt#local.tmpSuffix#') IS NOT NULL 
				DROP TABLE ####tmpTicketPackageCFDataPvt#local.tmpSuffix#;
			IF OBJECT_ID('tempdb..##tmpTicketCF') IS NOT NULL 
				DROP TABLE ##tmpTicketCF;
			IF OBJECT_ID('tempdb..##tmpTicketCFData') IS NOT NULL 
				DROP TABLE ##tmpTicketCFData;
			IF OBJECT_ID('tempdb..####tmpTicketCFDataPvt#local.tmpSuffix#') IS NOT NULL 
				DROP TABLE ####tmpTicketCFDataPvt#local.tmpSuffix#;
			IF OBJECT_ID('tempdb..##tmpTickets') IS NOT NULL 
				DROP TABLE ##tmpTickets;
			IF OBJECT_ID('tempdb..##tmpTicketsfinal') IS NOT NULL 
				DROP TABLE ##tmpTicketsfinal;
			IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
				DROP TABLE ##tmp_membersForFS;
			IF OBJECT_ID('tempdb..##tmp_allWithFS') IS NOT NULL
				DROP TABLE ##tmp_allWithFS;

			CREATE TABLE ##tmp_CF_ItemIDs (itemID int, itemType varchar(20));
			CREATE TABLE ##tmp_CF_FieldData (fieldID int, fieldValue varchar(max), amount decimal(14,2), itemID int);
			CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);
			CREATE TABLE ##tmp_allWithFS (MFSAutoID int IDENTITY(1,1) not null);
			CREATE TABLE ##tmpTicketPackageCFData (pvtInstanceID int, titleOnInvoice varchar(max), answer varchar(max), INDEX IX_tmpTicketPackageCFData_instanceID (pvtInstanceID));
			CREATE TABLE ##tmpTicketCFData (pvtSeatID int, titleOnInvoice varchar(max), answer varchar(max), INDEX IX_tmpTicketCFData_seatID (pvtSeatID));

			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			set @fieldSetID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.resultsFieldsetID#">;
			set @ticketID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('tid')#">;
			set @csrid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#this.eventAdminSiteResourceID#">;
			select @tpFieldUsageID = dbo.fn_cf_getUsageID('Event','TicketPackage',null);
			select @tFieldUsageID = dbo.fn_cf_getUsageID('Event','Ticket',null);

			-- all ticket package custom fields
			select tp.ticketPackageID, f.fieldID, dbo.fn_regexReplace(tp.ticketPackageName + '_' + f.fieldReference,'[^A-Za-z0-9_\- ]','') as titleOnInvoice
			into ##tmpTicketPackageCF
			from dbo.ev_tickets as t
			inner join dbo.ev_ticketPackages as tp on tp.ticketID = t.ticketID
			inner join dbo.cf_fields as f on f.detailID = tp.ticketPackageID and f.isActive = 1
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			where t.ticketID = @ticketID
			and f.controllingSiteResourceID = @csrid
			and fu.parentUsageID = @tpFieldUsageID;

			-- ticket package custom field data unpivoted
			INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
			SELECT DISTINCT rpi.instanceID, 'ticketPackInstCustom'
			FROM ##tmpTicketPackageCF as tpcf
			INNER JOIN dbo.ev_registrantPackageInstances as rpi on rpi.ticketPackageID = tpcf.ticketPackageID
				and rpi.status = 'A';

			EXEC dbo.cf_getFieldData;
			
			INSERT INTO ##tmpTicketPackageCFData (pvtInstanceID, titleOnInvoice, answer)
			SELECT fd.itemID AS pvtInstanceID, tpcf.titleOnInvoice, fd.fieldValue as answer
			FROM ##tmp_CF_FieldData AS fd
			INNER JOIN ##tmpTicketPackageCF AS tpcf ON tpcf.fieldID = fd.fieldID;

			-- ticket package custom field data pivoted
			set @tpcfList = '';
			select @tpcfList = COALESCE(@tpcfList + ',', '') + quoteName(titleonInvoice) from ##tmpTicketPackageCF group by titleOnInvoice;
			IF left(@tpcfList,1) = ','
				select @tpcfList = right(@tpcfList,len(@tpcfList)-1);
			IF len(@tpcfList) > 0 BEGIN
				set @fullsql = '
					select * 
					into ####tmpTicketPackageCFDataPvt#local.tmpSuffix#
					from ##tmpTicketPackageCFData
					PIVOT (min(answer) for titleonInvoice in (' + @tpcfList + ')) as p ';
				EXEC(@fullsql);
			END
			ELSE
				SELECT pvtInstanceID
				INTO ####tmpTicketPackageCFDataPvt#local.tmpSuffix# 
				FROM ##tmpTicketPackageCFData
				WHERE 0=1;

			-- truncate temp field tables
			TRUNCATE TABLE ##tmp_CF_ItemIDs;
			TRUNCATE TABLE ##tmp_CF_FieldData;

			-- all ticket custom fields
			select t.ticketID, f.fieldID, dbo.fn_regexReplace(t.ticketName + '_' + f.fieldReference,'[^A-Za-z0-9_\- ]','') as titleOnInvoice
			into ##tmpTicketCF
			from dbo.ev_tickets as t
			inner join dbo.cf_fields as f on f.detailID = t.ticketID and f.isActive = 1
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			where t.ticketID = @ticketID
			and f.controllingSiteResourceID = @csrid
			and fu.parentUsageID = @tFieldUsageID;

			INSERT INTO ##tmp_CF_ItemIDs (itemID, itemType)
			SELECT DISTINCT rpis.seatID, 'ticketPackSeatCustom'
			FROM ##tmpTicketCF as tcf
			INNER JOIN dbo.cf_fieldData as fd on fd.fieldID = tcf.fieldID
				and fd.itemType = 'ticketPackSeatCustom'
			INNER JOIN dbo.ev_registrantPackageInstanceSeats as rpis on rpis.seatID = fd.itemID
				and rpis.status = 'A';

			EXEC dbo.cf_getFieldData;
			
			INSERT INTO ##tmpTicketCFData (pvtSeatID, titleOnInvoice, answer)
			SELECT fd.itemID AS pvtSeatID, tcf.titleOnInvoice, fd.fieldValue AS answer
			FROM ##tmp_CF_FieldData AS fd
			INNER JOIN ##tmpTicketCF AS tcf ON tcf.fieldID = fd.fieldID;

			-- ticket custom field data pivoted
			set @tpcfList = '';
			select @tpcfList = COALESCE(@tpcfList + ',', '') + quoteName(titleonInvoice) from ##tmpTicketCF group by titleOnInvoice;
			IF left(@tpcfList,1) = ','
				select @tpcfList = right(@tpcfList,len(@tpcfList)-1);
			IF len(@tpcfList) > 0 BEGIN
				set @fullsql = '
					select * 
					into ####tmpTicketCFDataPvt#local.tmpSuffix#
					from ##tmpTicketCFData
					PIVOT (min(answer) for titleonInvoice in (' + @tpcfList + ')) as p ';
				EXEC(@fullsql);
			END
			ELSE
				SELECT pvtSeatID
				INTO ####tmpTicketCFDataPvt#local.tmpSuffix# 
				FROM ##tmpTicketCFData
				WHERE 0=1;

			-- ticket data with registrant info
			select t.ticketName as [Ticket], rpis.seatID as [Ticket ID], 
				tp.ticketPackageName as [Ticket Package], rpi.instanceID as [Ticket Package ID],
				tpcf.*, tcf.*, m2.activeMemberID
			into ##tmpTickets
			from dbo.ev_tickets as t
			inner join dbo.ev_ticketPackages as tp on tp.ticketID = t.ticketID
			inner join dbo.ev_registrantPackageInstances as rpi on rpi.ticketPackageID = tp.ticketPackageID
				and rpi.status = 'A'
			inner join dbo.ev_registrantPackageInstanceSeats as rpis on rpis.instanceID = rpi.instanceID
				and rpis.status = 'A'
			inner join dbo.ev_registrants as r on r.registrantID = rpi.registrantID
				and r.status = 'A'
			inner join dbo.ams_members as m2 on m2.memberID = r.memberID
			left outer join ####tmpTicketPackageCFDataPvt#local.tmpSuffix# as tpcf on tpcf.pvtInstanceID = rpi.instanceID
			left outer join ####tmpTicketCFDataPvt#local.tmpSuffix# as tcf on tcf.pvtSeatID = rpis.seatID
			where t.ticketID = @ticketID;

			ALTER TABLE ##tmpTickets DROP COLUMN pvtInstanceID;
			ALTER TABLE ##tmpTickets DROP COLUMN pvtSeatID;

			-- get fieldset data
			INSERT INTO ##tmp_membersForFS (memberID)
			select distinct activeMemberID
			from ##tmpTickets;

			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
				@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmp_allWithFS',
				@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;

			-- prep final table
			select t.*, tmpFS.*
			into ##tmpTicketsfinal
			from ##tmpTickets as t
			INNER JOIN ##tmp_allWithFS as tmpFS on t.activeMemberID = tmpFS.memberid;

			ALTER TABLE ##tmpTicketsfinal DROP COLUMN activememberID;
			ALTER TABLE ##tmpTicketsfinal DROP COLUMN memberID;

			DECLARE @selectsql varchar(max) = '
				SELECT *, ROW_NUMBER() OVER(order by [Ticket], [Ticket Package], [Ticket ID]) as mcCSVorder 
				*FROM* ##tmpTicketsfinal';
			EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpTicketPackageCF') IS NOT NULL 
				DROP TABLE ##tmpTicketPackageCF;
			IF OBJECT_ID('tempdb..##tmpTicketPackageCFData') IS NOT NULL 
				DROP TABLE ##tmpTicketPackageCFData;
			IF OBJECT_ID('tempdb..####tmpTicketPackageCFDataPvt#local.tmpSuffix#') IS NOT NULL 
				DROP TABLE ####tmpTicketPackageCFDataPvt#local.tmpSuffix#;
			IF OBJECT_ID('tempdb..##tmpTicketCF') IS NOT NULL 
				DROP TABLE ##tmpTicketCF;
			IF OBJECT_ID('tempdb..##tmpTicketCFData') IS NOT NULL 
				DROP TABLE ##tmpTicketCFData;
			IF OBJECT_ID('tempdb..####tmpTicketCFDataPvt#local.tmpSuffix#') IS NOT NULL 
				DROP TABLE ####tmpTicketCFDataPvt#local.tmpSuffix#;
			IF OBJECT_ID('tempdb..##tmpTickets') IS NOT NULL 
				DROP TABLE ##tmpTickets;
			IF OBJECT_ID('tempdb..##tmpTicketsfinal') IS NOT NULL 
				DROP TABLE ##tmpTicketsfinal;
			IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
				DROP TABLE ##tmp_membersForFS;
			IF OBJECT_ID('tempdb..##tmp_allWithFS') IS NOT NULL
				DROP TABLE ##tmp_allWithFS;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.editEvent#&eid=#arguments.event.getValue('eID')#&tab=registrants" addtoken="no">
		</cfif>	
	</cffunction>

	<cffunction name="exportRegTransPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsid", selectedFieldSetName='Events Download Registrants Standard', allowBlankOption=false, inlinePreviewSectionID="expRegTransContainer", fieldLabel='Member Field Set with data to include in export')>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_exportRegTrans.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportRegTransactions" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>

		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "RegTrans-#left(ReReplaceNoCase(local.strEvent.qryEventMeta.eventContentTitle,'[^A-Z0-9]','_','ALL'),30)#.csv">
		
		<cfset local.qryRegistrants = this.objAdminEvent.getRegistrantsFromFilters(event=arguments.event, mode="regTabDownload")>
		<cfset local.resultsFieldsetID = arguments.event.getValue('fsid',0)>
		<cfset local.tmpSuffix = replace(createUUID(),'-','','ALL')>

		<cfquery name="local.qryExport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryExportResult">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				-- status 2 (voided) do not appear here
				-- status 3 (pending) do not appear here -- they are not accepted transactions
				-- status 4 (voidedpending) do not appear here -- they are meant to be completely hidden
				-- types 5 (allocations), 8 (voidOffsets), and 10 (dit) do not appear here

				IF OBJECT_ID('tempdb..##tmpRegFiltered') IS NOT NULL 
					DROP TABLE ##tmpRegFiltered;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
					DROP TABLE ##tmp_membersForFS;
				IF OBJECT_ID('tempdb..##tmpExport') IS NOT NULL 
					DROP TABLE ##tmpExport;
				IF OBJECT_ID('tempdb..####tmpEVExport#local.tmpSuffix#') IS NOT NULL 
					DROP TABLE ####tmpEVExport#local.tmpSuffix#;
				CREATE TABLE ##tmpRegFiltered (registrantID int PRIMARY KEY, memberID int, registrationID int);
				CREATE TABLE ##tmpMembers (MFSAutoID int IDENTITY(1,1) not null);
				CREATE TABLE ##tmp_membersForFS (memberID int PRIMARY KEY);

				declare @orgID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.orgID')#">;
				declare @fieldsetID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.resultsFieldsetID#">;
				declare @t_Sale int, @t_Payment int, @t_Adj int, @t_Refund int, @t_WO int, @t_Tax int, @t_Alloc int, @t_VO int, @t_DIT int, @outputFieldsXML xml;
				set @t_Sale = dbo.fn_tr_getTypeID('Sale');
				set @t_Payment = dbo.fn_tr_getTypeID('Payment');
				set @t_Adj = dbo.fn_tr_getTypeID('Adjustment');
				set @t_Refund = dbo.fn_tr_getTypeID('Refund');
				set @t_WO = dbo.fn_tr_getTypeID('Write Off');
				set @t_Tax = dbo.fn_tr_getTypeID('Sales Tax');
				set @t_Alloc = dbo.fn_tr_getTypeID('Allocation');
				set @t_VO = dbo.fn_tr_getTypeID('VoidOffset');
				set @t_DIT = dbo.fn_tr_getTypeID('Deferred Transfer');

				-- registrants matching filters
				INSERT INTO ##tmpRegFiltered
				select r.registrantID, m.activeMemberID, r.registrationID
				from dbo.ev_registrants as r
				inner join dbo.ams_members as m on m.memberID = r.memberID
				where r.registrantID in (0#ValueList(local.qryRegistrants.registrantID)#);

				-- get fieldset data
				INSERT INTO ##tmp_membersForFS (memberID)
				select distinct memberID
				from ##tmpRegFiltered;

				EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, @fieldsetIDList=@fieldsetID, @existingFields='',
					@ovNameFormat=NULL, @ovMaskEmails=NULL, @membersTableName='##tmp_membersForFS', @membersResultTableName='##tmpMembers',
					@linkedMembers=0, @mode='export', @outputFieldsXML=@outputFieldsXML OUTPUT;
				
				select r.memberid, rt.transactionDate, tt.type, isnull(rt.detail,'') as detail, 
					ma2.lastname + ', ' + ma2.firstname + ' (' + ma2.membernumber + ')' as AssignedTo,
					m2.lastname + ', ' + m2.firstname + ' (' + m2.membernumber + ')' as RecordedBy,
					ACCOUNT = case
						when tt.typeID = @t_Sale then rglcred.thePathExpanded
						when tt.typeID = @t_Payment then rgldeb.thePathExpanded
						when tt.typeID = @t_Adj and rgldeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglcred.thePathExpanded
						when tt.typeID = @t_Adj and rglcred.GLCode = 'ACCOUNTSRECEIVABLE' then rgldeb.thePathExpanded
						when tt.typeID = @t_Refund then rglcred.thePathExpanded
						when tt.typeID = @t_WO and rglcred.GLCode = 'ACCOUNTSRECEIVABLE' then rgldeb.thePathExpanded
						when tt.typeID = @t_WO and rgldeb.GLCode = 'DEPOSITS' then rglcred.thePathExpanded
						when tt.typeID = @t_Tax then rglcred.thePathExpanded
						else '' end,
					AccountCode = case
						when tt.typeID = @t_Sale then rglcred.accountCode
						when tt.typeID = @t_Payment then rgldeb.accountCode
						when tt.typeID = @t_Adj and rgldeb.GLCode = 'ACCOUNTSRECEIVABLE' then rglcred.accountCode
						when tt.typeID = @t_Adj and rglcred.GLCode = 'ACCOUNTSRECEIVABLE' then rgldeb.accountCode
						when tt.typeID = @t_Refund then rglcred.accountCode
						when tt.typeID = @t_WO and rglcred.GLCode = 'ACCOUNTSRECEIVABLE' then rgldeb.accountCode
						when tt.typeID = @t_WO and rgldeb.GLCode = 'DEPOSITS' then rglcred.accountCode
						when tt.typeID = @t_Tax then rglcred.accountCode
						else '' end,
					DEBIT = case
						when tt.typeID = @t_Sale then null
						when tt.typeID = @t_Payment then rt.amount
						when tt.typeID = @t_Adj and rgldeb.GLCode = 'ACCOUNTSRECEIVABLE' then null
						when tt.typeID = @t_Adj and rglcred.GLCode = 'ACCOUNTSRECEIVABLE' then rt.amount
						when tt.typeID = @t_Refund then null
						when tt.typeID = @t_WO and rglcred.GLCode = 'ACCOUNTSRECEIVABLE' then rt.amount
						when tt.typeID = @t_WO and rgldeb.GLCode = 'DEPOSITS' then null
						when tt.typeID = @t_Tax then null
						else '' end,
					CREDIT = case
						when tt.typeID = @t_Sale then rt.amount
						when tt.typeID = @t_Payment then null
						when tt.typeID = @t_Adj and rgldeb.GLCode = 'ACCOUNTSRECEIVABLE' then rt.amount
						when tt.typeID = @t_Adj and rglcred.GLCode = 'ACCOUNTSRECEIVABLE' then null
						when tt.typeID = @t_Refund then rt.amount
						when tt.typeID = @t_WO and rglcred.GLCode = 'ACCOUNTSRECEIVABLE' then null
						when tt.typeID = @t_WO and rgldeb.GLCode = 'DEPOSITS' then rt.amount
						when tt.typeID = @t_Tax then rt.amount
						else '' end
				into ##tmpExport
				from ##tmpRegFiltered as r
				inner join dbo.ams_members as mr on mr.memberID = r.memberid
				cross apply dbo.fn_ev_registrantTransactions(r.registrantid) as rt
				inner join dbo.tr_types as tt on tt.typeID = rt.typeID
				inner join dbo.ams_members as ma on ma.memberID = rt.AssignedTomemberid
				inner join dbo.ams_members as ma2 on ma2.memberID = ma.activeMemberID
				inner join dbo.ams_members as m on m.memberID = rt.recordedByMemberID
				inner join dbo.ams_members as m2 on m2.memberID = m.activeMemberID
				INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rgldeb on rgldeb.GLAccountID = rt.debitGlAccountID
				INNER JOIN dbo.fn_getRecursiveGLAccounts(@orgID) as rglcred on rglcred.GLAccountID = rt.creditGlAccountID
				WHERE rt.ownedByOrgID = @orgID
				and rt.statusID = 1
				and rt.typeID not in (@t_Alloc,@t_VO,@t_DIT);

				-- put into temp table for export
				DECLARE @fullsql varchar(max) = '
					SELECT tmp.TransactionDate, tmp.type as TransactionType, 
						tmp.detail as TransactionDetail, tmp.AssignedTo, tmp.RecordedBy, tmp.Account, tmp.AccountCode, 
						tmp.Debit, tmp.Credit, m.*
					INTO ####tmpEVExport#local.tmpSuffix#
					FROM ##tmpExport as tmp
					INNER JOIN ##tmpMembers as m ON m.memberID = tmp.memberID';
				EXEC(@fullsql);

				EXEC tempdb..sp_rename '####tmpEVExport#local.tmpSuffix#.memberID', 'MemberCentralID', 'COLUMN';

				DECLARE @selectsql varchar(max) = '
					SELECT *, ROW_NUMBER() OVER(order by TransactionDate) as mcCSVorder 
					*FROM* ####tmpEVExport#local.tmpSuffix#';
				EXEC dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#local.strFolder.folderPathUNC#\#local.reportFileName#', @returnColumns=0;

				IF OBJECT_ID('tempdb..##tmpRegFiltered') IS NOT NULL 
					DROP TABLE ##tmpRegFiltered;
				IF OBJECT_ID('tempdb..##tmpMembers') IS NOT NULL 
					DROP TABLE ##tmpMembers;
				IF OBJECT_ID('tempdb..##tmp_membersForFS') IS NOT NULL
					DROP TABLE ##tmp_membersForFS;
				IF OBJECT_ID('tempdb..##tmpExport') IS NOT NULL 
					DROP TABLE ##tmpExport;
				IF OBJECT_ID('tempdb..####tmpEVExport#local.tmpSuffix#') IS NOT NULL 
					DROP TABLE ####tmpEVExport#local.tmpSuffix#;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;		
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.editEvent#&eid=#arguments.event.getValue('eID')#&tab=registrants" addtoken="no">
		</cfif>	
	</cffunction>

	<cffunction name="exportOnlineMeetingLogPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(
			siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsid", selectedFieldSetName='Events Download Registrants Standard', allowBlankOption=false, inlinePreviewSectionID="expAccessLogContainer", fieldLabel='Member Field Set with data to include in export')>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_exportAccessLog.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="exportOnlineMeetingLog" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfset var local = structNew()>
		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>

		<!--- set vars --->
		<cfset local.resultsFieldsetID = arguments.event.getValue('fsid',0)>
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "RegLog-#left(ReReplaceNoCase(local.strEvent.qryEventMeta.eventContentTitle,'[^A-Z0-9]','_','ALL'),30)#.csv">

		<!--- export data --->
		<cfstoredproc procedure="ev_exportOnlineMeetingLog" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('rID')#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.resultsFieldsetID#" null="No">
			<cfprocparam type="In" cfsqltype="CF_SQL_VARCHAR" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#" null="No">
		</cfstoredproc>
		
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.docResult = application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfif not local.docResult>
			<cflocation url="#this.link.editEvent#&eid=#arguments.event.getValue('eID')#&tab=registrants" addtoken="no">
		</cfif>	
	</cffunction>	
	
	<cffunction name="regSearch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
	
		<cfset local.qryCalendars = this.objAdminEvent.getCalendarsForFilters(arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.qryEventRoles = this.objAdminEvent.getEventRoles(siteResourceID=this.eventAdminSiteResourceID)>
		<cfset local.massEmailRegistrants = buildCurrentLink(arguments.event,"massEmailRegistrants") & "&mode=direct">

		<cfset local.evDateFrom = dateFormat(now(), "m/d/yyyy")>
		<cfset local.evDateTo = dateFormat(dateAdd("d",365,now()), "m/d/yyyy")>
		<cfset local.rDateFrom = "#month(now())#/1/#year(now())#">
		<cfset local.rDateTo = "#dateformat(now(),'m/d/yyyy')#">
		
		<cfset local.TransactionsAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='TransactionsAdmin',siteID=arguments.event.getValue('mc_siteInfo.siteid'))>
		<cfset local.myRightsTransactionsAdmin = buildRightAssignments(local.TransactionsAdminSRID,session.cfcuser.memberData.memberid,arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.badgePrinterEnabled = arguments.event.getValue('mc_siteinfo.SF_BADGEPRINTERS')>
		<cfset local.hasBadgeDevices = createObject("component","model.admin.badges.device").hasBadgeDeviceForSite(siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.qryBadgeTemplates = CreateObject("component","model.admin.badges.device").getCategoriesAndTemplatesForTree(siteID=arguments.event.getValue('mc_siteinfo.siteid'), treeCode="BTEVENTS")>
		
		<cfset local.eventRegistrantsLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getRegistrants&gridmode=regSearchGrid&mode=stream">
		<cfset local.memSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list')>
		<cfset local.grpSelectGotoLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups')>
		<cfset local.exportRegPromptLink = "#this.link.exportRegPrompt#&mode=direct&eID=0&view=regSearch">
		<cfset local.exportRegSearchLink = "#this.link.exportRegSearch#&mode=stream&eID=0">
		<cfset local.transAdminTool = "/?pg=admin&mca_s=0&mca_a=0&mca_tt=" & getToolIDByName('TransactionAdmin')>
		<cfset local.refundPaymentURL = local.transAdminTool & "&mca_ta=refundPayment&tabMode=registrants&mode=direct">
		
		<!--- role fields --->
		<cfset local.strFieldFilters = getFieldFilterSelector(siteID=arguments.event.getValue('mc_siteinfo.siteid'), eventID=0)>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_regSearch.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="exportRegSearch" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.exportRegResult = { "success":false, "errmsg":"" }>

		<cftry>
			<cfset local.qryRegistrants = CreateObject("component","model.admin.events.event").getRegistrantsFromFilters(event=arguments.event, mode="regSearchGridExport")>

			<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.qryRegistrants.csvFileName#")>
			<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.qryRegistrants.csvFileName#", displayName=local.qryRegistrants.displayName, deleteSourceFile=1)>
			<cfset local.exportRegResult.success = true>

		<cfcatch type="any">
			<cfif structKeyExists(cfcatch,"detail") and findNoCase("Column names in each table must be unique", cfcatch.detail)>
				<cfset local.exportRegResult.errmsg = "Column names in the registrants export must be unique.">
			<cfelse>
				<cfset application.objerror.senderror(cfcatch=cfcatch)>
				<cfset local.exportRegResult.errmsg = "We were unable to download the filtered registrants.">
			</cfif>
			<cfset local.exportRegResult.success = false>
		</cfcatch>
		</cftry>

		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">
					<cfif local.exportRegResult.success>
						doDownloadReg('#local.stDownloadURL#');
					<cfelseif len(local.exportRegResult.errmsg)>
						showDownloadRegError('#jsStringFormat(local.exportRegResult.errmsg)#');
					<cfelse>
						showDownloadRegError('We were unable to download registrants search.');
					</cfif>
				</script>
			</cfoutput>	
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	 
	<!--- Get Messages for Application--->
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();			
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('message')>
					<p>
						<cfswitch expression="#arguments.event.getValue('message')#">
							<cfcase value="1"><b>You do not have rights to this page.</b></cfcase>
							<cfcase value="2"><b>You must add Event Categories before you can Add an Event.</b></cfcase>
						</cfswitch>
					</p>				
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>

	<cffunction name="sampleCreditsImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = "The sample import file could not be generated. Contact MemberCentral for assistance.">
	
		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>

		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "CreditsImport-#left(ReReplaceNoCase(local.strEvent.qryEventMeta.eventContentTitle,'[^A-Z0-9]','_','ALL'),30)#.csv">

		<!--- export template --->
		<cfstoredproc procedure="ev_uploadRegistrantCreditTemplate" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eID')#">
			<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#">
		</cfstoredproc>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="importCreditsPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfscript>
		var local = structNew();

		local.objEvent = CreateObject("component","model.events.events");
		local.objRegistration = createObject('component', 'model.admin.events.registration');
		local.objAdminEvent = CreateObject("component","model.admin.events.event");
		
		// get information --------------------------------------------------------------------------
		local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
		arguments.event.setValue('cID',val(local.strEvent.qryEventMeta.calendarID));

		// if event not found, redirect to list events ------------------------------------------------
		if (not val(local.strEvent.qryEventMeta.eventID))
			application.objCommon.redirect(this.link.listEvents);	
			
		local.importCreditsLink = "#this.link.importCreditsComplete#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";
		local.sampleCreditsImportTemplate = "#this.link.sampleCreditsImportTemplate#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";			
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_importCredits.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="importCreditsComplete" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>

		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID) />
		<cfset local.errMsg = "" />
		<cfset local.objEvent = CreateObject("component","model.events.events") />
		<cfset local.objRegistration = createObject('component', 'model.admin.events.registration') />
		<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event") />
		<cfset local.importCreditsLink = "#this.link.importCreditsPrompt#&mode=direct&eID=#arguments.event.getValue('eID',0)#" />

		<!--- extend timeout --->
		<cfsetting requesttimeout="400">
		
		<cfset local.completeResult = processImportCredits(event=arguments.event)>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<h4>Upload Attendance and Credits Results</h4>
				
			<cfif not local.completeResult.success>
				<div class="alert alert-danger">
					<div class="font-weight-bold mb-3">The upload was stopped and could not complete.</div>
					<cfswitch expression="#local.completeResult.errorCode#">
						<cfcase value="105">
							<div class="mb-1">The following errors were fatal and stopped the import:</div>
							<div class="m-1 mb-3">
								<cfloop array="#XMLSearch(local.completeResult.importResultXML,"/import/errors/error")#" index="local.thisNode">
									&bull; #local.thisNode.xmlAttributes.msg#<br/>
								</cfloop>
							</div>
							<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#local.importCreditsLink#';">Try Again</button> 
						</cfcase>
						<cfdefaultcase>
							<div class="m-1 mb-3">#local.completeResult.errorInfo[local.completeResult.errorCode]#</div>
							<button type="button" class="btn btn-sm btn-secondary" onclick="self.location.href='#local.importCreditsLink#';">Try Again</button> 
						</cfdefaultcase>
					</cfswitch>
				</div>
			<cfelse>
				<div class="alert alert-success">
					<div class="font-weight-bold mb-3">The upload was successful!</div>
					<button type="button" class="btn btn-sm btn-secondary" onclick="top.reloadPage();top.closeBox();">Close</button>
				</div>
			</cfif>

			</cfoutput>
		</cfsavecontent>	
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	

	<cffunction name="processImportCredits" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errorCode = 999>
		<cfset local.returnStruct.errorInfo = StructNew()>

		<!--- attempt upload --->
		<cftry>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cffile action="upload" filefield="uploadFileName" destination="#local.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">
			<cfset local.ext = local.uploadResult.ServerFileExt>
			<cfif listFindNoCase("csv",local.uploadResult.ServerFileExt) is 0>
				<cffile action="DELETE" file="#local.strFolder.folderPath#/#local.uploadResult.ServerFile#">
				<cfthrow message="Uploaded file was not in the proper format (#local.uploadResult.ServerFileExt#).">
			</cfif> 
			<cfif "#local.strFolder.folderPath#/EventCredits#val(local.strEvent.qryEventMeta.eventID)#.csv" neq "#local.strFolder.folderPath#/#local.uploadResult.ServerFile#">
				<cffile action="move" destination="#local.strFolder.folderPath#/EventCredits#val(local.strEvent.qryEventMeta.eventID)#.csv" source="#local.strFolder.folderPath#/#local.uploadResult.ServerFile#">
			</cfif>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 1>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem uploading the selected file. Only comma separated value files (CSV) can be uploaded. Try the upload again or contact us for assistance.")>
				<cfreturn local.returnStruct>
			</cfcatch>
		</cftry>

		<!--- parse CSV --->
		<cfset local.parseResult = CreateObject("component","model.admin.common.modules.import.import").parseCSV(stFilePath='#local.strFolder.folderPath#/EventCredits#val(local.strEvent.qryEventMeta.eventID)#.csv', stFilePathTmp='#local.strFolder.folderPath#/EventCredits#val(local.strEvent.qryEventMeta.eventID)#Parsed.csv')>
		<cfset local.returnStruct.success = local.parseResult.isErr is 0>
		<cfif NOT local.returnStruct.success>
			<cfset local.returnStruct.errorCode = 101>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- run import --->
  		<cftry>
			<!--- had to do it in a cfquery because cfstoredproc kept truncating the result --->
			<cfquery name="local.qryImport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					
					DECLARE @orgID int, @eventID int, @importResult xml, @errCount int;
					set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
					set @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.strEvent.qryEventMeta.eventID)#">;

					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##tmpImportEventCreditData') IS NOT NULL 
							DROP TABLE ##tmpImportEventCreditData;
						CREATE TABLE ##tmpImportEventCreditData (
							<cfloop list="#local.parseResult.strTableColumnNames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(local.parseResult.strTableColumnNames,chr(7))>, </cfif>
							</cfloop>
						);
						BULK INSERT ##tmpImportEventCreditData 
							FROM '#local.strFolder.folderPathUNC#\EventCredits#val(local.strEvent.qryEventMeta.eventID)#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
					END TRY
					BEGIN CATCH
						set @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- preparing table for import
					BEGIN TRY
						ALTER TABLE ##tmpImportEventCreditData ADD MCMemberID int NULL, MCRegistrantID int NULL;
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to prepare table for import." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.ev_uploadRegistrantCredit @orgID=@orgID, @eventID=@eventID, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						set @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					set @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount, * FROM ##tmpImportEventCreditData;

					IF OBJECT_ID('tempdb..##tmpImportEventCreditData') IS NOT NULL 
						DROP TABLE ##tmpImportEventCreditData;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.returnStruct.numFatalErrors = local.qryImport.errCount>

			<cfif local.returnStruct.numFatalErrors gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 105>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'')>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfif findNoCase("An object or column name is missing or empty.",cfcatch.detail)>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the file.<br/>It looks like there is a data column with a blank column heading. This can happen if there is any data in any column to the right of your last column in the uploaded file.")>
			<cfelse>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the file.")>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfif>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="importRegPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />

		<cfscript>
		var local = structNew();

		local.objEvent = CreateObject("component","model.events.events");
		local.objRegistration = createObject('component', 'model.admin.events.registration');
		local.objAdminEvent = CreateObject("component","model.admin.events.event");
		
		// get information --------------------------------------------------------------------------
		local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID);
		arguments.event.setValue('cID',val(local.strEvent.qryEventMeta.calendarID));

		// if event not found, redirect to list events ------------------------------------------------
		if (not val(local.strEvent.qryEventMeta.eventID))
			application.objCommon.redirect(this.link.listEvents);
			
		local.qryRegCustomFields = local.objAdminEvent.getCustomFields(eventID=local.strEvent.qryEventMeta.eventID);
		local.importRegLink = "#this.link.importRegComplete#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#";
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_importReg.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="importRegComplete" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any" />
		
		<cfset var local = structNew()>

		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID) />
		<cfset local.errMsg = "" />
		<cfset local.objEvent = CreateObject("component","model.events.events") />
		<cfset local.objRegistration = createObject('component', 'model.admin.events.registration') />
		<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event") />
		<cfset local.importRegLink = "#this.link.importRegPrompt#&mode=direct&cID=#arguments.event.getValue('cID')#&eID=#val(local.strEvent.qryEventMeta.eventID)#" />

		<!--- extend timeout --->
		<cfsetting requesttimeout="500">
		
		<cfset local.completeResult = processImportReg(event=arguments.event)>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<h4>Import Registrants Results</h4>
				
			<cfif not local.completeResult.success>
				<div class="alert alert-danger">
					<div><b>The import was stopped and could not complete.</b></div>
					<br/>
					<cfswitch expression="#local.completeResult.errorCode#">
						<cfcase value="105">
							<div>The following errors were fatal and stopped the import:</div>
							<div class="m-2">
								<cfloop array="#XMLSearch(local.completeResult.importResultXML,"/import/errors/error")#" index="local.thisNode">
									&bull; #local.thisNode.xmlAttributes.msg#<br/>
								</cfloop>
							</div>
							<button type="button" class="btn btn-sm btn-secondary mt-2" onclick="self.location.href='#local.importRegLink#';">Try Again</button>
						</cfcase>
						<cfdefaultcase>
							<div>#local.completeResult.errorInfo[local.completeResult.errorCode]#</div>
							<button type="button" class="btn btn-sm btn-secondary mt-2" onclick="self.location.href='#local.importRegLink#';">Try Again</button>
						</cfdefaultcase>
					</cfswitch>
				</div>
			<cfelse>
				<div class="alert alert-success">
					<p class="font-weight-bold">The import was successful!</p>
					<button type="button" class="btn btn-sm btn-secondary" onclick="top.reloadPage();top.MCModalUtils.hideModal();">Close</button>
				</div>
			</cfif>

			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="processImportReg" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.strEvent = createObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.success = true>
		<cfset local.returnStruct.errorCode = 999>
		<cfset local.returnStruct.errorInfo = StructNew()>

		<!--- attempt upload --->
		<cftry>
			<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
			<cffile action="upload" filefield="uploadFileName" destination="#local.strFolder.folderPath#" result="local.uploadResult" nameconflict="OVERWRITE">
			<cfset local.ext = local.uploadResult.ServerFileExt>
			<cfif listFindNoCase("csv",local.uploadResult.ServerFileExt) is 0>
				<cffile action="DELETE" file="#local.strFolder.folderPath#/#local.uploadResult.ServerFile#">
				<cfthrow message="Uploaded file was not in the proper format (#local.uploadResult.ServerFileExt#).">
			</cfif> 
			<cfif "#local.strFolder.folderPath#/EVENTregistrants#arguments.event.getValue('mc_siteinfo.siteid')#.csv" neq "#local.strFolder.folderPath#/#local.uploadResult.ServerFile#">
				<cffile action="move" destination="#local.strFolder.folderPath#/EVENTregistrants#arguments.event.getValue('mc_siteinfo.siteid')#.csv" source="#local.strFolder.folderPath#/#local.uploadResult.ServerFile#">
			</cfif>
			<cfcatch type="Any">
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 1>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem uploading the selected file. Only comma separated value files (CSV) can be uploaded. Try the upload again or contact us for assistance.")>
				<cfreturn local.returnStruct>
			</cfcatch>
		</cftry>

		<!--- parse CSV --->
		<cfset local.parseResult = CreateObject("component","model.admin.common.modules.import.import").parseCSV(stFilePath='#local.strFolder.folderPath#/EVENTregistrants#arguments.event.getValue('mc_siteinfo.siteid')#.csv', stFilePathTmp='#local.strFolder.folderPath#/EVENTregistrants#arguments.event.getValue('mc_siteinfo.siteid')#Parsed.csv')>
		<cfset local.returnStruct.success = local.parseResult.isErr is 0>
		<cfif NOT local.returnStruct.success>
			<cfset local.returnStruct.errorCode = 101>
			<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,local.parseResult.errMsg)>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- run import --->
  		<cftry>
			<!--- had to do it in a cfquery because cfstoredproc kept truncating the result --->
			<cfquery name="local.qryImport" datasource="#Application.dsn.membercentral.dsn#" result="local.qryImportResult">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					
					DECLARE @siteID int, @eventID int, @markAttended bit, @awardCredit bit, @importResult xml, @errCount int;
					set @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">;
					set @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.strEvent.qryEventMeta.eventID)#">;
					set @markAttended = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('attend',0)#">;
					set @awardCredit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('awardcredit',0)#">;

					-- bulk insert from file
					BEGIN TRY
						IF OBJECT_ID('tempdb..##tmpImportEventRegistrants') IS NOT NULL 
							DROP TABLE ##tmpImportEventRegistrants;
						CREATE TABLE ##tmpImportEventRegistrants (
							<cfloop list="#local.parseResult.strTableColumnNames#" index="local.thisCol" delimiters="#chr(7)#">
								[#local.thisCol#] varchar(max)<cfif local.thisCol neq listLast(local.parseResult.strTableColumnNames,chr(7))>, </cfif>
							</cfloop>
						);
						BULK INSERT ##tmpImportEventRegistrants 
							FROM '#local.strFolder.folderPathUNC#\EVENTregistrants#arguments.event.getValue('mc_siteinfo.siteid')#Parsed.csv' 
							WITH (FIELDTERMINATOR='#chr(7)#', FIRSTROW=2);
					END TRY
					BEGIN CATCH
						set @importResult = '<import><errors><error msg="Unable to import the file for processing." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- preparing table for import
					BEGIN TRY
						ALTER TABLE ##tmpImportEventRegistrants ADD MCMemberID int NULL, MCRegistrantID int NULL;
						<cfif NOT ListFindNoCase(local.parseResult.strTableColumnNames,'DateRegistered',chr(7))>
							ALTER TABLE ##tmpImportEventRegistrants ADD dateRegistered varchar(max) NULL;
						</cfif>
					END TRY
					BEGIN CATCH
						select @importResult = '<import><errors><error msg="Unable to prepare table for import." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH

					-- import file
					BEGIN TRY
						set @importResult = null;
						EXEC dbo.ev_uploadRegistrants @siteID=@siteID, @eventID=@eventID, @markAttended=@markAttended, @awardCredit=@awardCredit, @importResult=@importResult OUTPUT;
					END TRY
					BEGIN CATCH
						set @importResult = '<import><errors><error msg="Unable to process the import file." /><error msg="' + error_message() + '" /></errors></import>';
						GOTO on_done;
					END CATCH
				
					on_done:
					set @errCount = @importResult.value('count(/import/errors/error)','int');
					SELECT @importResult as importResult, @errCount as errCount, * FROM ##tmpImportEventRegistrants;

					IF OBJECT_ID('tempdb..##tmpImportEventRegistrants') IS NOT NULL 
						DROP TABLE ##tmpImportEventRegistrants;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.returnStruct.importResultXML = xmlparse(local.qryImport.importResult)>
			<cfset local.returnStruct.numFatalErrors = local.qryImport.errCount>

			<cfif local.returnStruct.numFatalErrors gt 0>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.errorCode = 105>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,'')>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.errorCode = 1>
			<cfif findNoCase("An object or column name is missing or empty.",cfcatch.detail)>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the file.<br/>It looks like there is a data column with a blank column heading. This can happen if there is any data in any column to the right of your last column in the uploaded file.")>
			<cfelse>
				<cfset StructInsert(local.returnStruct.errorInfo,local.returnStruct.errorCode,"There was a problem importing the file.")>
				<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
			</cfif>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>	

	<!--- Asset Types --->
	<cffunction name="listAssetCategories" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			//make sure categoryTree exists
			local.categoryTreeName = 'Event Asset Types';			
			if (not this.objCategories.getCategoryTrees(siteResourceID=this.siteResourceID,categoryTreeName=local.categoryTreeName).recordCount)
				this.objCategories.addCategoryTree(	siteID=arguments.event.getValue('mc_siteInfo.siteid'), controllingSiteResourceID=this.siteResourceID, categoryTreeName=local.categoryTreeName );

			local.objCategories = createObject("component","model.admin.categories.categories");
			local.editPermissionFn = 'manageAssetTypes';
			local.deletePermissionFn = 'manageAssetTypes';

			local.strAssetTypesGridData = { 
				adminHomeResourceLink="#arguments.event.getValue('mc_adminNav.adminHomeResource')#",
				gridID="eventAssetTypes", mode="parent", siteResourceID=this.siteResourceID,
				categoryTreeName='Event Asset Types', categoryLabel="Event Asset Type",
				editPermissionFn=local.editPermissionFn,
				deleteLinkFn="doRemoveAssetType", deletePermissionFn=local.deletePermissionFn,
				addLinkUrl="#this.link.editAssetCategory#&catType=parent", editLinkUrl=this.link.editAssetCategory
			};
			local.strAssetTypesGrid = local.objCategories.getCategoriesGrid(strGridData=local.strAssetTypesGridData);

			local.strAssetNamesGridData = { 
				adminHomeResourceLink="#arguments.event.getValue('mc_adminNav.adminHomeResource')#",
				gridID="eventAssetNames", mode="sub", siteResourceID=this.siteResourceID,
				categoryTreeName="Event Asset Types", categoryHeader="Asset Type", subCategoryHeader="Asset Name", categoryLabel="Event Asset",
				editPermissionFn=local.editPermissionFn,
				deleteLinkFn="doRemoveAssetCategory", deletePermissionFn=local.deletePermissionFn,
				addLinkUrl="#this.link.editAssetCategory#&catType=child", editLinkUrl=this.link.editAssetCategory
			};
			local.strAssetNamesGrid = local.objCategories.getCategoriesGrid(strGridData=local.strAssetNamesGridData);
			
			var currTab = arguments.event.getValue('tab','types');
			if (currTab eq '') currTab = 'types';
			local.arrTabs = [ { title='Types', id='types', fn='dsp_eventAsset_types' }, 
							  { title='Assets', id='cats', fn='dsp_eventAsset_categories' } ];
			
			local.clrQueryString = REReplace(cgi.query_string,'&tab=#currTab#','');
		</cfscript>	

		<cfsavecontent variable="local.data">			
			<cfinclude template="dsp_listAssetCategories.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editAssetCategory" access="public" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.manageAssetTypes')>
			<cfset application.objCommon.redirect("#this.link.message#&message=1")>
		</cfif>

		<cfset arguments.event.paramValue('usageMode','EventAssets')>

		<cfset local.data = createObject("component","model.admin.categories.categories").getEditCategoryForm(event=arguments.event,
			saveCategoryLink=buildCurrentLink(arguments.event,"saveAssetCategory") & "&mode=stream",
			siteResourceID=this.siteResourceID, categoryTreeName="Event Asset Types")>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveAssetCategory" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.manageAssetTypes')>
			<cfset application.objCommon.redirect("#this.link.message#&message=1")>
		</cfif>
		
		<cfset local.strSaveResult =  createObject("component","model.admin.categories.categories").doSaveCategory(event=arguments.event,
			siteResourceID=this.siteResourceID, categoryTreeName="Event Asset Types")>

		<cfif local.strSaveResult.success>
			<cfreturn returnAppStruct(local.strSaveResult.html,"echo")>
		<cfelse>
			<cflocation url="#this.link.editAssetCategory#&err=1&categoryID=#arguments.event.getValue('categoryID',0)#&catType=#arguments.event.getValue('catType')#&mode=direct" addtoken="false">
		</cfif>
	</cffunction>
	
	<!--- Event Roles --->
	<cffunction name="listEventRoles" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.tmpSiteResourceRights = buildRightAssignments(siteResourceID=this.eventAdminSiteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));

			//make sure categoryTree exists
			if (not this.objCategories.getCategoryTreeID(siteResourceID=this.siteResourceID, categoryTreeName='Event Roles'))
				createDefaultEventRoles(siteID=arguments.event.getValue('mc_siteinfo.siteID'));

			local.qryEventRoles = this.objAdminEvent.getEventRoles(siteResourceID=this.eventAdminSiteResourceID);
		</cfscript>

		<cfsavecontent variable="local.eventRoleSection">
			<cfoutput>
				<div class="d-flex mb-2 mt-3">
					<h5 class="mb-0"><span id="evRole_{{roleCategoryID}}">{{roleName}}</span></h5>
					<cfif isDefined('local.tmpSiteResourceRights.manageEventRoles') AND local.tmpSiteResourceRights["manageEventRoles"]>
						<div class="ml-auto d-flex">
							<div class="mr-2"><a href="##" onclick="editEventRole({{roleCategoryID}});"><i class="fa-regular fa-pencil"></i> Edit Role</a></div>
							<div><a href="##" id="btnDelRow_{{roleCategoryID}}" onclick="removeEventRole({{roleCategoryID}},this);"><i class="fa-regular fa-circle-minus text-danger"></i> Remove Role</a></div>
						</div>
					</cfif>
				</div>
			</cfoutput>
		</cfsavecontent>

		<cfset local.arrGridData = arrayNew(1)>
		<cfloop query="local.qryEventRoles">
			<cfset local.thisEvRoleSection = replace(replace(local.eventRoleSection,"{{roleName}}",local.qryEventRoles.categoryName,'all'),"{{roleCategoryID}}",local.qryEventRoles.categoryID,'all')>
			<cfset local.tmpStr = { 
				intro=local.thisEvRoleSection,
				detailID=local.qryEventRoles.categoryID,
				gridext="#this.eventAdminSiteResourceID#_#local.qryEventRoles.currentRow#",
				initGridOnLoad=true,
				controllingSRID=this.eventAdminSiteResourceID, 
				resourceType='EventAdmin', 
				areaName='Role' 
			}>
			<cfset arrayAppend(local.arrGridData,local.tmpStr)>
		</cfloop>
		<cfset local.strEventRolesGrid = createObject("component","model.admin.common.modules.customFields.customFields").getGridHTML(arrGridData=local.arrGridData)>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_eventRoles.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="createDefaultEventRoles" access="private" output="false" returntype="void">
		<cfargument name="siteID" type="numeric" required="true">

		<cfstoredproc procedure="cms_createDefaultEventRoles" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">
		</cfstoredproc>
	</cffunction>

	<cffunction name="editEventRole" access="public" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.manageEventRoles')>
			<cfset application.objCommon.redirect("#this.link.message#&message=1")>
		</cfif>

		<cfset arguments.event.paramValue('usageMode','EventRoles')>

		<cfset local.data = createObject("component","model.admin.categories.categories").getEditCategoryForm(event=arguments.event,
			saveCategoryLink=buildCurrentLink(arguments.event,"saveEventRole") & "&mode=stream",
			siteResourceID=this.siteResourceID, categoryTreeName="Event Roles")>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveEventRole" access="public" output="false" returntype="struct" hint="save event role">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfif NOT arguments.event.getValue('mc_adminToolInfo.myRights.manageEventRoles')>
			<cfset application.objCommon.redirect("#this.link.message#&message=1")>
		</cfif>
		
		<cfset local.strSaveResult =  createObject("component","model.admin.categories.categories").doSaveCategory(event=arguments.event,
			siteResourceID=this.siteResourceID, categoryTreeName="Event Roles")>

		<cfif local.strSaveResult.success>
			<cfset local.categoryID = val(arguments.event.getValue('categoryID',0))>
			<cfset local.categoryName = arguments.event.getTrimValue('categoryName','')>
			<cfset local.oldCategoryName = arguments.event.getTrimValue('old_categoryName','')>

			<cfif local.categoryID gt 0 and compare(local.oldCategoryName,local.categoryName)>
				<cfquery name="local.qryUpdateConditions" datasource="#application.dsn.membercentral.dsn#">
					UPDATE c
					SET c.[verbose] = dbo.ams_getVirtualGroupConditionVerbose(c.conditionID)
					FROM dbo.ams_virtualGroupConditions as c 
					INNER JOIN dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
					INNER JOIN dbo.ams_virtualGroupConditionKeys as ck on ck.conditionKeyID = cv.conditionKeyID
					WHERE c.orgID = #arguments.event.getValue('mc_siteinfo.orgid')#
					and c.fieldCode = 'ev_entry'
					AND ck.conditionKey = 'evRoleID'
					AND cv.conditionValue = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.categoryID#">
				</cfquery>
			</cfif>

			<cfreturn returnAppStruct(local.strSaveResult.html,"echo")>
		<cfelse>
			<cflocation url="#this.link.editEventRole#&msg=3&categoryID=#arguments.event.getValue('categoryID',0)#&catType=parent" addtoken="false">
		</cfif>
	</cffunction>

	<!--- import data --->
	<cffunction name="sampleEventImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","EventImport").generateEventImportTemplate(orgID=arguments.event.getValue('mc_siteInfo.orgID'), siteID=arguments.event.getValue('mc_siteInfo.siteID'), siteCode=arguments.event.getValue('mc_siteInfo.siteCode'), siteResourceID=this.eventAdminSiteResourceID)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	

	<cffunction name="sampleRegistrantsImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","EventImport").generateRegistrantImportTemplate(orgID=arguments.event.getValue('mc_siteInfo.orgID'))>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>	

	<cffunction name="sampleRolesImportTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","EventImport").generateRolesImportTemplate()>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processEventImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","EventImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importEvents(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=this.link.listEvents, data="events")>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="processRegistrantsImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","EventImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importRegistrants(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=this.link.listEvents, data="registrants")>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="processRolesImport" access="private" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.objImport = CreateObject("component","EventImport")>

		<cfsetting requesttimeout="500">

		<cfset local.processResult = local.objImport.importRoles(event=arguments.event)>
		<cfset local.data = local.objImport.showImportResults(strResult=local.processResult, doAgainURL=this.link.listEvents, data="roles")>

		<cfreturn local.data>
	</cffunction>
	
	<cffunction name="editDocument" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.documentID = arguments.event.getValue('documentID',0)>
		<cfset local.eventID = arguments.event.getValue('eventID',0)>
		<cfset local.eventDocumentID = val(arguments.event.getValue('evdid',0))>

		<cfset local.qryDocument = this.objAdminEvent.getEventDocument(eventID=local.eventID, eventDocumentID=local.eventDocumentID)>
		<cfset local.qryDocumentGroupings = this.objAdminEvent.getEventDocumentGroupings(eventID=local.eventID)>
		<cfset local.eventDocumentID = val(local.qryDocument.eventDocumentID)>
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_document.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="saveDocument" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objDocument = CreateObject("component","model.system.platform.document")>		
		<cfset local.siteResourceID = this.objAdminEvent.getSiteResourceIDByEventID(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=arguments.event.getValue('eventID'))>

		<cfif arguments.event.getValue('eventDocumentID',0) gt 0>
			<cfquery name="local.qrySaveEventDoc" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					DECLARE @eventID int, @eventDocumentID int, @docLanguageID int, @docVersionID int, @currentEventDocumentGroupingID int, 
						@eventDocumentGroupingID int;
					SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eventID')#">;
					SET @eventDocumentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eventDocumentID')#">;
					SET @docLanguageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('documentLanguageID')#">;
					SET @eventDocumentGroupingID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('docGrouping',''))#">,0);
					SELECT @docVersionID = max(documentVersionID) from dbo.cms_documentVersions where documentLanguageID = @docLanguageID;

					SELECT @currentEventDocumentGroupingID = eventDocumentGroupingID
					FROM dbo.ev_eventDocuments
					WHERE eventDocumentID = @eventDocumentID;

					BEGIN TRAN;
						<cfif arguments.event.getValue('docGrouping','') EQ 'new' AND len(arguments.event.getTrimValue('newDocGroupingName',''))>
							EXEC dbo.ev_addEventDocumentGrouping @eventID=@eventID, 
								@eventDocumentGrouping=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('newDocGroupingName')#">,
								@eventDocumentGroupingID=@eventDocumentGroupingID OUTPUT;
						</cfif>

						IF ISNULL(@currentEventDocumentGroupingID,0) <> ISNULL(@eventDocumentGroupingID,0) BEGIN
							UPDATE dbo.ev_eventDocuments
							SET eventDocumentGroupingID = @eventDocumentGroupingID,
								fileOrder = 999
							WHERE eventDocumentID = @eventDocumentID;

							EXEC dbo.ev_reorderEventDocuments @eventDocumentGroupingID=@eventDocumentGroupingID, @eventID=@eventID;
						END

						UPDATE dbo.cms_documentLanguages
						SET docTitle = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('docTitle')#">, 
							docDesc = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('docDesc')#">, 
							dateModified = GETDATE()
						WHERE documentLanguageID = @docLanguageID;

						UPDATE dbo.cms_documentVersions
						SET author = NULLIF(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('docAuthor','')#">,''),
							dateModified = GETDATE()
						WHERE documentVersionID = @docVersionID;
					COMMIT TRAN;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

		<cfelse>

			<cfset local.fileUploaded = true>
			<cftry>
				<cfset local.newFile = local.objDocument.uploadFile("form.newFile")>
				<cfset local.fileUploaded = local.newFile.uploadComplete>
			<cfcatch type="Any">
				<cfset local.fileUploaded = false>
			</cfcatch>
			</cftry>

			<cfif local.fileUploaded>
				<cfset local.objDocument.forceFileExtentionIfBlank(local.newFile)>
				<cfset local.insertResults = local.objDocument.insertDocument(siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
							resourceType='ApplicationCreatedDocument', parentSiteResourceID=this.siteResourceID,
							sectionID=0, docTitle=arguments.event.getTrimValue('docTitle'), docDesc=arguments.event.getTrimValue('docDesc'), 
							author=arguments.event.getTrimValue('docAuthor',''), fileData=local.newFile, isActive=1, isVisible=true, 
							contributorMemberID=session.cfcuser.memberdata.memberid, recordedByMemberID=session.cfcuser.memberdata.memberid, 
							oldFileExt=local.newFile.serverFileExt)>
				
				<cfquery name="local.qrySaveEventDoc" datasource="#application.dsn.membercentral.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY

						DECLARE @eventID int, @eventDocumentID int, @documentID int, @eventDocumentGroupingID int;
						SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eventID')#">;
						SET @documentID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.insertResults.documentID#">;
						SET @eventDocumentGroupingID = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(arguments.event.getValue('docGrouping',''))#">,0);

						BEGIN TRAN;
							<cfif arguments.event.getValue('docGrouping','') EQ 'new' AND len(arguments.event.getTrimValue('newDocGroupingName',''))>
								EXEC dbo.ev_addEventDocumentGrouping @eventID=@eventID, 
									@eventDocumentGrouping=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getTrimValue('newDocGroupingName')#">,
									@eventDocumentGroupingID=@eventDocumentGroupingID OUTPUT;
							</cfif>

							INSERT INTO dbo.ev_eventDocuments (eventID, documentID, eventDocumentGroupingID, fileOrder)
							VALUES (@eventID, @documentID, @eventDocumentGroupingID, 999);
								SET @eventDocumentID = SCOPE_IDENTITY();

							EXEC dbo.ev_reorderEventDocuments @eventDocumentGroupingID=@eventDocumentGroupingID, @eventID=@eventID;
						COMMIT TRAN;

						SELECT @eventDocumentID AS eventDocumentID;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>
		</cfif>
			
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadDocumentsTable();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="replaceEventFile" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.eventDocumentID = arguments.event.getValue('evdid',0)>
		<cfset local.documentID = arguments.event.getValue('did',0)>

		<cfset local.eventDocument = this.objAdminEvent.getEventDocument(eventID=arguments.event.getValue('eid',0),eventDocumentID=local.eventDocumentID)>

		<cfif NOT structKeyExists(local.eventDocument, "eventDocumentID") OR NOT val(local.eventDocument.eventDocumentID)>
			<cfset local.data = "Invalid Event Document">
		<cfelse>
			<cfset local.fileUploadSettings = {
				"url": "/?event=proxy.ts_json&c=ADMINEVENT&m=processEventFileUpload",
				"maxFileSize": "750mb"
			}>
			<cfset local.fileUploadSettingsJSON = serializeJSON(local.fileUploadSettings)>

			<cfsavecontent variable="local.data">
				<cfinclude template="frm_replaceEventFile.cfm">
			</cfsavecontent>
		</cfif>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageMassRates" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.qryGetRateGroupings = this.objAdminEvent.getGroupsbyRegistrationID(registrationID=arguments.event.getValue('registrationID'));
			local.qryRegistrationSchedule = this.objAdminEvent.getRegistrationSchedule(siteID=arguments.event.getValue('mc_siteInfo.siteid'), registrationID=arguments.event.getValue('registrationID'));
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_massRates.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="listEventCustomFields" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			local.arrGridData = [];
			local.arrGridData[1] = { 
				title="Cross-Event Custom Fields", 
				intro="These fields will be common to ALL of your Events in the Details Tab and are useful for categorizing and coding your events, or capturing other data specific to each event. This is not to be used for registrant-specific data; registrant custom fields can be defined within each event.",
				gridext="#this.eventAdminSiteResourceID#",
				initGridOnLoad=true,
				controllingSRID=this.eventAdminSiteResourceID, 
				resourceType='EventAdmin', 
				areaName='Event' 
			};
			local.strCrossEventFieldsGrid = createObject("component","model.admin.common.modules.customFields.customFields").getGridHTML(arrGridData=local.arrGridData);
		</cfscript>	

		<cfsavecontent variable="local.data">			
			<cfinclude template="dsp_eventCustomFields.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editTicket" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = this.link.saveTicket;
		local.qryTicket = this.objAdminEvent.getTicket(ticketID=arguments.event.getValue('ticketID',0));

		arguments.event.setValue('registrationID',arguments.event.getValue('registrationID',0));
		arguments.event.setValue('eID',arguments.event.getValue('eID',0));
		arguments.event.setValue('ticketID',val(local.qryTicket.ticketID));
		arguments.event.setValue('ticketName',local.qryTicket.ticketName);
		arguments.event.setValue('ticketDescription',local.qryTicket.ticketDescription);
		arguments.event.setValue('assignToMembers',val(local.qryTicket.assignToMembers));
		arguments.event.setValue('inventory',local.qryTicket.inventory);
		arguments.event.setValue('autoManageInventory',val(local.qryTicket.autoManageInventory));

		arguments.event.setValue('glAccountID',val(local.qryTicket.glAccountID));
		local.tmpStrAccount = CreateObject("component","model.admin.GLAccounts.GLAccounts").getGLAccount(GLAccountID=val(local.qryTicket.glAccountID), orgID=arguments.event.getValue('mc_siteInfo.orgID'));
		arguments.event.setValue('GLAccountPath',local.tmpStrAccount.qryAccount.thePathExpanded);
		
		local.strRevenueGLAcctWidgetData = {
			"label": "Revenue GL Override",
			"btnTxt": "Choose GL Account",
			"glatid": 3,
			"widgetMode": "GLSelector",
			"idFldName": "evGLAccountID",
			"idFldValue": val(arguments.event.getValue('glAccountID',0)),
			"pathFldValue": arguments.event.getValue('GLAccountPath',''),
			"pathNoneTxt": "(No account selected; uses event rate's designated GL Account.)",
			"clearBtnTxt": "Clear Selected GL Account"
		};
		local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_ticket.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveTicket" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	

		<cfscript>
		var local = structNew();
		if (arguments.event.getValue('ticketID',0) gt 0) {
			this.objAdminEvent.updateTicket(ticketID=arguments.event.getValue('ticketID'), ticketName=arguments.event.getTrimValue('ticketName',''), 
					ticketDescription=arguments.event.getTrimValue('ticketDescription',''), assignToMembers=val(arguments.event.getValue('assignToMembers',1)),
					inventory=val(arguments.event.getValue('inventory',0)), autoManageInventory=arguments.event.getValue('autoManageInventory',0), 
					GLAccountID=val(arguments.event.getValue('evGLAccountID',0)));
			local.process = 'update';
		} else {
			local.ticketID = this.objAdminEvent.insertTicket(registrationID=arguments.event.getValue('registrationID'), ticketName=arguments.event.getTrimValue('ticketName',''), 
									ticketDescription=arguments.event.getTrimValue('ticketDescription',''), assignToMembers=val(arguments.event.getValue('assignToMembers',1)),
									inventory=val(arguments.event.getValue('inventory',0)), autoManageInventory=arguments.event.getValue('autoManageInventory',0), 
									GLAccountID=val(arguments.event.getValue('evGLAccountID',0)));
			local.process = 'insert';
		}
		</cfscript>

		<cfif local.process is 'update'>
			<cfset local.qryTicket = this.objAdminEvent.getTicket(ticketID=arguments.event.getValue('ticketID'))>
			<cfsavecontent variable="local.ticketTitleDetails">
				<cfoutput>#local.qryTicket.ticketInventoryCount#<cfif local.qryTicket.inventory gt 0> of #local.qryTicket.inventory#</cfif></cfoutput>
			</cfsavecontent>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<cfif local.process is 'update'>
					top.updateTicketTitle(#arguments.event.getValue('ticketID')#,'#JSStringFormat(trim(local.ticketTitleDetails))#','#JSStringFormat(local.qryTicket.ticketName)#');
				<cfelse>
					top.injectTicketGrid(#local.ticketID#);
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="editTicketPackage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();
		local.formlink = this.link.saveTicketPackage;
		local.qryTicketPackage = this.objAdminEvent.getTicketPackage(ticketPackageID=arguments.event.getValue('ticketPackageID',0));
		local.qryEventScheduleMappedPrice = this.objAdminEvent.getEventScheduleMappedPrice(registrationID=arguments.event.getValue('registrationID'), ticketPackageID=val(local.qryTicketPackage.ticketPackageID));
		local.qryTicketPackageExcludedRates = this.objAdminEvent.getTicketPackageExcludedRates(ticketPackageID=val(local.qryTicketPackage.ticketPackageID));

		arguments.event.setValue('ticketID',arguments.event.getValue('ticketID',0));
		arguments.event.setValue('ticketPackageID',val(local.qryTicketPackage.ticketPackageID));
		arguments.event.setValue('eID',arguments.event.getValue('eID',0));
		arguments.event.setValue('ticketPackageName',local.qryTicketPackage.ticketPackageName);
		arguments.event.setValue('ticketPackageDescription',local.qryTicketPackage.ticketPackageDescription);
		arguments.event.setValue('ticketPackageConfirmationEmailInfo',local.qryTicketPackage.ticketPackageConfirmationEmailInfo);
		arguments.event.setValue('ticketCount',local.qryTicketPackage.ticketCount);
		arguments.event.setValue('adminOnly',val(local.qryTicketPackage.adminOnly));
		arguments.event.setValue('inventory',local.qryTicketPackage.inventory);
		arguments.event.setValue('maxPerRegistrant',local.qryTicketPackage.maxPerRegistrant);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_ticketPackage.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveTicketPackage" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	

		<cfscript>
		var local = structNew();	

		local.arrPriceTypes = arrayNew(1);
		for (local.thisEl in arguments.event.getCollection()) {
			if (left(local.thisEl,12) eq "amountCheck_") {
				local.thisScheduleID = getToken(local.thisEl,2,"_");
				if (arguments.event.getValue('amountCheck_#local.thisScheduleID#') eq 1 AND len(arguments.event.getValue('amount_#local.thisScheduleID#')) AND val(arguments.event.getValue('amount_#local.thisScheduleID#')) gte 0) {
					local.strTemp = { scheduleID=local.thisScheduleID, amount=arguments.event.getValue('amount_#local.thisScheduleID#') };
					arrayAppend(local.arrPriceTypes,local.strTemp);
				}
			}
		}

		if (arguments.event.getValue('ticketPackageID',0) gt 0) {
			this.objAdminEvent.updateTicketPackage(ticketPackageID=arguments.event.getValue('ticketPackageID'), 
					ticketPackageName=arguments.event.getTrimValue('ticketPackageName',''), 
					ticketPackageDescription=arguments.event.getTrimValue('ticketPackageDescription',''),
					ticketPackageConfirmationEmailInfo=arguments.event.getTrimValue('ticketPackageConfirmationEmailInfo',''),
					ticketCount=val(arguments.event.getValue('ticketCount',1)),
					adminOnly=arguments.event.getValue('adminOnly',0), 
					inventory=val(arguments.event.getValue('inventory',0)), 
					maxPerRegistrant=val(arguments.event.getValue('maxPerRegistrant',0)), 
					arrPriceTypes=local.arrPriceTypes);
			local.process = 'update';
		} else {
			local.ticketPackageID =	this.objAdminEvent.insertTicketPackage(ticketID=arguments.event.getValue('ticketID'), 
											ticketPackageName=arguments.event.getTrimValue('ticketPackageName',''), 
											ticketPackageDescription=arguments.event.getTrimValue('ticketPackageDescription',''),
											ticketPackageConfirmationEmailInfo=arguments.event.getTrimValue('ticketPackageConfirmationEmailInfo',''),
											ticketCount=val(arguments.event.getValue('ticketCount',1)),
											adminOnly=arguments.event.getValue('adminOnly',0), 
											inventory=val(arguments.event.getValue('inventory',0)), 
											maxPerRegistrant=val(arguments.event.getValue('maxPerRegistrant',0)),
											arrPriceTypes=local.arrPriceTypes);
			local.process = 'insert';
		}
		</cfscript>	

		<cfif local.process is 'update'>
			<cfset local.qryTicketPackage = this.objAdminEvent.getTicketPackage(ticketPackageID=arguments.event.getValue('ticketPackageID'))>
			<cfsavecontent variable="local.ticketPackageTitleDetails">
				<cfoutput>
					(#local.qryTicketPackage.ticketCount# seat<cfif val(local.qryTicketPackage.ticketCount) gt 1>s</cfif> included)
					&nbsp; &nbsp; 
					#local.qryTicketPackage.ticketPackageInventoryCount#<cfif local.qryTicketPackage.inventory gt 0> of #local.qryTicketPackage.inventory#</cfif>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				<cfif local.process is 'update'>
					top.updateTicketPackageTitle(#arguments.event.getValue('ticketPackageID')#,'#JSStringFormat(local.ticketPackageTitleDetails)#','#JSStringFormat(local.qryTicketPackage.ticketPackageName)#');
				<cfelse>
					top.injectTicketPackageGrid(#arguments.event.getValue('ticketID')#,#local.ticketPackageID#);
				</cfif>
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="processCopyEvent" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.eventID = int(val(arguments.event.getValue('eid',0)))>
		<cfset local.parenteventID = int(val(arguments.event.getValue('peid',0)))>
		<cfset local.formLink = "#this.link.copyEvent#&eID=#local.eventID#&peid=#local.parenteventID#&mode=stream">

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qrySiteEventCustomField">
			select top 1 fieldID 
			from dbo.cf_fields 
			where controllingSiteResourceID = <cfqueryparam value="#this.eventAdminSiteResourceID#" cfsqltype="CF_SQL_INTEGER">
			and usageID = dbo.fn_cf_getUsageID('EventAdmin','Event',NULL);
		</cfquery>
		
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEventRegType">
			select r.registrationTypeID 
			from dbo.ev_registration as r
			inner join dbo.ev_registrationTypes as rt on rt.registrationTypeID = r.registrationTypeID
			inner join dbo.ev_events as e on e.eventid = r.eventid and e.status <> 'D' 
			where r.siteID = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteid')#" cfsqltype="CF_SQL_INTEGER"> 
			and e.eventID = <cfqueryparam value="#local.eventID#" cfsqltype="CF_SQL_INTEGER">
			and r.status <> 'D';
		</cfquery>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_copyEvent.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>	
	</cffunction>
	
	<cffunction name="editRegSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			
			local.scheduleID = int(val(arguments.event.getValue('sID',0)));
			local.registrationID = int(val(arguments.event.getValue('registrationID',0)));
			local.formLink = this.link.saveRegistrationSchedule;
			local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
			local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		</cfscript>

		<cfquery name="local.qryRegistrationSchedule" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteid')#" cfsqltype="CF_SQL_INTEGER">;

			SELECT ep.rangeName, ep.startDate, ep.endDate
			FROM dbo.ev_priceSchedule ep
			INNER JOIN dbo.ev_registration as er ON er.registrationID = ep.registrationID 
				AND er.siteID = @siteID
				AND er.registrationTypeID = 1
			INNER JOIN dbo.ev_events e ON e.siteID = @siteID and e.eventID = er.eventID AND e.status <> 'D'
			WHERE ep.scheduleID = <cfqueryparam value="#local.scheduleID#" cfsqltype="CF_SQL_INTEGER">
			ORDER BY ep.scheduleID;
		</cfquery>
		
		<cfif local.scheduleID eq 0>
			<cfquery name="local.qryRegistrationDates" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;

				DECLARE @siteID int = <cfqueryparam value="#arguments.event.getValue('mc_siteInfo.siteid')#" cfsqltype="CF_SQL_INTEGER">;

				SELECT er.startDate, er.endDate
				FROM dbo.ev_registration as er
				INNER JOIN dbo.ev_events as e ON e.siteID = @siteID and e.eventID = er.eventID AND e.status <> 'D'
				WHERE er.registrationID = <cfqueryparam value="#local.registrationID#" cfsqltype="CF_SQL_INTEGER">
				AND er.siteID = @siteID;
			</cfquery>
			
			<cfset local.startDateTime = local.qryRegistrationDates.startDate>
			<cfset local.endDateTime = local.qryRegistrationDates.endDate>
		<cfelse>
			<cfset local.startDateTime = local.qryRegistrationSchedule.startDate>
			<cfset local.endDateTime = local.qryRegistrationSchedule.endDate>
		</cfif>
		
		<cfset local.startDateTime = local.objTZ.convertTimeZone(dateToConvert=local.startDateTime,fromTimeZone='US/Central',toTimeZone=local.regTimeZone)>
		<cfset local.endDateTime = local.objTZ.convertTimeZone(dateToConvert=local.endDateTime,fromTimeZone='US/Central',toTimeZone=local.regTimeZone)>
		
		<cfset local.startDateTime = "#dateTimeFormat(local.startDateTime,'m/d/yyyy - h:nn tt',local.regTimeZone)#">
		<cfset local.endDateTime = "#dateTimeFormat(local.endDateTime,'m/d/yyyy - h:nn tt',local.regTimeZone)#">
				
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_registrationSchedule.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="saveRegistrationSchedule" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	

		<cfscript>
		var local = structNew();
		local.objTZ = CreateObject("component","model.system.platform.tsTimeZone");
		local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'));
		
		local.startDateTime = ParseDateTime("#replace(arguments.event.getValue('startDateTime',''),' - ',' ')#");
		local.endDateTime = ParseDateTime("#replace(arguments.event.getValue('endDateTime',''),' - ',' ')#");
		
		// convert times from default timezone to central to store in db as central
		local.startDateTime = local.objTZ.convertTimeZone(dateToConvert=local.startDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central');
		local.endDateTime = local.objTZ.convertTimeZone(dateToConvert=local.endDateTime,fromTimeZone=local.regTimeZone,toTimeZone='US/Central');
		
		if (arguments.event.getValue('scheduleID',0) is 0) {
			local.scheduleID = this.objAdminEvent.insertRegistrationSchedule(registrationID=arguments.event.getValue('registrationID',0), rangeName=arguments.event.getValue('rangeName',''), startDateTime=local.startDateTime, endDateTime=local.endDateTime);
		} else {
			this.objAdminEvent.updateRegistrationSchedule(scheduleID=arguments.event.getValue('scheduleID'), registrationID=arguments.event.getValue('registrationID',0), rangeName=arguments.event.getValue('rangeName',''), startDateTime=local.startDateTime, endDateTime=local.endDateTime);
		}
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadRegScheduleTable();
				try { top.reloadRatesTable(); } catch (e){ }; /* reload the rates grid that shows schedule name */
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="manageRegTickets" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	
		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","eventReg").manageRegTickets(event=arguments.event)>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageRegTicketSelections" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	
		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","eventReg").manageRegTicketSelections(event=arguments.event)>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="manageRegTicketFields" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	
		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","eventReg").manageRegTicketFields(event=arguments.event)>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="loadRegConfirmation" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">	
		<cfset var local = structNew()>
		<cfset local.data = CreateObject("component","eventReg").loadRegConfirmation(event=arguments.event)>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getTicketGridsStructure" access="private" output="false" returntype="struct">
		<cfargument name="registrationID" type="numeric" required="true">
		<cfargument name="objResourceCustomFields" type="any" required="true">

		<cfset var local = structNew()>
		<cfset local.baseURL = "/?mode=stream&pg=admin&mca_ajaxlib=resourceCustomFields&mca_ajaxfunc=">
		<cfset local.strTicket = { "js":"", "html"="", "arrGridExt"=[], "idExt"="" }>

		<cfquery name="local.qryTickets" datasource="#application.dsn.membercentral.dsn#">
			select t.ticketID, t.ticketName, t.inventory as ticketInventory, 
				(select count(rpid.seatID) 
					from dbo.ev_registrantPackageInstanceSeats as rpid
					inner join dbo.ev_registrantPackageInstances as rpi on rpi.instanceID = rpid.instanceID and rpi.status = 'A'
					inner join dbo.ev_ticketPackages as etp1 on etp1.ticketPackageID = rpi.ticketPackageID
					inner join dbo.ev_tickets as et on et.ticketID = etp1.ticketID
					where et.ticketID = t.ticketID
					and rpid.status = 'A') as ticketInventoryCount,
				tp.ticketPackageID, tp.ticketPackageName, tp.ticketCount, tp.inventory as ticketPackageInventory,
				(select count(rpi.instanceID) 
					from dbo.ev_registrantPackageInstances as rpi
					inner join dbo.ev_ticketPackages as etp2 on etp2.ticketPackageID = rpi.ticketPackageID
					where etp2.ticketPackageID = tp.ticketPackageID
					and rpi.status = 'A') as ticketPackageInventoryCount,
				canDeleteTicket = case when exists (select 1 
					from dbo.ev_registrantPackageInstances as rpi
					inner join dbo.ev_ticketPackages as tp on tp.ticketPackageID = rpi.ticketPackageID
					where tp.ticketID = t.ticketID) then 0 else 1 end,
				canDeleteTicketPackage = case when exists (select 1 
					from dbo.ev_registrantPackageInstances 
					where ticketPackageID = tp.ticketPackageID) then 0 else 1 end,
				ROW_NUMBER() OVER(ORDER BY t.sortOrder, t.ticketID, tp.sortOrder, tp.ticketPackageID) as row
			from dbo.ev_tickets as t
			left outer join dbo.ev_ticketPackages as tp on tp.ticketID = t.ticketID
			where t.registrationID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.registrationID#">
			order by row
		</cfquery>

		<cfset local.arrGridData = arrayNew(1)>
		<cfoutput query="local.qryTickets" group="ticketID">
			<cfquery name="local.qryTicket" dbtype="query" maxrows="1">
				select ticketID, ticketName, ticketInventory, ticketInventoryCount, canDeleteTicket
				from [local].qryTickets
				where ticketID = #local.qryTickets.ticketID#
			</cfquery>
			<cfquery name="local.qryMoveTicketUp" dbtype="query" maxrows="1">
				select ticketID
				from [local].qryTickets
				where row < #local.qryTickets.row#
				and ticketID <> #local.qryTickets.ticketID#
			</cfquery>
			<cfquery name="local.qryMoveTicketDown" dbtype="query" maxrows="1">
				select ticketID
				from [local].qryTickets
				where row > #local.qryTickets.row#
				and ticketID <> #local.qryTickets.ticketID#
			</cfquery>

			<cfset QueryAddColumn(local.qryTicket, "canMoveUp", "bit", [local.qryMoveTicketUp.recordcount neq 0])>
			<cfset QueryAddColumn(local.qryTicket, "canMoveDown", "bit", [local.qryMoveTicketDown.recordcount neq 0])>

			<cfsavecontent variable="local.thisTicketSectionHeader">
				<cfinclude template="frm_ticketSectionHeader.cfm">
			</cfsavecontent>
			<cfsavecontent variable="local.thisTicketSectionFooter">
					</div>
				</div>
			</cfsavecontent>

			<cfset local.thisTicketFooter = "">

			<cfoutput>
				<cfif val(local.qryTickets.ticketPackageID)>
					<cfquery name="local.qryTicketPackage" dbtype="query" maxrows="1">
						select ticketID, ticketName, ticketPackageID, ticketPackageName, ticketCount, 
							ticketPackageInventory, ticketPackageInventoryCount, canDeleteTicketPackage
						from [local].qryTickets
						where ticketPackageID = #local.qryTickets.ticketPackageID#
					</cfquery>
					<cfquery name="local.qryMovePackageUp" dbtype="query" maxrows="1">
						select ticketID
						from [local].qryTickets
						where row < #local.qryTickets.row#
						and ticketID = #local.qryTickets.ticketID#
					</cfquery>
					<cfquery name="local.qryMovePackageDown" dbtype="query" maxrows="1">
						select ticketID
						from [local].qryTickets
						where row > #local.qryTickets.row#
						and ticketID = #local.qryTickets.ticketID#
					</cfquery>

					<cfset QueryAddColumn(local.qryTicketPackage, "canMoveUp", "bit", [local.qryMovePackageUp.recordcount neq 0])>
					<cfset QueryAddColumn(local.qryTicketPackage, "canMoveDown", "bit", [local.qryMovePackageDown.recordcount neq 0])>

					<cfsavecontent variable="local.thisTicketPackageHeader">
						<cfinclude template="frm_ticketPackageSectionHeader.cfm">
					</cfsavecontent>
					<cfsavecontent variable="local.thisTicketPackageFooter">
							</div>
						</div>
					</cfsavecontent>

					<cfset local.strGridData = { 
						header=local.thisTicketPackageHeader,
						footer=local.thisTicketPackageFooter,
						btnClassList="btn btn-sm",
						detailID=local.qryTickets.ticketPackageID,
						gridext="#this.eventAdminSiteResourceID#_tp_#local.qryTickets.ticketPackageID#",
						gridMode="compact",
						initGridOnLoad=false,
						controllingSRID=this.eventAdminSiteResourceID, 
						resourceType='Event', 
						areaName='TicketPackage'
					}>
					<cfset local.strTicket.arrGridExt.append(local.strGridData.gridext)>

					<cfset local.ticketPackageGridHTML = arguments.objResourceCustomFields.getGridBoxHTML(strGridData=local.strGridData)>
					<cfsavecontent variable="local.thisTicketPackageJS">
						<script type="text/javascript">
							#arguments.objResourceCustomFields.getGridJS(strGridData=local.strGridData, baseURL=local.baseURL)#
						</script>
						<style>#arguments.objResourceCustomFields.getGridCSS(strGridData=local.strGridData)#</style>
					</cfsavecontent>
					<cfset local.strTicket.js = local.strTicket.js & local.thisTicketPackageJS>
					<cfset local.thisTicketFooter = local.thisTicketFooter & local.ticketPackageGridHTML>
				</cfif>
			</cfoutput>

			<cfset local.thisTicketFooter = local.thisTicketFooter & local.thisTicketSectionFooter>

			<cfset local.strTicketGrid = {
				header=local.thisTicketSectionHeader,
				footer=local.thisTicketFooter,
				btnClassList="btn btn-sm",
				detailID=local.qryTickets.ticketID,
				gridext="#this.eventAdminSiteResourceID#_t_#local.qryTickets.ticketID#",
				gridMode="compact",
				initGridOnLoad=false,
				controllingSRID=this.eventAdminSiteResourceID, 
				resourceType='Event', 
				areaName='Ticket',
				gridClassList="pb-2" 
			}>
			<cfset local.arrGridData.append(local.strTicketGrid)>
			<cfset local.strTicket.arrGridExt.append(local.strTicketGrid.gridext)>
		</cfoutput>

		<cfset local.strEventTicketsGrid = arguments.objResourceCustomFields.getGridHTML(arrGridData=local.arrGridData)>
		
		<cfset local.strTicket.js = local.strEventTicketsGrid.js & local.strTicket.js>
		<cfset local.strTicket.html = local.strEventTicketsGrid.html>
		<cfset local.strTicket.idExt = local.strEventTicketsGrid.idExt>

		<cfreturn local.strTicket>
	</cffunction>

	<cffunction name="loadResourceGrid" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.baseURL = "/?mode=stream&pg=admin&mca_ajaxlib=resourceCustomFields&mca_ajaxfunc=">
		<cfset local.returnStruct = { html="", js="" }>

		<cfswitch expression="#arguments.event.getValue('usageAN','')#">
			<cfcase value="ticket">
				<cfquery name="local.qryTicket" datasource="#application.dsn.membercentral.dsn#">
					select t.ticketID, t.ticketName, t.inventory as ticketInventory, 
						(select count(rpid.seatID) 
							from dbo.ev_registrantPackageInstanceSeats as rpid
							inner join dbo.ev_registrantPackageInstances as rpi on rpi.instanceID = rpid.instanceID and rpi.status = 'A'
							inner join dbo.ev_ticketPackages as etp1 on etp1.ticketPackageID = rpi.ticketPackageID
							inner join dbo.ev_tickets as et on et.ticketID = etp1.ticketID
							where et.ticketID = t.ticketID
							and rpid.status = 'A') as ticketInventoryCount,
						case when t.sortOrder = 1 then 0 else 1 end as canMoveUp,
						case 
						when t.sortOrder = (select max(sortOrder) from dbo.ev_tickets where registrationid = t.registrationid) then 0 
						else 1 end as canMoveDown, 
						canDeleteTicket = case when exists (select 1 
											from dbo.ev_registrantPackageInstances as rpi
											inner join dbo.ev_ticketPackages as tp on tp.ticketPackageID = rpi.ticketPackageID
											where tp.ticketID = t.ticketID) then 0 else 1 end
					from dbo.ev_tickets as t
					where t.ticketID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('detailID',0)#">
				</cfquery>
				
				<cfsavecontent variable="local.thisTicketSectionHeader">
					<cfinclude template="frm_ticketSectionHeader.cfm">
				</cfsavecontent>
				<cfsavecontent variable="local.thisTicketSectionFooter">
					<cfoutput></div></div></cfoutput>
				</cfsavecontent>

				<cfset local.strGridData = {
					header=local.thisTicketSectionHeader,
					footer=local.thisTicketSectionFooter,
					btnClassList="btn btn-sm",
					detailID=local.qryTicket.ticketID,
					gridext="#arguments.event.getValue('csrid',0)#_t_#local.qryTicket.ticketID#",
					gridMode="compact",
					initGridOnLoad=true,
					controllingSRID=arguments.event.getValue('csrid',0), 
					resourceType='Event', 
					areaName='Ticket',
					gridClassList="pb-2" 
				}>
			</cfcase>
			<cfcase value="ticketPackage">
				<cfquery name="local.qryTicketPackage" datasource="#application.dsn.membercentral.dsn#">
					select tp.ticketID, tp.ticketPackageID, tp.ticketPackageName, t.ticketName, tp.ticketCount, tp.inventory as ticketPackageInventory,
						(select count(rpi.instanceID) 
							from dbo.ev_registrantPackageInstances as rpi
							inner join dbo.ev_ticketPackages as etp2 on etp2.ticketPackageID = rpi.ticketPackageID
							where etp2.ticketPackageID = tp.ticketPackageID
							and rpi.status = 'A') as ticketPackageInventoryCount,
						case when tp.sortOrder = 1 then 0 else 1 end as canMoveUp,
						case 
						when tp.sortOrder = (select max(sortOrder) from dbo.ev_ticketPackages where ticketID = tp.ticketID) then 0 
						else 1 end as canMoveDown, 
						canDeleteTicketPackage = case when exists (select 1 
													from dbo.ev_registrantPackageInstances 
													where ticketPackageID = tp.ticketPackageID) then 0 else 1 end
					from dbo.ev_ticketPackages as tp
					inner join dbo.ev_tickets as t on t.ticketID = tp.ticketID
					where tp.ticketPackageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('detailID',0)#">
				</cfquery>

				<cfsavecontent variable="local.thisTicketPackageHeader">
					<cfinclude template="frm_ticketPackageSectionHeader.cfm">
				</cfsavecontent>
				<cfsavecontent variable="local.thisTicketPackageFooter">
					<cfoutput></div></div></cfoutput>
				</cfsavecontent>

				<cfset local.strGridData = { 
					header=local.thisTicketPackageHeader,
					footer=local.thisTicketPackageFooter,
					btnClassList="btn btn-sm",
					detailID=local.qryTicketPackage.ticketPackageID,
					gridext="#arguments.event.getValue('csrid',0)#_tp_#local.qryTicketPackage.ticketPackageID#",
					gridMode="compact",
					initGridOnLoad=true,
					controllingSRID=arguments.event.getValue('csrid',0), 
					resourceType='Event', 
					areaName='TicketPackage'
				}>
			</cfcase>
		</cfswitch>

		<cfset local.objCustomFields = createObject("component","model.admin.common.modules.customFields.customFields")>
		<cfset local.gridhtml = local.objCustomFields.getGridBoxHTML(strGridData=local.strGridData)>
		<cfset local.gridjs = local.objCustomFields.getGridJS(strGridData=local.strGridData, baseURL=local.baseURL)>
		<cfset local.gridcss = local.objCustomFields.getGridCSS(strGridData=local.strGridData)>

		<cfsavecontent variable="local.customJS">
			<cfoutput>
			<script language="javascript">
				#local.gridjs#
			</script>
			<style>#local.gridcss#</style>
			</cfoutput>
		</cfsavecontent>
		<cfhtmlhead text="#local.customJS#">

		<cfreturn returnAppStruct(local.gridhtml,"echo")>
	</cffunction>

	<cffunction name="viewSigninSheetPrompt" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.strFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector").getFieldSetSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="fsid", selectedFieldSetName='Events Download Registrants Standard', allowBlankOption=false, inlinePreviewSectionID="divSigninSheet",fieldLabel='Member Field Set that includes name format for members on sign-in sheet.')>

		<cfsavecontent variable="local.data">
			<cfinclude template="frm_generateSigninSheet.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewSigninSheet" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfset local.qryRegistrants = CreateObject("component","model.admin.events.event").getRegistrantsFromFilters(event=arguments.event, mode="regTabSignInSheet")>
			<cfif not local.qryRegistrants.recordCount>
				<cfthrow message="No registrants found.">
			</cfif>

			<cfset local.strSheet = CreateObject("component","model.admin.events.signinsheet").generateSheet(eventID=arguments.event.getValue('eID',0), qryRegistrants=local.qryRegistrants)>
			<cfif len(local.strSheet.sheetURL)>
				<cflocation url="#local.strSheet.sheetURL#" addtoken="No">
			<cfelse>
				<cfthrow message="Unable to generate sign-in sheet.">
			</cfif>
		<cfcatch> 					
			<cfset application.objError.extendRequestTimeout()>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="m-2"><h4>Sorry</h4><div>We encountered an error generating the sign-in sheet. Try again or contact us for assistance.</div></div>
				</cfoutput>
			</cfsavecontent>		
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewSigninRSVPsheet" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfsetting requesttimeout="300">

		<cftry>
			<cfset local.qryRegistrants = CreateObject("component","model.admin.events.event").getRSVPRegistrants(event=arguments.event)>

			<cfset local.strSheet = CreateObject("component","model.admin.events.signinsheet").generateSheet(eventID=arguments.event.getValue('eID',0), qryRegistrants=local.qryRegistrants)>
			<cfif len(local.strSheet.sheetURL)>
				<cflocation url="#local.strSheet.sheetURL#" addtoken="No">
			<cfelse>
				<cfthrow message="Unable to generate sign-in sheet.">
			</cfif>
		<cfcatch> 					
			<cfset application.objError.extendRequestTimeout()>
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfsavecontent variable="local.data">
				<cfoutput>
					<div class="m-2"><h4>Sorry</h4><div>We encountered an error generating the sign-in sheet. Try again or contact us for assistance.</div></div>
				</cfoutput>
			</cfsavecontent>		
		</cfcatch>
		</cftry>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="massEmailRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.strFilters = structNew()>
		<cfset structInsert(local.strFilters, 'eID', arguments.event.getValue('eID',0))>
		<cfset structInsert(local.strFilters, 'evDateFrom', arguments.event.getValue('evDateFrom',''))>
		<cfset structInsert(local.strFilters, 'evDateTo', arguments.event.getValue('evDateTo',''))>
		<cfset structInsert(local.strFilters, 'evCalendar', arguments.event.getValue('evCalendar',0))>
		<cfset structInsert(local.strFilters, 'evCategory', arguments.event.getValue('evCategory',0))>
		<cfset structInsert(local.strFilters, 'evKeyword', arguments.event.getValue('evKeyword',''))>
		<cfset structInsert(local.strFilters, 'evReportCode', arguments.event.getValue('evReportCode',''))>
		<cfset structInsert(local.strFilters, 'evEventType', arguments.event.getValue('evEventType','all'))>
		<cfset structInsert(local.strFilters, 'rDateFrom', arguments.event.getValue('rDateFrom',''))>
		<cfset structInsert(local.strFilters, 'rDateTo', arguments.event.getValue('rDateTo',''))>
		<cfset structInsert(local.strFilters, 'rAttended', arguments.event.getValue('rAttended',''))>
		<cfset structInsert(local.strFilters, 'rBillFrom', arguments.event.getValue('rBillFrom',''))>
		<cfset structInsert(local.strFilters, 'rBillTo', arguments.event.getValue('rBillTo',''))>
		<cfset structInsert(local.strFilters, 'rDuesFrom', arguments.event.getValue('rDuesFrom',''))>
		<cfset structInsert(local.strFilters, 'rDuesTo', arguments.event.getValue('rDuesTo',''))>
		<cfset structInsert(local.strFilters, 'rEvRole', arguments.event.getValue('rEvRole',''))>
		<cfset structInsert(local.strFilters, 'rEvFormCompletion', arguments.event.getValue('rEvFormCompletion',''))>
		<cfset structInsert(local.strFilters, 'rAssocType', arguments.event.getValue('rAssocType',''))>
		<cfset structInsert(local.strFilters, 'rAssociatedMemberID', arguments.event.getValue('rAssociatedMemberID',''))>
		<cfset structInsert(local.strFilters, 'rAssociatedGroupID', arguments.event.getValue('rAssociatedGroupID',''))>
		<cfset structInsert(local.strFilters, 'rCreditType', arguments.event.getValue('rCreditType',''))>
		<cfset structInsert(local.strFilters, 'rStatus', arguments.event.getValue('rStatus',0))>
		<cfset structInsert(local.strFilters, 'rEvRate', arguments.event.getValue('rEvRate',''))>

		<cfset local.rc = arguments.event.getCollection()>
		<cfloop collection="#local.rc#" item="local.thisFormField">
			<cfif left(local.thisFormField,8) eq "rFieldID" and len(local.rc[local.thisFormField])>
				<cfset local.arrCFID = listToArray(local.rc[local.thisFormField],'_')>
				<cfset structInsert(local.strFilters, local.thisFormField, local.rc[local.thisFormField])>
				<cfif isValid("integer",local.arrCFID[1]) and structKeyExists(local.rc,"RF_#local.arrCFID[1]#_exp") and len(local.rc['RF_#local.arrCFID[1]#_exp'])>
					<cfset structInsert(local.strFilters, "RF_#local.arrCFID[1]#_exp", local.rc['RF_#local.arrCFID[1]#_exp'])>
					<cfif structKeyExists(local.rc,"RF_#local.arrCFID[1]#") and len(local.rc['RF_#local.arrCFID[1]#'])>
						<cfset structInsert(local.strFilters, "RF_#local.arrCFID[1]#", local.rc['RF_#local.arrCFID[1]#'])>
					<cfelseif structKeyExists(local.rc,"RF_#local.arrCFID[1]#_lower") and structKeyExists(local.rc,"RF_#local.arrCFID[1]#_upper")>
						<cfset structInsert(local.strFilters, "RF_#local.arrCFID[1]#_lower", local.rc['RF_#local.arrCFID[1]#_lower'])>
						<cfset structInsert(local.strFilters, "RF_#local.arrCFID[1]#_upper", local.rc['RF_#local.arrCFID[1]#_upper'])>
					</cfif>
				</cfif>
			</cfif>
		</cfloop>

		<cfset local.strResourceTitle = { resourceTitle='Registrants', resourceTitleDesc='', templateEditorLabel='Compose your message.' }>
		
		<cfset local.argumentCollection = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), resourceType='Events', 
					recipientType='Registrants', strResourceTitle=local.strResourceTitle, strFilters=local.strFilters, arrRecipientModes=arrayNew(1),
					mergeCodeInstructionsLink="#buildCurrentLink(arguments.event,'showMergeCodeInstructions')#&eid=#arguments.event.getValue('eID',0)#&incEV=1&mode=stream",
					emailTemplateTreeCode="ETEVENTS" }>

		<cfset local.data = CreateObject("component","model.admin.common.modules.massEmails.massEmails").prepMassEmails(argumentCollection=local.argumentCollection)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="getEventTimeinAllTimeZones" access="public" output="false" returntype="struct">
		<cfargument name="startTimeToConvert" type="date" required="true">
		<cfargument name="endTimeToConvert" type="date" required="true">
		<cfargument name="selectedTimeZoneId" type="numeric" required="true">
		
		<cfset var local = structNew()>
		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.qryTimeZones = local.objTSTZ.getTimeZones()>
		<cfset local.qrySelectedTimeZoneDetail = local.objTSTZ.getTZAllFromTZID(arguments.selectedTimeZoneId)>

		<!--- Get time in diff timezones --->
		<!--- converts saved time zone to all other timezones. While 3rd param of datetimeformat considers TZ to format date, the format string intentionally leaves off timezone so that calling code gets the data returned back as expected  --->
		<cfset local.returnStruct = {}>

		<cfloop query="local.qryTimeZones">
			<cfset local.returnStruct[local.qryTimeZones.timeZoneCode] = {
				"id": local.qryTimeZones.timeZoneID,
				"starttime": datetimeFormat(local.objTSTZ.convertTimeZone(dateToConvert=arguments.startTimeToConvert,fromTimeZone=local.qrySelectedTimeZoneDetail.timeZoneCode,toTimeZone=local.qryTimeZones.timeZoneCode),"m/d/yyyy h:nn tt",local.qryTimeZones.timeZoneCode),
				"endtime": datetimeFormat(local.objTSTZ.convertTimeZone(dateToConvert=arguments.endTimeToConvert,fromTimeZone=local.qrySelectedTimeZoneDetail.timeZoneCode,toTimeZone=local.qryTimeZones.timeZoneCode),"m/d/yyyy h:nn tt",local.qryTimeZones.timeZoneCode)
			}>
		</cfloop>
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="massPrintBadges" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		
		<cfset local.strFilters = structNew()>
		<cfset structInsert(local.strFilters, 'eID', arguments.event.getValue('eID',0))>
		<cfset structInsert(local.strFilters, 'evDateFrom', arguments.event.getValue('evDateFrom',''))>
		<cfset structInsert(local.strFilters, 'evDateTo', arguments.event.getValue('evDateTo',''))>
		<cfset structInsert(local.strFilters, 'evCalendar', arguments.event.getValue('evCalendar',0))>
		<cfset structInsert(local.strFilters, 'evCategory', arguments.event.getValue('evCategory',0))>
		<cfset structInsert(local.strFilters, 'evKeyword', arguments.event.getValue('evKeyword',''))>
		<cfset structInsert(local.strFilters, 'evReportCode', arguments.event.getValue('evReportCode',''))>
		<cfset structInsert(local.strFilters, 'evEventType', arguments.event.getValue('evEventType','all'))>
		<cfset structInsert(local.strFilters, 'rDateFrom', arguments.event.getValue('rDateFrom',''))>
		<cfset structInsert(local.strFilters, 'rDateTo', arguments.event.getValue('rDateTo',''))>
		<cfset structInsert(local.strFilters, 'rAttended', arguments.event.getValue('rAttended',''))>
		<cfset structInsert(local.strFilters, 'rBillFrom', arguments.event.getValue('rBillFrom',''))>
		<cfset structInsert(local.strFilters, 'rBillTo', arguments.event.getValue('rBillTo',''))>
		<cfset structInsert(local.strFilters, 'rDuesFrom', arguments.event.getValue('rDuesFrom',''))>
		<cfset structInsert(local.strFilters, 'rDuesTo', arguments.event.getValue('rDuesTo',''))>
		<cfset structInsert(local.strFilters, 'rEvRole', arguments.event.getValue('rEvRole',''))>
		<cfset structInsert(local.strFilters, 'rEvFormCompletion', arguments.event.getValue('rEvFormCompletion',''))>
		<cfset structInsert(local.strFilters, 'rAssocType', arguments.event.getValue('rAssocType',''))>
		<cfset structInsert(local.strFilters, 'rAssociatedMemberID', arguments.event.getValue('rAssociatedMemberID',''))>
		<cfset structInsert(local.strFilters, 'rAssociatedGroupID', arguments.event.getValue('rAssociatedGroupID',''))>
		<cfset structInsert(local.strFilters, 'rCreditType', arguments.event.getValue('rCreditType',''))>
		<cfset structInsert(local.strFilters, 'rStatus', arguments.event.getValue('rStatus',0))>
		<cfset structInsert(local.strFilters, 'rEvRate', arguments.event.getValue('rEvRate',''))>

		<cfset local.rc = arguments.event.getCollection()>
		<cfloop collection="#local.rc#" item="local.thisFormField">
			<cfif left(local.thisFormField,8) eq "rFieldID" and len(local.rc[local.thisFormField])>
				<cfset local.arrCFID = listToArray(local.rc[local.thisFormField],'_')>
				<cfset structInsert(local.strFilters, local.thisFormField, local.rc[local.thisFormField])>
				<cfif isValid("integer",local.arrCFID[1]) and structKeyExists(local.rc,"RF_#local.arrCFID[1]#_exp") and len(local.rc['RF_#local.arrCFID[1]#_exp'])>
					<cfset structInsert(local.strFilters, "RF_#local.arrCFID[1]#_exp", local.rc['RF_#local.arrCFID[1]#_exp'])>
					<cfif structKeyExists(local.rc,"RF_#local.arrCFID[1]#") and len(local.rc['RF_#local.arrCFID[1]#'])>
						<cfset structInsert(local.strFilters, "RF_#local.arrCFID[1]#", local.rc['RF_#local.arrCFID[1]#'])>
					<cfelseif structKeyExists(local.rc,"RF_#local.arrCFID[1]#_lower") and structKeyExists(local.rc,"RF_#local.arrCFID[1]#_upper")>
						<cfset structInsert(local.strFilters, "RF_#local.arrCFID[1]#_lower", local.rc['RF_#local.arrCFID[1]#_lower'])>
						<cfset structInsert(local.strFilters, "RF_#local.arrCFID[1]#_upper", local.rc['RF_#local.arrCFID[1]#_upper'])>
					</cfif>
				</cfif>
			</cfif>
		</cfloop>

		<cfset local.strResourceTitle = { resourceTitle='Registrants', resourceTitleDesc='', templateEditorLabel='Create your badge.' }>
		<cfset local.qryGetEventDefaultBadgeDetails = this.objAdminEvent.getEventDefaultBadgeDetails(eID =arguments.event.getValue('eID',0), siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		
		<cfset local.argumentCollection = { siteID=arguments.event.getValue('mc_siteinfo.siteid'), siteCode=arguments.event.getValue('mc_siteinfo.sitecode'), defaultBadgeTemplateID=#local.qryGetEventDefaultBadgeDetails.defaultBadgeTemplateID# ,badgeDeviceID=#local.qryGetEventDefaultBadgeDetails.badgeDeviceID#,
					resourceType='Events', recipientType='Registrants', strResourceTitle=local.strResourceTitle, strFilters=local.strFilters,
					mergeCodeInstructionsLink="#buildCurrentLink(arguments.event,'showMergeCodeInstructions')#&eid=#arguments.event.getValue('eID',0)#&incEV=1&mode=stream",
					previewMembersLink="#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getRegistrantsForPrintingBadges&mode=stream",
					badgeTemplateTreeCode="BTEVENTS" }>

		<cfset local.data = CreateObject("component","model.admin.badges.massPrintBadges").massPrintBadgesPreparation(argumentCollection=local.argumentCollection)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="sampleMassUpdateRegTemplate" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.data = "The sample update template file could not be generated. Contact MemberCentral for assistance.">
		
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "RegistrantUpdateTemplate.csv">
		
		<cfset local.qryRegistrantUpdateColumns = getRegistrantUpdateFieldDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), eventID=arguments.event.getValue('eID',0), mode='uploadTemplate')>

		<cffile action="WRITE" file="#local.strFolder.folderPath#/#local.reportFileName#" output="#valueList(local.qryRegistrantUpdateColumns.columnName)#">
		
		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="getRegistrantUpdateFieldDetails" access="private" output="false" returntype="query">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eventID" type="numeric" required="true">
		<cfargument name="arrUpdateColumnDetails" type="array" required="false" default="#arrayNew(1)#">
		<cfargument name="mode" type="string" required="true" hint="upload, processTemplate, and uploadTemplate">

		<cfset var local = structNew()>
		<cfset local.columnNameRegex = "[^A-Za-z0-9_\-\&\(\)\:\/\s]">
		<cfset local.EventAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin',siteID=arguments.siteID)>

		<cfquery name="local.qryRegistrantUpdateColumns" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;

			IF OBJECT_ID('tempdb..##tmpRegistrantUpdateColumnHeaders') IS NOT NULL 
				DROP TABLE ##tmpRegistrantUpdateColumnHeaders;
			IF OBJECT_ID('tempdb..##tmpRegistrantUpdateColumns') IS NOT NULL 
				DROP TABLE ##tmpRegistrantUpdateColumns;
			IF OBJECT_ID('tempdb..##tmpRegistrantUpdateColumnDetails') IS NOT NULL 
				DROP TABLE ##tmpRegistrantUpdateColumnDetails;
			
			CREATE TABLE ##tmpRegistrantUpdateColumnHeaders (headerRowID int, header varchar(200));
			CREATE TABLE ##tmpRegistrantUpdateColumns (columnID int IDENTITY(1,1), columnName varchar(500), dataTypeCode varchar(12), isRequired bit, headerRowID int, 
				defaultColValue varchar(max), skipUpdate bit default(1));
			CREATE TABLE ##tmpRegistrantUpdateColumnDetails (columnID int, mappedColValue varchar(500), mappedColOverrideValue varchar(max));

			DECLARE @siteID int, @usageID int, @eventID int, @eventAdminSRID int, @eventRolesCTID int, @eventSRID int;
			
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
			SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">;
			SET @eventAdminSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.EventAdminSRID#">;

			SELECT @eventSRID = siteResourceID 
			FROM dbo.ev_events
			WHERE eventID = @eventID;

			/* headers */
			INSERT INTO ##tmpRegistrantUpdateColumnHeaders (headerRowID, header)
			select 1, 'Registrant'
				union all
			select 2, 'Event Role Fields'
				union all
			select 3, 'Additional Registrant Information '
				union all
			select 4, 'Event Specifics';

			<cfif arrayLen(arguments.arrUpdateColumnDetails)>
				<cfloop array="#arguments.arrUpdateColumnDetails#" index="local.thisColumn">
					INSERT INTO ##tmpRegistrantUpdateColumnDetails (columnID, mappedColValue, mappedColOverrideValue)
					VALUES (#int(val(local.thisColumn.columnID))#,'#replace(local.thisColumn.mappedColValue,"'","''","ALL")#','#replace(local.thisColumn.mappedColOverrideValue,"'","''","ALL")#');
				</cfloop>
			</cfif>
			
			/* RegistrantID */
			INSERT INTO ##tmpRegistrantUpdateColumns (columnName, dataTypeCode, isRequired, headerRowID, skipUpdate)
			VALUES ('RegistrantID', 'INTEGER', 1, 1, 0);
			

			/* Event Role Fields */
			select @usageID = dbo.fn_cf_getUsageID('EventAdmin','Role',null);
			select @eventRolesCTID = dbo.fn_getCategoryTreeIDForSiteResourceIDandTree(@eventAdminSRID,'Event Roles');

			INSERT INTO ##tmpRegistrantUpdateColumns (columnName, dataTypeCode, isRequired, headerRowID)
			select c.categoryName + '_' + f.fieldReference, ft.dataTypeCode, f.isRequired, 2
			from dbo.cf_fields as f 
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
				and f.usageID = @usageID
				and f.controllingSiteResourceID = @eventAdminSRID
			inner join dbo.cms_categories as c on c.categoryID = f.detailID
			where c.categoryTreeID = @eventRolesCTID
			and c.isActive = 1 
			and c.parentCategoryID is NULL
			and len(f.fieldReference) > 0
			and f.isActive = 1
			and ft.displayTypeCode not in ('LABEL','DOCUMENT')
			and 1 = case 
					when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
					else 1 end
			order by c.categoryID, f.fieldOrder;


			/* Registrant Non-Monetary Fields */
			set @usageID = null;
			select @usageID = dbo.fn_cf_getUsageID('Event','NonMonetary','Registrant');

			INSERT INTO ##tmpRegistrantUpdateColumns (columnName, dataTypeCode, isRequired, headerRowID)
			select f.fieldReference, ft.dataTypeCode, f.isRequired, 3
			from dbo.cf_fields as f
			inner join dbo.cf_fieldUsages as fu on fu.usageID = f.usageID
			inner join dbo.cf_fieldTypes as ft on ft.fieldTypeID = f.fieldTypeID
			where fu.usageID = @usageID
			and f.controllingSiteResourceID = @eventSRID
			and len(f.fieldReference) > 0
			and f.isActive = 1
			and ft.displayTypeCode <> 'LABEL'
			and 1 = case 
					when ft.displayTypeCode in ('SELECT','RADIO','CHECKBOX') then case when exists(select 1 from dbo.cf_fieldValues where fieldID = f.fieldID) then 1 else 0 end 
					else 1 end
			order by f.fieldOrder;

			<cfif arguments.mode is 'uploadTemplate'>
				select columnID, dbo.fn_regexReplace(columnName,'#local.columnNameRegex#','') as columnName 
				from ##tmpRegistrantUpdateColumns
				where defaultColValue is null
				order by columnID;
			<cfelseif arguments.mode is 'processTemplate'>
				select columnID, quotename(dbo.fn_regexReplace(columnName,'#local.columnNameRegex#','')) as columnName 
				from ##tmpRegistrantUpdateColumns
				where defaultColValue is null
				order by columnID;
			<cfelseif arguments.mode is 'upload'>
				select tmpH.header, tmpCol.columnID, dbo.fn_regexReplace(tmpCol.columnName,'#local.columnNameRegex#','') as columnName, tmpCol.dataTypeCode, 
					tmpCol.isRequired, tmpCol.defaultColValue, tmpCol.skipUpdate, tmpColDetails.mappedColValue, tmpColDetails.mappedColOverrideValue 
				from ##tmpRegistrantUpdateColumns as tmpCol 
				inner join ##tmpRegistrantUpdateColumnHeaders as tmpH on tmpH.headerRowID = tmpCol.headerRowID
				left outer join ##tmpRegistrantUpdateColumnDetails as tmpColDetails on tmpColDetails.columnID = tmpCol.columnID
				order by tmpCol.headerRowID, tmpCol.columnID;
			<cfelse>
				select columnID, columnName 
				from ##tmpRegistrantUpdateColumns
				order by columnID;
			</cfif>

			IF OBJECT_ID('tempdb..##tmpRegistrantUpdateColumnHeaders') IS NOT NULL 
				DROP TABLE ##tmpRegistrantUpdateColumnHeaders;
			IF OBJECT_ID('tempdb..##tmpRegistrantUpdateColumns') IS NOT NULL 
				DROP TABLE ##tmpRegistrantUpdateColumns;
			IF OBJECT_ID('tempdb..##tmpRegistrantUpdateColumnDetails') IS NOT NULL 
				DROP TABLE ##tmpRegistrantUpdateColumnDetails;
		</cfquery>

		<cfreturn local.qryRegistrantUpdateColumns>
	</cffunction>

	<cffunction name="preProcessMassUpdateRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfset local.strEvent = CreateObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eID',0), siteid=arguments.event.getValue('mc_siteinfo.siteid'), languageid=this.languageID)>

		<cfset local.strUpdate = { resourceType='Event', updateTitle='Registrants Update - Column Mapping', processUpdateLink=buildCurrentLink(arguments.event,"processMassUpdateRegistrants"), 
									doAgainLink="#this.link.editEvent#&cID=#local.strEvent.qryEventMeta.calendarID#&eID=#local.strEvent.qryEventMeta.eventID#&tab=registrants", 
									eventID=local.strEvent.qryEventMeta.eventID }>

		<cfset local.strUpdate.updateDesc = "Map the columns of your upload file to the columns for this upload.<br/>We have preselected the upload column that matches the required column name.">

		<cfset local.strFormFields = { resourceType='Event', eventID=local.strEvent.qryEventMeta.eventID }>

		<cfset local.qryUpdateColumns = getRegistrantUpdateFieldDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), eventID=local.strEvent.qryEventMeta.eventID, mode='upload')>
		
		<cfset local.data = createObject("component","model.admin.common.modules.massUpdate.massUpdate").preProcessUpdate(event=arguments.event, strUpdate=local.strUpdate, 
								strFormFields=local.strFormFields, qryUpdateColumns=local.qryUpdateColumns, formFieldName='uploadFileName')>

		<!--- Build breadCrumb Trail --->
		<cfset appendBreadCrumbs(arguments.event,{ link='#local.strUpdate.doAgainLink#', text=len(local.strEvent.qryEventMeta.eventContentTitle) GT 60 ? encodeForHTML('#left(local.strEvent.qryEventMeta.eventContentTitle,60)#...') : encodeForHTML(local.strEvent.qryEventMeta.eventContentTitle) })>
		<cfset appendBreadCrumbs(arguments.event,{ link='', text="Registrants Update - Column Mapping" })>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="processMassUpdateRegistrants" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.strEvent = CreateObject("component","model.events.events").getEvent(eventid=arguments.event.getValue('eventID',0), siteid=arguments.event.getValue('mc_siteinfo.siteid'), languageid=this.languageID)>

		<cfset local.doAgainURL = "#this.link.editEvent#&cID=#local.strEvent.qryEventMeta.calendarID#&eID=#local.strEvent.qryEventMeta.eventID#&tab=registrants">
		<cfset local.qryUpdateColumns = getRegistrantUpdateFieldDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), eventID=local.strEvent.qryEventMeta.eventID, mode='processTemplate')>
		<cfset local.strUpdateResult = this.objAdminEvent.massUpdateRegistrants(event=arguments.event, qryUpdateColumns=local.qryUpdateColumns)>
		
		<cfif not local.strUpdateResult.success and structKeyExists(local.strUpdateResult,"strUpdateDetails") and arrayLen(local.strUpdateResult.strUpdateDetails.arrUpdateColumnDetails)>
			<cfset local.strUpdate = { resourceType='Event', updateTitle='Registrants Update - Column Mapping', updateDesc='', 
										processUpdateLink=buildCurrentLink(arguments.event,"processMassUpdateRegistrants"), doAgainLink=local.doAgainURL }>

			<cfset local.qryUpdateColumns = getRegistrantUpdateFieldDetails(siteID=arguments.event.getValue('mc_siteInfo.siteID'), eventID=local.strEvent.qryEventMeta.eventID, 
												arrUpdateColumnDetails=local.strUpdateResult.strUpdateDetails.arrUpdateColumnDetails, mode='upload')>

			<cfset local.strUpdateResult.previousMappingScreen = createObject("component","model.admin.common.modules.massUpdate.massUpdate").showUpdateProcessResults(strUpdate=local.strUpdate, 
																	strFormFields=local.strUpdateResult.strUpdateDetails.strFormFields, qryUpdateColumns=local.qryUpdateColumns)>
		</cfif>

		<cfset local.data = this.objAdminEvent.showMassUpdateRegistrantResults(strUpdateResult=local.strUpdateResult, doAgainURL=local.doAgainURL, eventID=local.strEvent.qryEventMeta.eventID)>
		
		<!--- Build breadCrumb Trail --->
		<cfset appendBreadCrumbs(arguments.event,{ link='#local.doAgainURL#', text=len(local.strEvent.qryEventMeta.eventContentTitle) GT 60 ? encodeForHTML('#left(local.strEvent.qryEventMeta.eventContentTitle,60)#...') : encodeForHTML(local.strEvent.qryEventMeta.eventContentTitle) })>
		<cfset appendBreadCrumbs(arguments.event,{ link='', text="Mass Update Registrants" })>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportEVRegistrantsFormResponses" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = StructNew();
			local.gridmode = arguments.event.getValue('gridmode','');
			local.siteCode = arguments.event.getValue('mc_siteInfo.siteCode');
			
			local.data = "The export file could not be generated. Contact Admin for assistance.";
			local.strFolder = application.objDocDownload.createHoldingFolder(prefix=local.siteCode);
			arguments.event.setValue("strFolder",local.strFolder);
			local.eventID = arguments.event.getTrimValue('eid');
			local.formID = arguments.event.getTrimValue('formID');
			local.exportType = arguments.event.getTrimValue('exportType');
			if(local.exportType eq "pdf")
				local.reportFileName = "Responses.pdf";
			else
				local.reportFileName = "Responses.csv";
				
			local.qryEnrollments = CreateObject("component","model.admin.events.event").getRegistrantsFromFilters(event=arguments.event, mode="formResponses");

		</cfscript>

		<cfif local.exportType eq "pdf">
			<cfset local.formResponsesXML = xmlParse(local.qryEnrollments.xmlResult)>
			<cfset local.arrForm = XmlSearch(local.formResponsesXML,"/data/form")>

			<cfif arrayLen(local.arrForm)>
				<cfset local.formTitle = local.arrForm[1].XmlAttributes.formtitle>
				<cfset local.formIntro = local.arrForm[1].XmlAttributes.formintro>
				<cfset local.formSectionsArr = local.arrForm[1].XmlChildren>
				<cfset local.summaryResponseCount = arrayLen(XMLSearch(local.formResponsesXML,"/data/responsesummary/qrcs/qrc"))>
				<cfset local.assetsURL = replaceNoCase(application.paths.internalPlatform.url,"*SITECODE*",local.siteCode) & "assets/common/images/formbuilder/">

				<cfsavecontent variable="local.summaryData">
					<cfoutput>
						<html>
						<head>
							<style type="text/css">
								<cfinclude template="/assets/admin/css/pdfstylesheet.css">
							</style>
						</head>
						<body>
							<cfinclude template="/model/admin/seminarweb/dsp_registrantsFormReponseSummaryPDF.cfm">
						</body>
						</html>
					</cfoutput>
				</cfsavecontent>

				<cfdocument filename="#local.strFolder.folderPath#/#local.reportFileName#" pagetype="letter" margintop="0.5" marginbottom="0.5" marginright="0.5" format="PDF" marginleft="0.5" backgroundvisible="Yes" orientation="portrait" unit="in" fontembed="Yes" scale="100">
					<cfoutput>
					<cfdocumentsection>
						#local.summaryData#
					</cfdocumentsection>
					</cfoutput>
				</cfdocument>
			</cfif>
		</cfif>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<script type="text/javascript">downloadEVFormResponses('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="changeRecurringEventTime" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.qryEvent = this.objAdminEvent.getEventTimeDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=arguments.event.getValue('eID',0))>
		<cfset local.isRecurringEvent = val(local.qryEvent.recurringSeriesID) GT 0>

		<cfif NOT local.isRecurringEvent>
			<cfreturn returnAppStruct("Invalid Event.","echo")>
		</cfif>

		<cfset local.qryTimeZones = this.objAdminEvent.getTimeZones()>
		<cfset local.qrySiteAFs = this.objAdminEvent.getAdvanceFormulas(siteID=arguments.event.getValue('mc_siteInfo.siteID'))>
		<cfset local.recurringEventSeriesEndDate = this.objAdminEvent.getEndDateOfRecurringEventSeries(siteID=arguments.event.getValue('mc_siteinfo.siteID'), seriesID=local.qryEvent.recurringSeriesID)>
		<cfset local.formLink = buildCurrentLink(arguments.event,"saveRecurringEventTime") & "&eID=#local.qryEvent.eventID#&mode=stream">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_changeRecurringEventTime.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveRecurringEventTime" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.qryEvent = this.objAdminEvent.getEventTimeDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=arguments.event.getValue('eID',0))>
		<cfset local.isRecurringEvent = val(local.qryEvent.recurringSeriesID) GT 0>

		<cfif NOT local.isRecurringEvent>
			<cfreturn returnAppStruct("Invalid Event.","echo")>
		</cfif>

		<cfset local.arrTimeZoneIDs = listToArray(CreateObject("component","model.system.platform.tsTimeZone").getTZIDList())>
		<cfif arguments.event.getValue('isAllDayEvent',0)>
			<cfset local.startDateTime = ParseDateTime(arguments.event.getValue('eventStartTime'))>
			<cfset local.endDateTime = ParseDateTime("#arguments.event.getValue('eventEndTime')# 23:59:59.997")>
		<cfelse>
			<cfset local.startDateTime = ParseDateTime("#replace(arguments.event.getValue('eventStartTime'),' - ',' ')#")>
			<cfset local.endDateTime = ParseDateTime("#replace(arguments.event.getValue('eventEndTime'),' - ',' ')#")>
			<cfset local.structAllEventTimes = getEventTimeinAllTimeZones(local.startDateTime,local.endDateTime,arguments.event.getValue('eventTimeZoneID',0))>
		</cfif>
		<cfif arguments.event.getValue('lockTimeZone',0)>
			<cfset local.lockTimeZoneID = arguments.event.getValue('eventTimeZoneID',0)>
		<cfelse>
			<cfset local.lockTimeZoneID = 0>
		</cfif>

		<cfset local.updateRecurrenceSettings = arguments.event.getValue('updateRecurrenceSettings',0) EQ 1>
		<cfif local.updateRecurrenceSettings>
			<cfset local.recurrenceAFID = arguments.event.getValue('recurrenceAFID')>
			<cfset local.recurrenceEndsOn = arguments.event.getTrimValue('recurrenceEndsOn')>
		<cfelse>
			<cfset local.recurrenceAFID = local.qryEvent.recurringSeriesAFID>
			<cfset local.recurrenceEndsOn = this.objAdminEvent.getEndDateOfRecurringEventSeries(siteID=arguments.event.getValue('mc_siteinfo.siteID'), seriesID=local.qryEvent.recurringSeriesID)>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryUpdateEvent">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY

				DECLARE @siteID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">, 
					@eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#">,
					@isAllDayEvent bit = <cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('isAllDayEvent',0)#">,
					@lockTimeZoneID int = NULLIF(<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.lockTimeZoneID#">,0),
					@afID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.recurrenceAFID#">,
					@endDate date = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.recurrenceEndsOn#">,
					@recordedByMemberID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberdata.memberID#">,
					@timeID int, @recurringEventsImportResult xml;
				
				<cfif arguments.event.getValue('updateUpcomingRecurringEvents',0) EQ 1>
					IF OBJECT_ID('tempdb..##tmpEventTimes') IS NOT NULL
						DROP TABLE ##tmpEventTimes;
					CREATE TABLE ##tmpEventTimes (eventID int, tzID int, startTime datetime, endTime datetime);

					<cfif arguments.event.getValue('isAllDayEvent',0)>
						<cfloop array="#local.arrTimeZoneIDs#" index="local.tzid">
							INSERT INTO ##tmpEventTimes (eventID, tzID, startTime, endTime)
							VALUES (@eventID,
								<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.tzID#">,
								<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.startDateTime#">,
								<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.endDateTime#">);
						</cfloop>
					<cfelse>
						<cfloop collection="#local.structAllEventTimes#" item="local.key">
							INSERT INTO ##tmpEventTimes (eventID, tzID, startTime, endTime)
							VALUES (@eventID,
								<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.structAllEventTimes[local.key].id#">,
								<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.structAllEventTimes[local.key].starttime#">,
								<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.structAllEventTimes[local.key].endtime#">);
						</cfloop>
					</cfif>

					EXEC dbo.ev_updateRecurringEventsTime @siteID=@siteID, @eventID=@eventID, @isAllDayEvent=@isAllDayEvent,
						@lockTimeZoneID=@lockTimeZoneID, @afID=@afID, @endDate=@endDate, @recordedByMemberID=@recordedByMemberID, 
						@recurringEventsImportResult=@recurringEventsImportResult OUTPUT;

					SELECT 1 AS success, @recurringEventsImportResult AS recurringEventsImportResult, 
						@recurringEventsImportResult.value('count(/import/errors/error)','int') AS errCount;

					IF OBJECT_ID('tempdb..##tmpEventTimes') IS NOT NULL
						DROP TABLE ##tmpEventTimes;
				<cfelse>
					BEGIN TRAN;
						UPDATE dbo.ev_events 
						SET isAllDayEvent = @isAllDayEvent,
							lockTimeZoneID = @lockTimeZoneID,
							defaultTimeID = NULL, 
							lockedTimeID = NULL 
						WHERE eventID = @eventID;

						DELETE FROM dbo.ev_times
						WHERE eventID = @eventID;

						<cfif arguments.event.getValue('isAllDayEvent',0)>
							<cfloop array="#local.arrTimeZoneIDs#" index="local.tzid">
								EXEC dbo.ev_createTime @eventID=@eventID,
									@timeZoneID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.tzID#">,
									@startTime=<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.startDateTime#">,
									@endTime=<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.endDateTime#">,
									@timeID=@timeID OUTPUT;
							</cfloop>
						<cfelse>
							<cfloop collection="#local.structAllEventTimes#" item="local.key">
								EXEC dbo.ev_createTime @eventID=@eventID,
									@timeZoneID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.structAllEventTimes[local.key].id#">,
									@startTime=<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.structAllEventTimes[local.key].starttime#">,
									@endTime=<cfqueryparam cfsqltype="CF_SQL_TIMESTAMP" value="#local.structAllEventTimes[local.key].endtime#">,
									@timeID=@timeID OUTPUT;
							</cfloop>
						</cfif>
					COMMIT TRAN;

					SELECT 1 AS success;
				</cfif>

			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cfset local.tmpEventParams = "">
		<cfif arguments.event.getValue('updateUpcomingRecurringEvents',0) EQ 1 AND val(local.qryUpdateEvent.errCount)>
			<cfset application.mcCacheManager.sessionSetValue(keyname="recurringEvImpErr#arguments.event.getValue('eID')#", value=XMLSearch(local.qryUpdateEvent.recurringEventsImportResult,"/import/errors/error"))>
			<cfset local.tmpEventParams = "&recurringevimperr=1">
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.MCModalUtils.hideModal();
				top.location.href = '#this.link.editEvent#&eID=#arguments.event.getValue('eID')##local.tmpEventParams#';
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deleteEventPrompt" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objEvent = CreateObject("component","model.events.events")>
		<cfset local.eventID = int(val(arguments.event.getValue('eID',0)))>
		<cfset local.qryEvent = this.objAdminEvent.getEventTimeDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=local.eventID)>
		<cfset local.strEvent = local.objEvent.getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=this.languageID)>
		<cfset local.formattedEventDate = local.objEvent.generateEventDateString(mode='eventAdminEditEvent',
			startTime=local.strEvent.qryEventTimes_selected.startTime, endTime=local.strEvent.qryEventTimes_selected.endTime, 
			isAllDayEvent=local.strEvent.qryEventMeta.isAllDayEvent, showTimeZone=false,
			timeZoneAbbr=local.strEvent.qryEventTimes_selected.TIMEZONEABBR)>
		
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qryEvent.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
		<cfif not structKeyExists(local.tmpRights,"DeleteEvent") or local.tmpRights.DeleteEvent is not 1>
			<cfreturn returnAppStruct("You do not have rights to this page.","echo")>
		</cfif>
		
		<cfset local.isRecurringEvent = val(local.qryEvent.recurringSeriesID) GT 0>
		<cfif local.isRecurringEvent>
			<cfset local.qryRecurringEvents = this.objAdminEvent.getRecurringEvents(siteID=arguments.event.getValue('mc_siteinfo.siteID'), seriesID=local.qryEvent.recurringSeriesID, startEventID=local.eventID, count=1)>
		</cfif>
		<cfset local.formLink = buildCurrentLink(arguments.event,"deleteEvent") & "&eID=#local.qryEvent.eventID#&mode=stream">
		
		<cfsavecontent variable="local.data">
			<cfinclude template="frm_deleteEventPrompt.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="deleteEvent" access="private" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.eventID = int(val(arguments.event.getValue('eID',0)))>
		<cfset local.qryEvent = this.objAdminEvent.getEventTimeDetails(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=local.eventID)>
		<cfset local.isRecurringEvent = val(local.qryEvent.recurringSeriesID) GT 0>
		
		<cfset local.tmpRights = application.objSiteResource.buildRightAssignments(siteResourceID=local.qryEvent.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteinfo.siteID'))>
		<cfif not structKeyExists(local.tmpRights,"DeleteEvent") or local.tmpRights.DeleteEvent is not 1>
			<cfreturn returnAppStruct("You do not have rights to this page.","echo")>
		</cfif>

		<cfset this.objAdminEvent.doDeleteEvent(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=local.eventID)>
	
		<!--- delete upcoming recurring events --->
		<cfif arguments.event.getValue('deleteUpcomingRecurringEvents',0) EQ 1 AND local.isRecurringEvent>
			<cfset local.qryUpcomingRecurringEvents = this.objAdminEvent.getRecurringEvents(siteID=arguments.event.getValue('mc_siteinfo.siteID'), seriesID=local.qryEvent.recurringSeriesID, startEventID=local.eventID)>
			<cfloop query="local.qryUpcomingRecurringEvents">
				<cfset this.objAdminEvent.doDeleteEvent(siteID=arguments.event.getValue('mc_siteinfo.siteID'), eventID=local.qryUpcomingRecurringEvents.eventID)>
			</cfloop>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">
				top.reloadLinkEvents();
			</script>
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="viewEventAuditLog" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.auditLogLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=eventsJSON&meth=getEventAuditLogs&mode=stream";

			local.filterKeyName = "eventauditlog";
			local.tmpStr = { "fDescription": '', "fDateFrom": '#dateFormat(dateAdd('d',-14,now()),'m/d/yyyy')#', "fDateTo": '#dateFormat(now(),'m/d/yyyy')#', "fAreaCode": '' };
			local.EventAuditLogFilter = application.objCommon.getToolFiltersData(keyname=local.filterKeyName, defaultFilterData=local.tmpStr);

			if (len(local.EventAuditLogFilter.fDateFrom))
				local.EventAuditLogFilter.fDateFrom = dateFormat(local.EventAuditLogFilter.fDateFrom,'m/d/yyyy');
			if (len(local.EventAuditLogFilter.fDateTo))
				local.EventAuditLogFilter.fDateTo = dateFormat(local.EventAuditLogFilter.fDateTo,'m/d/yyyy');

			// Build breadCrumb Trail
			appendBreadCrumbs(arguments.event,{ link='', text='Event Audit Log' });
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_eventAuditLog.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>