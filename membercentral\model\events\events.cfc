<cfcomponent extends="model.AppLoader" output="no">

	<cfset defaultEvent = "controller">
	<cfset variables.applicationReservedURLParams = "evAction,evSubAction,eid,regaction,regselmode,mid,regcartv2,evrk,ev_rateid,registrantID,rsvpcount,er,calmonth,pagenum,skair,evstp,rk,euc,perr,formid,fbaction">

	<!--- check for bots --->
	<cfif isDefined("session.mcstruct.deviceProfile.is_bot") and session.mcstruct.deviceProfile.is_bot is 1>
		<cfset variables.isBot = 1>
	<cfelse>
		<cfset variables.isBot = 0>
	</cfif>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset variables.instanceSettings = getCalendarSettings(this.appInstanceID)>
		<cfset buildAppRightsStruct(memberid=application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID),siteid=variables.instanceSettings.siteid)>
		
		<!--- validate params. if fail, send back to pg= if that is defined. Dont use mainurl because we could get into a recursive cflocation loop --->
		<cfif NOT validateParameters(event=arguments.event)>
			<cflocation url="/?pg=#arguments.event.getValue('pg','main')#" addtoken="false">
		</cfif>

		<cfset arguments.event.setValue('mainurl','?#getBaseQueryString(false)#')>
		<cfset arguments.event.setValue('mainregurl','#arguments.event.getValue('mainurl')#&eid=#arguments.event.getValue('eid')#&evAction=regV2')>
		<cfset arguments.event.setValue('guestlocatorurl','?event=cms.showResource&resID=#this.siteResourceID#&eid=#arguments.event.getValue('eid')#&evAction=regV2&regaction=guestsearchresults&mode=stream')>
		<cfset arguments.event.setValue('guestALNAurl','?event=cms.showResource&resID=#this.siteResourceID#&eid=#arguments.event.getValue('eid')#&evAction=regV2&regaction=guestnewacct&mode=stream')>
		
		<cfif (application.objCMS.getTemplateSetting(arguments.event,"supportsBootstrap") eq "true") or (isdefined("session.enableMobile") and session.enableMobile)>
			<cfset arguments.event.setValue('viewDirectory', 'responsive')>
		<cfelse>
			<cfset arguments.event.setValue('viewDirectory', 'default')>
		</cfif>

		<!--- get the event --->
		<cfif listFindNoCase('downloadICal,downloadGCal,online,printReg,showDetail,regV2,saveRSVP,showEval',arguments.event.getValue('evAction'))>
			<cfset local.strEvent = getEvent(eventid=arguments.event.getValue('eid'), siteid=variables.instanceSettings.siteid, languageid=session.mcstruct.languageID)>
		</cfif>
		
		<!--- switch depending on evAction --->
		<cfswitch expression="#arguments.event.getValue('evAction')#">
			<!--- save rsvp --->
			<cfcase value="saveRSVP">
				<cfset local.rsvpCount = saveRSVP(event=arguments.event, strEvent=local.strEvent)>
				<cfset application.objCommon.redirect(arguments.event.getValue('mainurl') & '&evAction=showDetail' & '&eid=' & arguments.event.getValue('eid') & '&rsvpCount=' & local.rsvpCount)>
			</cfcase>

			<!--- register --->
			<cfcase value="regV2">
				<cfset arguments.event.setValue('locatorurl','?event=cms.showResource&resID=#this.siteResourceID#&eid=#arguments.event.getValue('eid')#&evAction=regV2&regaction=locator&mode=stream')>
				<cfset arguments.event.setValue('newregacctformurl','?event=cms.showResource&resID=#this.siteResourceID#&eid=#arguments.event.getValue('eid')#&evAction=regV2&regaction=newregacct&mode=stream')>
				<cfset arguments.event.setValue('evregresourceurl','?event=cms.showResource&resID=#this.siteResourceID#&eid=#arguments.event.getValue('eid')#&evAction=regV2&mode=stream')>
				<cfset local.data = CreateObject("component","eventRegV2").doEventReg(event=arguments.event, strEvent=local.strEvent, settings=variables.instanceSettings)>
			</cfcase>
			<cfcase value="addEventRegV2">
				<cfset local.objEventReg = CreateObject("component","eventRegV2")>
				<cfset local.strResponse = local.objEventReg.addEventReg(event=arguments.event, settings=variables.instanceSettings)>
				<cfif local.strResponse.success>
					<cfset local.receiptKeyEnc = local.objEventReg.prepareRegReceipt(event=arguments.event, strAddReg=local.strResponse)>
					<cflocation url="#arguments.event.getValue('mainurl')#&evaction=showReceipt&rk=#local.receiptKeyEnc#" addtoken="false">
				<cfelse>
					<cflocation url="#arguments.event.getValue('mainurl')#&regcartv2&perr=#urlEncodedFormat(local.strResponse.response)#" addtoken="false">
				</cfif>
			</cfcase>
			<cfcase value="showReceipt">
				<cfset local.data = CreateObject("component","eventRegV2").showRegReceipt(event=arguments.event)>
			</cfcase>
			<cfcase value="updateEventRegV2">
				<cfset local.objEventReg = CreateObject("component","eventRegV2")>
				<cfset local.strResponse = local.objEventReg.updateEventReg(event=arguments.event, settings=variables.instanceSettings)>
				<cfif local.strResponse.success>
					<cfset local.receiptKeyEnc = local.objEventReg.prepareEditRegReceipt(event=arguments.event, strEditReg=local.strResponse)>
					<cflocation url="#arguments.event.getValue('mainurl')#&evaction=editRegReceipt&rk=#local.receiptKeyEnc#" addtoken="false">
				<cfelse>
					<cflocation url="#arguments.event.getValue('mainregurl')#&regaction=finalizeEditReg&perr=#urlEncodedFormat(local.strResponse.response)#" addtoken="false">
				</cfif>
			</cfcase>
			<cfcase value="editRegReceipt">
				<cfset local.data = CreateObject("component","eventRegV2").editRegReceipt(event=arguments.event)>
			</cfcase>

			<!--- show event detail --->
			<cfcase value="showDetailUC">
				<cfset local.data = showEventDetailFromEventCode(event=arguments.event)>
			</cfcase>
			<cfcase value="showDetail">
				<cfset local.data = showEventDetail(event=arguments.event,strEvent=local.strEvent)>
			</cfcase>
			<cfcase value="getRegistrantRosterDetails">
				<cfset local.data = getRegistrantRosterDetails(event=arguments.event)>
			</cfcase>
			<cfcase value="printReg">
				<cfset local.data = printReg(event=arguments.event,strEvent=local.strEvent)>
			</cfcase>

			<!--- online meetings --->
			<cfcase value="online">
				<cfset local.data = showOnlineMeeting(event=arguments.event,strEvent=local.strEvent)>
			</cfcase>

			<!--- Evaluations --->
			<cfcase value="showEval">
				<cfset local.data = showEvaluation(event=arguments.event,strEvent=local.strEvent)>
			</cfcase>

			<!--- downloading ICAL --->
			<cfcase value="downloadICal">
				
				<!--- no event found --->
				<cfif local.strEvent.qryEventMeta.recordcount is 0> 
					<cfsavecontent variable="local.data">
						<cfoutput>That event does not exist.</cfoutput>
					</cfsavecontent>
				<!--- inactive event --->
				<cfelseif local.strEvent.qryEventMeta.status neq "A"> 
					<cfsavecontent variable="local.data">
						<cfoutput>This event is currently unavailable or you do not have rights to this event.</cfoutput>
					</cfsavecontent>
				<cfelse>
					<!--- get time of event --->
					<cfif local.strEvent.qryEventMeta.lockTimeZoneID gt 0>
						<cfquery name="local.qryEventTimes_selected" dbtype="query">
							select *
							from [local].strEvent.qryEventTimes
							where timezoneID = #local.strEvent.qryEventMeta.lockTimeZoneID#
						</cfquery>
					<cfelse>
						<cfquery name="local.qryEventTimes_selected" dbtype="query">
							select *
							from [local].strEvent.qryEventTimes
							where timezoneID = #arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#
						</cfquery>
					</cfif>

					<cfset local.eventContent = trim(local.strEvent.qryEventMeta.eventContent)>
					<cfif arguments.event.getValue('includeInfoContent') eq 1 and len(local.strEvent.qryEventMeta.informationContent)>
						<cfif len(local.strEvent.qryEventMeta.informationContentTitle)>
							<cfset local.eventContent = "Confirmation Information:\n\n#local.strEvent.qryEventMeta.informationContentTitle#\n#local.strEvent.qryEventMeta.informationContent#\n\nDescription:\n\n#local.eventContent#">
						<cfelse>
							<cfset local.eventContent = "Confirmation Information:\n\n#local.strEvent.qryEventMeta.informationContent#\n\nDescription:\n\n#local.eventContent#">
						</cfif>
					<cfelse>
						<cfset local.eventContent = "Description:\n\n#local.eventContent#">
					</cfif>

					<!--- clean html and css --->
					<cfset local.evDescSanitizeOptions = {
						"allowedTags": [],
						"allowedAttributes": {}
					}>
					<cfset local.sanitizeEVDescResponse = application.objCommon.sanitizeHTML(dirtyHTML=trim(local.eventContent), sanitizeOptions=local.evDescSanitizeOptions)>
					<cfif local.sanitizeEVDescResponse.success>
						<cfset local.eventContent = trim(REREPLACE(REREPLACE(local.sanitizeEVDescResponse.content,"\t","","ALL"),"(\r\n)+","#chr(13)##chr(10)#","ALL"))>
					</cfif>
					<cfif val(arguments.event.getValue('memRegId',0))>
						<cfset local.eventContent = processCalendarMergeCodes(event=arguments.event, eventContent=local.eventContent, regId=arguments.event.getValue('memRegId'))>
					</cfif>

					<!--- generate ics content --->
					<cfset local.data = CreateObject("component","model.system.common.ICALGenerator").getICALFile(
							arguments.event.getValue('mc_siteInfo.siteName'),
							local.strEvent.qryEventMeta.isAllDayEvent,
							local.qryEventTimes_selected.startTime,
							local.qryEventTimes_selected.endTime,
							local.qryEventTimes_selected.timeZoneID,
							"DEFAULT",
							local.strEvent.qryEventMeta.locationContentTitle,
							local.strEvent.qryEventMeta.locationContent,
							htmleditformat(local.strEvent.qryEventMeta.eventContentTitle),
							left(local.eventContent,1500),
							'DEFAULT',
							'DEFAULT')>

					<!--- save to a file and send to browser --->
					<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
					<cfset local.reportFileName = "#ReReplaceNoCase(local.strEvent.qryEventMeta.eventContentTitle,'[^A-Z0-9]','','ALL')#.ics">
					<cffile file="#local.strFolder.folderPath#/#local.reportFileName#" action="write" output="#local.data#" addnewline="false">
					<cfset application.objDocDownload.doDownloadDocument(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>
				</cfif>
			</cfcase>

			<!--- generate GCAL link --->
			<cfcase value="downloadGCal">
				
				<!--- no event found --->
				<cfif local.strEvent.qryEventMeta.recordcount is 0> 
					<cfsavecontent variable="local.data">
						<cfoutput>That event does not exist.</cfoutput>
					</cfsavecontent>
				<!--- inactive event --->
				<cfelseif local.strEvent.qryEventMeta.status neq "A"> 
					<cfsavecontent variable="local.data">
						<cfoutput>This event is currently unavailable or you do not have rights to this event.</cfoutput>
					</cfsavecontent>
				<cfelse>
					<!--- get time of event --->
					<cfif local.strEvent.qryEventMeta.lockTimeZoneID gt 0>
						<cfquery name="local.qryEventTimes_selected" dbtype="query">
							select startTime, endTime, timezone
							from [local].strEvent.qryEventTimes
							where timezoneID = #local.strEvent.qryEventMeta.lockTimeZoneID#
						</cfquery>
					<cfelse>
						<cfquery name="local.qryEventTimes_selected" dbtype="query">
							select startTime, endTime, timezone
							from [local].strEvent.qryEventTimes
							where timezoneID = #arguments.event.getValue('mc_siteinfo.defaultTimeZoneID')#
						</cfquery>
					</cfif>

					<!--- clean html and css --->
					<cfset local.linebreak = "#chr(13)##chr(10)#">
					<cfset local.gcalDetails = trim(local.strEvent.qryEventMeta.eventContent)>
					<cfif arguments.event.getValue('includeInfoContent') eq 1 and len(local.strEvent.qryEventMeta.informationContent)>
						<cfif len(local.strEvent.qryEventMeta.informationContentTitle)>
							<cfset local.gcalDetails = "Confirmation Information:#local.linebreak##local.strEvent.qryEventMeta.informationContentTitle##local.linebreak##local.strEvent.qryEventMeta.informationContent##local.linebreak#Description:#local.linebreak##local.gcalDetails#">
						<cfelse>
							<cfset local.gcalDetails = "Confirmation Information:#local.linebreak##local.strEvent.qryEventMeta.informationContent##local.linebreak#Description:#local.linebreak##local.gcalDetails#">
						</cfif>
					<cfelse>
						<cfset local.gcalDetails = "Description:#local.linebreak##local.gcalDetails#">
					</cfif>
					<cfif val(arguments.event.getValue('memRegId',0))>
						<cfset local.gcalDetails = processCalendarMergeCodes(event=arguments.event,eventContent=local.gcalDetails,regId=arguments.event.getValue('memRegId'))>
					</cfif>

					<!--- Google descript max length 8000 --->
					<cfif len(local.gcalDetails) GTE 8000>
						<cfset local.gcalDetails = LEFT(local.gcalDetails, 7900) & "...">
					</cfif>
					
					<cfset local.googleCalendarLink = application.objCommon.getGoogleCalendarLink(text=local.strEvent.qryEventMeta.eventContentTitle, details=local.gcalDetails,
														location=local.strEvent.qryEventMeta.locationContentTitle, website=arguments.event.getValue('mc_siteInfo.mainhostname'),
														startDate=local.qryEventTimes_selected.startTime, endDate=local.qryEventTimes_selected.endTime, 
														isAllDayEvent=local.strEvent.qryEventMeta.isAllDayEvent, timeZone=local.qryEventTimes_selected.timeZone)>
					<cflocation url="#local.googleCalendarLink#" addtoken="false">
				</cfif>
			</cfcase>

			<cfcase value="addEv">
				<cfset local.data = addEvent(event=arguments.event,settings=variables.instanceSettings)>
			</cfcase>
			<cfcase value="editEv">
				<cfset local.data = editEvent(event=arguments.event,settings=variables.instanceSettings)>
			</cfcase>
			<cfcase value="insertEv">
				<cfset insertEvent(arguments.event)>
				<cfset local.subAction = arguments.event.getValue('evSubAction','')>
				<cfset local.relocURL = "/?#getBaseQueryString(false)#">
				<cfif len(local.subAction)>
					<cfset local.relocURL = "#local.relocURL#&evAction=#local.subAction#">
				</cfif>
				<cflocation url="#local.relocURL#" addtoken="no">
			</cfcase>
			<cfcase value="saveEv">
				<cfset saveEvent(arguments.event)>
				<cfset local.subAction = arguments.event.getValue('evSubAction','')>
				<cfset local.relocURL = "/?#getBaseQueryString(false)#">
				<cfif len(local.subAction)>
					<cfset local.relocURL = "#local.relocURL#&evAction=#local.subAction#">
				</cfif>
				<cflocation url="#local.relocURL#" addtoken="no">
			</cfcase>
			<cfcase value="delEv">
				<cfset deleteEvent(arguments.event)>
				<cfset local.subAction = arguments.event.getValue('evSubAction','')>
				<cfset local.relocURL = "/?#getBaseQueryString(false)#">
				<cfif len(local.subAction)>
					<cfset local.relocURL = "#local.relocURL#&evAction=#local.subAction#">
				</cfif>
				<cflocation url="#local.relocURL#" addtoken="no">
			</cfcase>
			<cfcase value="eventDocDownload">
				<cfset local.data = eventDocDownload(event=arguments.event)>
			</cfcase>
			<!--- show calendar/cart --->
			<cfdefaultcase>
				<cfif arguments.event.valueExists('regcartv2')>
					<cfset local.data = CreateObject("component","eventRegV2").viewRegCart(event=arguments.event, settings=variables.instanceSettings)>
				<cfelse>
					<cfset local.data = showCalendar(event=arguments.event,settings=variables.instanceSettings)>
				</cfif>
			</cfdefaultcase>
		</cfswitch>

		<!--- record app hit --->
		<cfset application.objPlatformStats.recordAppHit(appname="events",appsection="")>
		<cfif isStruct(local.data) and structKeyExists(local.data,"view")>
			<cfreturn local.data>
		<cfelse>
			<cfreturn returnAppStruct(local.data,"echo")>
		</cfif>
	</cffunction>

	<cffunction name="validateParameters" access="private" output="false" returntype="boolean">
		<cfargument name="Event" type="any">

		<cfscript>
		var local = structNew();

		arguments.event.paramValue('evAction','');
		arguments.event.paramValue('eid',0);
		arguments.event.paramValue('includeInfoContent',0);

		// evAction needs to be a simple value. URLs like   evaction..id= will result in evaction being a struct
		if (NOT isSimpleValue(arguments.event.getTrimValue('evAction')))
			return false;

		// eid needs to be a simple value. URLs like   eid..id= will result in eid being a struct
		if (NOT isSimpleValue(arguments.event.getTrimValue('eid')))
			return false;

		// evAction must be a valid option
		if (len(arguments.event.getTrimValue('evAction')) and NOT listFindNoCase("addEv,calSubscriptions,delEv,downloadICal,downloadGCal,editEv,eventDocDownload,getRegistrantRosterDetails,insertEv,listAll,listMonth,online,printReg,regV2,addEventRegV2,updateEventRegV2,saveEv,saveRSVP,showDetail,showDetailUC,viewMonth,viewOptions,showReceipt,editRegReceipt,showEval", arguments.event.getTrimValue('evAction')))
			return false;

		// eid must be valid positive integer. regex added because eid=1234. would pass int check but would break int functions
		if (NOT isValid("integer",arguments.event.getTrimValue('eid')) or ReFindNoCase("[^0-9]+",arguments.event.getTrimValue('eid')) or arguments.event.getTrimValue('eid') lt 0)
			return false;

		if (NOT isValid("boolean",arguments.event.getTrimValue('includeInfoContent')))
			return false;

		// evAction specific validations
		switch(arguments.event.getTrimValue('evAction')){
			case 'calSubscriptions':
				arguments.event.paramValue('calId',0);
				if (NOT isSimpleValue(arguments.event.getTrimValue('calId')))
					return false;
				arguments.event.setValue('calId',int(val(arguments.event.getTrimValue('calId'))));
				if (NOT isValid("integer",arguments.event.getTrimValue('calId')) or arguments.event.getTrimValue('calId') lte 0)
					return false;
				break;

			case 'showDetailUC':
				arguments.event.paramValue('euc','');
				if (NOT isSimpleValue(arguments.event.getTrimValue('euc')))
					return false;
				arguments.event.setValue('euc',left(arguments.event.getTrimValue('euc'),15));
				if (arguments.event.getTrimValue('euc') EQ '')
					return false;
				break;

			case 'showEval':
				arguments.event.paramValue('formID',0);
				if (NOT isSimpleValue(arguments.event.getTrimValue('formID')))
					return false;
				arguments.event.setValue('formID',int(val(arguments.event.getTrimValue('formID'))));
				if (NOT isValid("integer",arguments.event.getTrimValue('formID')) or arguments.event.getTrimValue('formID') lte 0)
					return false;
				break;
		
			// showing the calendar (listMonth, listAll, viewMonth)
			default:
				// page num
				arguments.event.paramValue('pageNum',1);
				if (NOT isSimpleValue(arguments.event.getTrimValue('pageNum')))
					return false;
				arguments.event.setValue('pageNum',int(val(arguments.event.getTrimValue('pageNum'))));
				if (NOT isValid("integer",arguments.event.getTrimValue('pageNum')) or arguments.event.getTrimValue('pageNum') lt 0)
					return false;
				if (arguments.event.getTrimValue('pageNum') eq 0)
					arguments.event.setValue('pageNum',1);

				// include categories
				if (NOT isSimpleValue(arguments.event.getTrimValue('evCat','')))
					return false;
				if (len(arguments.event.getTrimValue('evCat',''))) {
					for (local.i=1; local.i<=ListLen(arguments.event.getTrimValue('evCat')); local.i++) {
						if (NOT isNumeric(ListGetAt(arguments.event.getTrimValue('evCat'),local.i)))
							return false;
					}
				}

				// exclude categories
				if (NOT isSimpleValue(arguments.event.getTrimValue('evXCat','')))
					return false;
				if (len(arguments.event.getTrimValue('evXCat',''))) {
					for (local.i=1; local.i<=ListLen(arguments.event.getTrimValue('evXCat')); local.i++) {
						if (NOT isNumeric(ListGetAt(arguments.event.getTrimValue('evXCat'),local.i)))
							return false;
					}
				}

				// calmonth YYYYMM for listmonth, viewmonth
				arguments.event.paramValue('calmonth',dateformat(now(),"YYYYMM"));
				if (NOT isSimpleValue(arguments.event.getTrimValue('calmonth')))
					return false;
				arguments.event.setValue('calmonth',int(val(arguments.event.getTrimValue('calmonth'))));
				if (NOT isValid("integer",arguments.event.getTrimValue('calmonth')) or arguments.event.getTrimValue('calmonth') lt 175301 or arguments.event.getTrimValue('calmonth') gt 299912)
					return false;
				local.calmonth_month = right(arguments.event.getTrimValue('calmonth'),2);
				if (local.calmonth_month eq 0 or local.calmonth_month gt 12)
					return false;
				break;
		}
		
		return true;
		</cfscript>
	</cffunction>

	<cffunction name="getEvent" access="public" output="false" returntype="struct">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="languageID" type="numeric" required="yes">
		
		<cfset var local = structNew()>

		<cfstoredproc procedure="ev_getEvent" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.siteid#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.eventID#">
			<cfprocparam type="In" cfsqltype="cf_sql_integer" value="#arguments.languageid#">
			<cfprocresult name="local.qryEventMeta" resultset="1">
			<cfprocresult name="local.qryEventSponsors" resultset="2">
			<cfprocresult name="local.qryEventCategories" resultset="3">
			<cfprocresult name="local.qryEventTimes" resultset="4">
			<cfprocresult name="local.qryEventRegMeta" resultset="5">
			<cfprocresult name="local.qryParentEvent" resultset="6">
		</cfstoredproc>
		
		<!--- get time of event --->
		<cfif local.qryEventMeta.lockTimeZoneID gt 0>
			<cfquery name="local.qryEventTimes_selected" dbtype="query">
				select *
				from [local].qryEventTimes
				where timezoneID = #local.qryEventMeta.lockTimeZoneID#
			</cfquery>
		<cfelse>
			<cfquery name="local.qrySiteCode" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
				
				select sitecode
				from dbo.sites
				where siteID = <cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">;

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>
			<cfquery name="local.qryEventTimes_selected" dbtype="query">
				select *
				from [local].qryEventTimes
				where timezoneID = #application.objSiteInfo.getSiteInfo(local.qrySiteCode.sitecode).defaultTimeZoneID#
			</cfquery>
		</cfif>

		<!--- subevent support --->
		<cfquery name="local.qrySubEvents" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;
			
			select childEventID
			from (
				select e.eventID as masterEventID, subEvent.eventID as childEventID
				FROM dbo.ev_events as e
				inner join dbo.ev_subEvents as subEvent on subEvent.parentEventID = e.eventID 	
					and e.eventID = <cfqueryparam value="#arguments.eventID#" cfsqltype="CF_SQL_INTEGER">
			) tmp
			inner join ev_events e2 on e2.eventID = tmp.childEventID 
				and e2.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.hasSubEvents = false>
		<cfif local.qrySubEvents.recordcount>
			<cfset local.hasSubEvents = true>
		</cfif>

		<cfset local.isSubEvent = false />
		<cfif local.qryParentEvent.recordCount>
			<cfset local.isSubEvent = true />
		</cfif>
		
		<cfstoredproc procedure="ev_getMerchantProfilesByRegistrationID" datasource="#application.dsn.membercentral.dsn#">
			<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.qryEventRegMeta.registrationid)#">
			<cfprocresult name="local.qryCurrentProfiles">
		</cfstoredproc>
		<cfset local.merchantProfileCheck = 0>
		<cfif local.qryCurrentProfiles.recordCount>
			<cfset local.merchantProfileCheck = 1>	
		</cfif>

		<cfquery name="local.qryEventPerms" datasource="#application.dsn.membercentral.dsn#">
			select  dbo.fn_cache_perms_getResourceRightsXML(<cfqueryparam value="#local.qryEventMeta.siteResourceID#" cfsqltype="CF_SQL_INTEGER">,
															<cfqueryparam value="#session.cfcuser.memberdata.memberid#" cfsqltype="CF_SQL_INTEGER">,
															<cfqueryparam value="#arguments.siteid#" cfsqltype="CF_SQL_INTEGER">) as rightsXML
		</cfquery>

		<cfreturn local>
	</cffunction>
	
	<cffunction name="generateEventDateString" access="public" output="false" returntype="string">
		<cfargument name="mode" type="string" required="yes">
		<cfargument name="startTime" type="date" required="yes">
		<cfargument name="endTime" type="date" required="yes">
		<cfargument name="isAllDayEvent" type="boolean" required="yes">
		<cfargument name="showTimeZone" type="boolean" required="yes">
		<cfargument name="timeZoneAbbr" type="string" required="yes">

		<cfset var eventtime = "">

		<cfsavecontent variable="eventtime">
			<cfswitch expression="#arguments.mode#">
				<cfcase value="eventDetail">
					<cfif arguments.isAllDayEvent>
						<cfoutput>#DateFormat(arguments.startTime, "ddd, mmmm d, yyyy")#</cfoutput>
						<cfif DateCompare(arguments.startTime,arguments.endTime,'d') is not 0>
							<cfoutput> to<br/>#DateFormat(arguments.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
						</cfif>
					<cfelse>
						<cfoutput>#DateFormat(arguments.startTime, "ddd, mmmm d, yyyy")#<br/></cfoutput>
						<!--- if event start and event end are the same day and time --->			
						<cfif DateCompare(arguments.startTime,arguments.endTime,'n') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>

						<!--- if event start and event end are the same day but different times --->
						<cfelseif DateCompare(arguments.startTime,arguments.endTime,'d') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #timeformat(arguments.endTime,"h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						
						<!--- if event start and event end are different days --->
						<cfelse>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# to</cfoutput>
							<cfoutput><br/>#DateFormat(arguments.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
							<cfoutput><br/>#TimeFormat(arguments.endTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						</cfif>
					</cfif>
				</cfcase>
				<cfcase value="eventEmailRegistrants">
					<cfif arguments.isAllDayEvent>
						<cfoutput>#DateFormat(arguments.startTime, "ddd, mmmm d, yyyy")#</cfoutput>
						<cfif DateCompare(arguments.startTime,arguments.endTime,'d') is not 0>
							<cfoutput> to #DateFormat(arguments.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
						</cfif>
					<cfelse>
						<cfoutput>#DateFormat(arguments.startTime, "ddd, mmmm d, yyyy")# </cfoutput>
						<!--- if event start and event end are the same day and time --->			
						<cfif DateCompare(arguments.startTime,arguments.endTime,'n') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>

						<!--- if event start and event end are the same day but different times --->
						<cfelseif DateCompare(arguments.startTime,arguments.endTime,'d') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #timeformat(arguments.endTime,"h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						
						<!--- if event start and event end are different days --->
						<cfelse>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# to </cfoutput>
							<cfoutput>#DateFormat(arguments.endTime, "ddd, mmmm d, yyyy")# </cfoutput>
							<cfoutput>#TimeFormat(arguments.endTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						</cfif>
					</cfif>
				</cfcase>
				<cfcase value="eventConfirmation">
					<cfif arguments.isAllDayEvent>
						<cfif DateCompare(arguments.startTime,arguments.endTime,"m")>
							<cfoutput>#DateFormat(arguments.startTime, "mmmm d, yyyy")# </cfoutput>
							<cfoutput>- #DateFormat(arguments.endTime, "mmmm d, yyyy")#</cfoutput>
						<cfelse>
							<cfoutput>#DateFormat(arguments.startTime, "mmmm d")#</cfoutput>
							<cfif DateCompare(arguments.endTime,arguments.startTime,"d")>
								<cfoutput>-#DateFormat(arguments.endTime, "d")#</cfoutput>
							</cfif>
							<cfoutput>, #DateFormat(arguments.startTime, "yyyy")#</cfoutput>
						</cfif>
					<cfelse>
						<cfoutput>#DateFormat(arguments.startTime, "mmmm d, yyyy")# </cfoutput>
						<!--- if event start and event end are the same day and time --->			
						<cfif DateCompare(arguments.startTime,arguments.endTime,'n') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>

						<!--- if event start and event end are the same day but different times --->
						<cfelseif DateCompare(arguments.startTime,arguments.endTime,'d') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #timeformat(arguments.endTime,"h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						
						<!--- if event start and event end are different days --->
						<cfelse>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #DateFormat(arguments.endTime, "mmmm d, yyyy")# </cfoutput>
							<cfoutput>#TimeFormat(arguments.endTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						</cfif>
					</cfif>
				</cfcase>
				<cfcase value="calendarListView">
					<cfif arguments.isAllDayEvent>
						<cfoutput>#DateFormat(arguments.startTime, "ddd m/d/yyyy")#</cfoutput>
						<cfif DateCompare(arguments.startTime,arguments.endTime,'d') is not 0>
							<cfoutput> - #DateFormat(arguments.endTime, "ddd m/d/yyyy")#</cfoutput>
						</cfif>
					<cfelse>
						<cfoutput>#DateFormat(arguments.startTime, "ddd m/d/yyyy")# </cfoutput>

						<!--- if event start and event end are the same day and time --->			
						<cfif DateCompare(arguments.startTime,arguments.endTime,'n') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>

						<!--- if event start and event end are the same day but different times --->
						<cfelseif DateCompare(arguments.startTime,arguments.endTime,'d') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #timeformat(arguments.endTime,"h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						
						<!--- if event start and event end are different days --->
						<cfelse>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #DateFormat(arguments.endTime, "ddd m/d/yyyy")#</cfoutput>
							<cfoutput><br/>#TimeFormat(arguments.endTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						</cfif>
					</cfif>
				</cfcase>
				<cfcase value="calendarListViewResponsive">
					<cfif arguments.isAllDayEvent>
						<cfif DateCompare(arguments.startTime,arguments.endTime,'d') is not 0>
							<cfoutput>#dateformat(arguments.startTime,"ddd m/d/yyyy")# - #dateformat(arguments.endTime,"ddd m/d/yyyy")#</cfoutput>
						</cfif>
					<cfelse>
						<cfoutput>#DateFormat(arguments.startTime, "ddd m/d/yyyy")# </cfoutput>

						<!--- if event start and event end are the same day and time --->			
						<cfif DateCompare(arguments.startTime,arguments.endTime,'n') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>

						<!--- if event start and event end are the same day but different times --->
						<cfelseif DateCompare(arguments.startTime,arguments.endTime,'d') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #timeformat(arguments.endTime,"h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						
						<!--- if event start and event end are different days --->
						<cfelse>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #DateFormat(arguments.endTime, "ddd m/d/yyyy")# </cfoutput>
							<cfoutput>#TimeFormat(arguments.endTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						</cfif>
					</cfif>
				</cfcase>
				<cfcase value="upcomingEventsWidget">
					<cfif arguments.isAllDayEvent>
						<cfif DateCompare(arguments.startTime,arguments.endTime,'m') is not 0>
							<cfoutput>#DateFormat(arguments.startTime, "mmmm d, yyyy")#</cfoutput>
							<cfoutput><br/>- #DateFormat(arguments.endTime, "mmmm d, yyyy")#</cfoutput>
						<cfelse>
							<cfoutput>#DateFormat(arguments.startTime, "mmmm d")#</cfoutput>
							<cfif DateCompare(arguments.startTime,arguments.endTime,'d') is not 0>
								<cfoutput>-#DateFormat(arguments.endTime, "d")#</cfoutput>
							</cfif>
							<cfoutput>, #DateFormat(arguments.startTime, "yyyy")#</cfoutput>
						</cfif>
					<cfelse>
						<cfoutput>#DateFormat(arguments.startTime, "mmmm d, yyyy")# </cfoutput>
						<!--- if event start and event end are the same day and time --->			
						<cfif DateCompare(arguments.startTime,arguments.endTime,'n') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>

						<!--- if event start and event end are the same day but different times --->
						<cfelseif DateCompare(arguments.startTime,arguments.endTime,'d') is 0>
							<cfoutput><br/>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #timeformat(arguments.endTime,"h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						
						<!--- if event start and event end are different days --->
						<cfelse>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# to</cfoutput>
							<cfoutput><br/>- #DateFormat(arguments.endTime, "mmmm d, yyyy")# </cfoutput>
							<cfoutput>#TimeFormat(arguments.endTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						</cfif>
					</cfif>
				</cfcase>
				<cfcase value="eventAdminEditEvent,eventAdminRemoveSynd">
					<cfif arguments.isAllDayEvent>
						<cfoutput>#DateFormat(arguments.startTime, "ddd, mmmm d, yyyy")#</cfoutput>
						<cfif DateCompare(arguments.startTime,arguments.endTime,'d') is not 0>
							<cfoutput> - #DateFormat(arguments.endTime, "ddd, mmmm d, yyyy")#</cfoutput>
						</cfif>
					<cfelse>
						<cfoutput>#DateFormat(arguments.startTime, "ddd, mmmm d, yyyy")# </cfoutput>
						<!--- if event start and event end are the same day and time --->			
						<cfif DateCompare(arguments.startTime,arguments.endTime,'n') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")#</cfoutput>

						<!--- if event start and event end are the same day but different times --->
						<cfelseif DateCompare(arguments.startTime,arguments.endTime,'d') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #timeformat(arguments.endTime,"h:mm TT")#</cfoutput>
						
						<!--- if event start and event end are different days --->
						<cfelse>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# - </cfoutput>
							<cfoutput>#DateFormat(arguments.endTime, "ddd, mmmm d, yyyy")# #TimeFormat(arguments.endTime, "h:mm TT")#</cfoutput>
						</cfif>
					</cfif>
				</cfcase>
				<cfcase value="eventCertificate">
					<cfif arguments.isAllDayEvent>
						<cfif DateCompare(arguments.startTime,arguments.endTime,"m")>
							<cfoutput>#DateFormat(arguments.startTime, "mmmm d, yyyy")# </cfoutput>
							<cfoutput>- #DateFormat(arguments.endTime, "mmmm d, yyyy")#</cfoutput>
						<cfelse>
							<cfoutput>#DateFormat(arguments.startTime, "mmmm d")#</cfoutput>
							<cfif DateCompare(arguments.endTime,arguments.startTime,"d")>
								<cfoutput>-#DateFormat(arguments.endTime, "d")#</cfoutput>
							</cfif>
							<cfoutput>, #DateFormat(arguments.startTime, "yyyy")#</cfoutput>
						</cfif>
					<cfelse>
						<cfoutput>#DateFormat(arguments.startTime, "mmmm d, yyyy")# </cfoutput>
						<!--- if event start and event end are the same day and time --->			
						<cfif DateCompare(arguments.startTime,arguments.endTime,'n') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>

						<!--- if event start and event end are the same day but different times --->
						<cfelseif DateCompare(arguments.startTime,arguments.endTime,'d') is 0>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #timeformat(arguments.endTime,"h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						
						<!--- if event start and event end are different days --->
						<cfelse>
							<cfoutput>#TimeFormat(arguments.startTime, "h:mm TT")# </cfoutput>
							<cfoutput>- #DateFormat(arguments.endTime, "mmmm d, yyyy")# </cfoutput>
							<cfoutput>#TimeFormat(arguments.endTime, "h:mm TT")#<cfif arguments.showTimeZone> #arguments.timeZoneAbbr#</cfif></cfoutput>
						</cfif>
					</cfif>
				</cfcase>
			</cfswitch>
		</cfsavecontent>

		<cfreturn trim(eventtime)>
	</cffunction>

	<cffunction name="showEventDetailFromEventCode" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryEvent">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT dbo.fn_ev_getEventIDFromEventCode(
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">, 
				<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#variables.instanceSettings.calendarID#">, 
				<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('euc')#">
			) AS eventID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- no event found --->
		<cfif NOT val(local.qryEvent.eventID)> 
			<cfsavecontent variable="local.datastruct">
				<cfoutput>That event does not exist.</cfoutput>
			</cfsavecontent>
			<cfheader statuscode="404" statustext="Event not found">
			<cfset local.viewtouse = 'echo'>
		<!--- good event --->
		<cfelse>
			<cflocation url="#arguments.event.getValue('mainurl')#&eid=#local.qryEvent.eventID#&evAction=showDetail" addtoken="false">
		</cfif>

		<cfreturn returnAppStruct(local.dataStruct,local.viewToUse)>
	</cffunction>

	<cffunction name="showEventDetail" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="strEvent" type="struct" required="yes">

		<cfset var local = structNew()>
		<!--- no event found --->
		<cfif arguments.strEvent.qryEventMeta.recordcount is 0> 
			<cfsavecontent variable="local.datastruct">
				<cfoutput>That event does not exist.</cfoutput>
			</cfsavecontent>
			<cfheader statuscode="404" statustext="File not found">
			<cfset local.viewtouse = 'echo'>
		<!--- inactive event --->
		<cfelseif arguments.strEvent.qryEventMeta.status neq "A"> 
			<cfsavecontent variable="local.datastruct">
				<cfoutput>This event is currently unavailable or you do not have rights to this event.</cfoutput>
			</cfsavecontent>
			<cfheader statuscode="404" statustext="File not found">
			<cfset local.viewtouse = 'echo'>
		<!--- good event --->
		<cfelse>
			<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event")>
			<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
			<cfset local.languageID	= arguments.event.getValue('mc_siteInfo.defaultLanguageID')>

			<cfset local.currentPageTitle = arguments.event.getValue('mc_pageDefinition.pagetitle')>
			<cfset arguments.event.getCollection()['mc_pageDefinition']['pagetitle'] = '#arguments.strEvent.qryEventMeta.eventContentTitle# - #local.currentPageTitle#'>
			<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>
			<cfset local.viewToUse = 'events/#local.viewDirectory#/eventDetail'>

			<!--- link to edit event page from toolbar --->
			<cfif application.objUser.isSiteAdmin(cfcuser=session.cfcuser)>
				<cfset local.appPageCPLink = { linkText="Edit Event", link="/?pg=admin&jumpToTool=EventAdmin%7ClistEvents%7CeditEvent&eid=#arguments.strEvent.qryEventMeta.eventID#&bc=e" }>
				<cfset arguments.event.setValue('mc_appPageCPLink', local.appPageCPLink) />
			</cfif>

			<cfset local.baseQueryString = getBaseQueryString(false)>
			<cfif structKeyExists(variables.appRightsStruct,'evEditOwn') AND variables.appRightsStruct['evEditOwn'] is 1>
				<cfset local.canEditEv = 1>
			<cfelse>
				<cfset local.canEditEv = 0>
			</cfif>
			<cfif structKeyExists(variables.appRightsStruct,'evDeleteOwn') AND variables.appRightsStruct['evDeleteOwn'] is 1>
				<cfset local.canDelEv = 1>
			<cfelse>
				<cfset local.canDelEv = 0>
			</cfif>
			<cfset local.subAction = arguments.event.getValue('evSubAction','')>
			
			<!--- show currency types --->
			<cfset local.displayedCurrencyType = "">
			<cfif application.objSiteInfo.getSiteInfo(variables.instancesettings.sitecode).showCurrencyType is 1>
				<cfset local.displayedCurrencyType = application.objSiteInfo.getSiteInfo(variables.instancesettings.sitecode).defaultCurrencyType>
			</cfif>

			<!--- supports memberkey --->
			<cfset local.memberIDForReg = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID)>

			<!--- get event rights --->
			<cfset local.strEventRights = buildRightAssignments(siteResourceID=arguments.strEvent.qryEventMeta.siteResourceID, memberID=local.memberIDForReg, siteID=variables.instanceSettings.siteID)>

			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRegistrant">
				EXEC dbo.ev_getRegistrantInfo @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEvent.qryEventMeta.eventID#">,
					@memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberIDForReg#">;
			</cfquery>
			<cfset local.XUserName = application.objSiteInfo.getSiteInfo(sitecode=variables.instancesettings.sitecode).XUserName>

			<!--- registration link --->
			<cfset local.allowRegister = 0>
			<cfset local.showRates = 0>
			<cfset local.regMessage = "">
			<cfset local.regLink = "">
			<cfset local.regBtn = "">
			<cfset local.regAlready = "">
			<cfif len(arguments.strEvent.qryEventMeta.altRegistrationURL)>
				<cfset local.allowRegister = 1>
				<cfset local.regLink = arguments.strEvent.qryEventMeta.altRegistrationURL>
				<cfif local.qryRegistrant.recordCount gt 0>
					<cfset local.regAlready = "You are registered for this event.">
					<cfset local.regBtn = "Register A Colleague">
				<cfelse>
					<cfset local.regBtn = "Register Now">
				</cfif>
			<cfelseif arguments.strEvent.qryEventRegMeta.recordcount is 0>
			<cfelseif arguments.strEvent.qryEventRegMeta.status neq "A">
				<cfset local.regMessage = "This event is not accepting online registrations at this time.">
			<cfelseif now() lt arguments.strEvent.qryEventRegMeta.startdate>
				<cfset local.regMessage = "Online registration for this event opens in #application.objCommon.diffDateLong(now(),arguments.strEvent.qryEventRegMeta.startdate)#.">
			<cfelseif now() gt arguments.strEvent.qryEventRegMeta.enddate>
				<cfif len(arguments.strEvent.qryEventRegMeta.expireContent)>
					<cfset local.regMessage = arguments.strEvent.qryEventRegMeta.expireContent>
				<cfelse>
					<cfset local.regMessage = "Online registration for this event is closed.">
				</cfif>
			<cfelseif arguments.strEvent.qryEventRegMeta.regCapReached>
				<cfset local.regMessage = arguments.strEvent.qryEventRegMeta.registrantCapContent>
			<cfelseif NOT arguments.strEvent.merchantProfileCheck AND arguments.strEvent.qryEventRegMeta.registrationTypeID EQ 1>
				<cfset local.regMessage = "Event registration is not available at this time.">
			<cfelseif arguments.strEvent.qryEventRegMeta.registrationType eq "RSVP">
				<cfset local.allowRegister = 1>
				<cfset local.regLink = "##RSVP">
				<cfset local.regBtn = "RSVP Now">
			<cfelse>
				<cfset local.allowRegister = 1>
				<cfset local.regLink = arguments.event.getValue('mainurl') & '&eid=' & arguments.strEvent.qryEventMeta.eventID & '&evAction=regV2'>
				<cfset local.showRates = 1>
				<cfif local.qryRegistrant.recordCount gt 0>
					<cfset local.regAlready = "You are registered for this event.">
					<cfset local.regBtn = "Register A Colleague">
				<cfelse>
					<cfset local.regBtn = "Register Now">
				</cfif>
			</cfif>
			
			<!--- rsvp prefixes --->
			<cfif local.allowRegister is 1 and arguments.strEvent.qryEventRegMeta.registrationType eq "RSVP">
				<cfset local.qryOrgSettings = CreateObject("component","model.admin.organization.organization").getSettings(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>
				<cfset local.qryOrgPrefixes = application.objOrgInfo.getOrgPrefixTypes(orgID=arguments.event.getValue('mc_siteinfo.orgid'))>				
			</cfif>

			<!--- online meeting support --->
			<cfset local.isOnlineMeeting = val(arguments.strEvent.qryEventRegMeta.isOnlineMeeting)>
			<cfset local.allowEnterOnlineMeeting = 0>
			<cfset local.onlineMeetingLink = "">
			<cfset local.disallowEnterOnlineMeetingMessage = "">
			<cfset local.showLoginOnlineMeeting = 0>
			<cfif local.isOnlineMeeting and now() gte arguments.strEvent.qryEventRegMeta.onlineEnterStartTime and now() lt arguments.strEvent.qryEventRegMeta.onlineEnterEndTime>
				<cfset local.allowEnterOnlineMeeting = 1>
				<cfif len(arguments.strEvent.qryEventRegMeta.onlineEmbedOverrideLink)>
					<cfset local.onlineMeetingLink = arguments.strEvent.qryEventRegMeta.onlineEmbedOverrideLink>
				<cfelse>
					<cfset local.onlineMeetingLink = arguments.event.getValue('mainurl') & '&eid=' & arguments.strEvent.qryEventMeta.eventID & '&evAction=online&mode=full&mid=' & local.memberIDForReg>
				</cfif>
			<cfelseif local.isOnlineMeeting and now() lt arguments.strEvent.qryEventRegMeta.onlineEnterStartTime>
				<cfset local.onlinestarttime = arguments.strEvent.qryEventRegMeta.onlineEnterStartTime>
				<cfset local.objTZ = CreateObject("component","model.system.platform.tsTimeZone")>
				<cfset local.onlineTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'))>
				<cfif local.onlineTimeZone neq "US/Central">
					<cfset local.onlinestarttime = local.objTZ.convertTimeZone(dateToConvert=local.onlinestarttime,fromTimeZone='US/Central',toTimeZone=local.onlineTimeZone)>
				</cfif>	
				<cfset local.disallowEnterOnlineMeetingMessage = "You'll be able to enter the program<br/>on #DateTimeFormat(local.onlinestarttime, 'mmmm d', local.onlineTimeZone)# at #DateTimeFormat(local.onlinestarttime, 'h:nn tt', local.onlineTimeZone)#.">
			<cfelseif local.isOnlineMeeting and now() gte arguments.strEvent.qryEventRegMeta.onlineEnterEndTime>
				<cfset local.disallowEnterOnlineMeetingMessage = "This program has already closed.">
			</cfif>
			<cfif NOT local.memberIDForReg and local.isOnlineMeeting and now() lt arguments.strEvent.qryEventRegMeta.onlineEnterEndTime>
				<cfset local.showLoginOnlineMeeting = 1>
			</cfif>
			<cfset local.showTimeZone = true>
			<cfif application.objSiteInfo.getSiteInfo(session.mcStruct.sitecode).defaultTimeZoneID eq arguments.strEvent.qryEventTimes_selected.timeZoneID and arguments.strEvent.qryEventMeta.alwaysShowEventTimezone eq 0>
				<cfset local.showTimeZone = false>
			</cfif>

			<cfset local.eventtime = generateEventDateString(mode='eventDetail', startTime=arguments.strEvent.qryEventTimes_selected.startTime, 
				endTime=arguments.strEvent.qryEventTimes_selected.endTime, isAllDayEvent=arguments.strEvent.qryEventMeta.isAllDayEvent,
				showTimeZone=local.showTimeZone, timeZoneAbbr=arguments.strEvent.qryEventTimes_selected.timeZoneAbbr)>

			<cfset local.selectedTimeZoneCode = local.objTSTZ.getTZCodeFromTZID(arguments.strEvent.qryEventTimes_selected.timezoneid)>
			<cfset local.utcStart = local.objTSTZ.convertTimeZone(arguments.strEvent.qryEventTimes_selected.startTime,local.selectedTimeZoneCode,"UTC")>
			<cfset local.utcEnd = local.objTSTZ.convertTimeZone(arguments.strEvent.qryEventTimes_selected.endTime,local.selectedTimeZoneCode,"UTC")>
			<cfset local.centralEnd = local.objTSTZ.convertTimeZone(arguments.strEvent.qryEventTimes_selected.endTime,local.selectedTimeZoneCode,"US/Central")>


			<cfif arguments.strEvent.qryEventRegMeta.recordcount gt 0>
				<cfset local.qryEventRegRates = CreateObject("component","eventRegV2").getRegRates(regid=arguments.strEvent.qryEventRegMeta.registrationid, mid=local.memberIDForReg, showall=true)>
				<cfif local.qryEventRegRates.recordcount is 0>
					<cfset local.showRates = 0>
				</cfif>
			</cfif>

			<!--- event documents --->
			<cfset local.qryDocuments = local.objAdminEvent.getEventDocuments(eventID=arguments.strEvent.qryEventMeta.eventID, memberID=local.memberIDForReg)>			
			<cfset local.eventStruct = structNew()>
			<cfset local.eventStruct.qryEventDocs = QueryFilter(local.qryDocuments, function(thisRow) { return arguments.thisRow.isChild EQ 0; })>
			<cfset local.eventStruct.qrySubEventDocs = QueryFilter(local.qryDocuments, function(thisRow) { return arguments.thisRow.isChild EQ 1; })>
			<cfset local.eventStruct.hasDoc = local.qryDocuments.recordCount GT 0>
			
			<cfset local.dataStruct = StructNew()>
			<cfset local.dataStruct.actionStruct = StructNew()>
			<cfset local.dataStruct.actionStruct.eventStruct = local.eventStruct>
			<cfset local.dataStruct.actionStruct.orgMemberID = local.memberIDForReg>
			<cfset local.dataStruct.instanceSettings = variables.instanceSettings>
			<cfset local.dataStruct.baseQueryString = getBaseQueryString(false)>
			<cfif isDefined("local.qryOrgSettings")>
				<cfset local.dataStruct.orgSettings = local.qryOrgSettings>
				<cfset local.dataStruct.orgPrefixTypes = local.qryOrgPrefixes>
			</cfif>
			<cfset local.dataStruct.actionStruct.isbot = variables.isbot>
			<cfset local.dataStruct.actionStruct.registrationType = arguments.strEvent.qryEventRegMeta.registrationType>
			<cfset local.dataStruct.actionStruct.enableRealTimeRoster = val(arguments.strEvent.qryEventRegMeta.enableRealTimeRoster)>
			<cfif local.dataStruct.actionStruct.enableRealTimeRoster>
				<cfset local.dataStruct.actionStruct.jsonURL = "/?event=proxy.ts_json&c=EV&m=getRegListforRoster">
			</cfif>
			<cfset local.dataStruct.actionStruct.enteredByMemberID = arguments.strEvent.qryEventMeta.enteredByMemberID>
			<cfset local.dataStruct.actionStruct.calendarApplicationInstanceID = arguments.strEvent.qryEventMeta.calendarApplicationInstanceID>
			<cfset local.dataStruct.actionStruct.eventID = arguments.strEvent.qryEventMeta.eventID>
			<cfset local.dataStruct.actionStruct.eventSubTitle = arguments.strEvent.qryEventMeta.eventSubTitle>
			<cfset local.dataStruct.actionStruct.eventContentTitle = arguments.strEvent.qryEventMeta.eventContentTitle>
			<cfset local.dataStruct.actionStruct.eventContent = arguments.strEvent.qryEventMeta.eventContent>
			<cfset local.dataStruct.actionStruct.locationContentTitle = arguments.strEvent.qryEventMeta.locationContentTitle>
			<cfset local.dataStruct.actionStruct.locationContent = arguments.strEvent.qryEventMeta.locationContent>
			<cfset local.dataStruct.actionStruct.travelContentTitle = arguments.strEvent.qryEventMeta.travelContentTitle>
			<cfset local.dataStruct.actionStruct.travelContent = arguments.strEvent.qryEventMeta.travelContent>
			<cfset local.dataStruct.actionStruct.cancelContentTitle = arguments.strEvent.qryEventMeta.cancelContentTitle>
			<cfset local.dataStruct.actionStruct.cancelContent = arguments.strEvent.qryEventMeta.cancelContent>
			<cfset local.dataStruct.actionStruct.altRegistrationURL = arguments.strEvent.qryEventMeta.altRegistrationURL>
			<cfset local.dataStruct.actionStruct.contactContentTitle = arguments.strEvent.qryEventMeta.contactContentTitle>
			<cfset local.dataStruct.actionStruct.contactContent = arguments.strEvent.qryEventMeta.contactContent>
			<cfset local.dataStruct.actionStruct.endTime = arguments.strEvent.qryEventTimes_selected.endTime>
			<cfset local.dataStruct.actionStruct.qryEventCategories = arguments.strEvent.qryEventCategories>
			<cfset local.dataStruct.actionStruct.rsvpCountExists = arguments.event.valueExists('rsvpCount')>
			<cfif arguments.event.valueExists('rsvpCount')>
				<cfset local.dataStruct.actionStruct.rsvpCount = arguments.event.getValue('rsvpCount')>
			</cfif>
			<cfset local.dataStruct.actionStruct.mainurl = arguments.event.getValue('mainurl')>
			<cfset local.dataStruct.actionStruct.pg = arguments.event.getValue('pg')>
			<cfset local.dataStruct.actionStruct.eid = arguments.event.getValue('eid')>
			<cfset local.dataStruct.actionStruct.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname')>
			<cfset local.dataStruct.actionStruct.canDelEv = local.canDelEv>
			<cfset local.dataStruct.actionStruct.canEditEv = local.canEditEv>
			<cfset local.dataStruct.actionStruct.qryRegistrant = local.qryRegistrant>
			<cfset local.dataStruct.actionStruct.regLink = local.regLink>
			<cfset local.dataStruct.actionStruct.regBtn = local.regBtn>
			<cfset local.dataStruct.actionStruct.regMessage = local.regMessage>
			<cfset local.dataStruct.actionStruct.subAction = local.subAction>
			<cfset local.dataStruct.actionStruct.qryEventSponsors = arguments.strEvent.qryEventSponsors>
			<cfif arguments.strEvent.qryEventSponsors.recordCount>
				<cfset local.dataStruct.actionStruct.imageUUID = createUUID()>
				<cfset local.dataStruct.actionStruct.featuredThumbImageRootPath = "/userassets/#LCASE(arguments.event.getValue('mc_siteinfo.orgcode'))#/#LCASE(arguments.event.getValue('mc_siteinfo.sitecode'))#/featuredimages/thumbnails/">
			</cfif>
			<cfset local.dataStruct.actionStruct.allowRegister = local.allowRegister>
			<cfset local.dataStruct.actionStruct.eventtime = local.eventtime>
			<cfset local.dataStruct.actionStruct.regAlready = local.regAlready>
			<cfif arguments.strEvent.qryEventRegMeta.recordcount gt 0>
				<cfset local.dataStruct.actionStruct.qryEventRegRates = local.qryEventRegRates>

				<cfset local.dataStruct.actionStruct.showCredit = arguments.strEvent.qryEventRegMeta.showCredit>
				<cfif local.dataStruct.actionStruct.showCredit>
					<cfset local.dataStruct.actionStruct.strEventRegCredits = getCreditInfoForEvent(arguments.strEvent.qryEventMeta.eventID)>
					<cfif local.dataStruct.actionStruct.strEventRegCredits.creditCount is 0>
						<cfset local.dataStruct.actionStruct.showCredit = 0>
					</cfif>
				</cfif>
			<cfelse>
				<cfset local.dataStruct.actionStruct.showCredit = 0>
			</cfif>
			<cfset local.dataStruct.actionStruct.regEditAllowed = val(arguments.strEvent.qryEventRegMeta.registrantEditAllowed)>
			<cfset local.dataStruct.actionStruct.showEditRegLink = 0>
			<cfset local.dataStruct.actionStruct.regEditDeadlineContent = "">
			<cfset local.dataStruct.actionStruct.editRegLink = "">
			<cfif local.dataStruct.actionStruct.regEditAllowed is 1>
				<cfif len(arguments.strEvent.qryEventRegMeta.registrantEditDeadline) and DateDiff("n",now(),arguments.strEvent.qryEventRegMeta.registrantEditDeadline) gte 0>
					<cfset local.dataStruct.actionStruct.showEditRegLink = 1>
				<cfelse>
					<cfset local.dataStruct.actionStruct.regEditDeadlineContent = arguments.strEvent.qryEventRegMeta.registrantEditDeadlineContent>
				</cfif>
			</cfif>
			<cfif local.dataStruct.actionStruct.showEditRegLink and local.qryRegistrant.recordcount>
				<cfset local.regEditLinkEnc = Replace(URLEncodedFormat(ToBase64(Encrypt("#right(GetTickCount(),5)#|#local.memberIDForReg#|#local.qryRegistrant.registrantID#|#right(GetTickCount(),5)#","M3m18eR_CenTR@l"))),"%","xPcmKx","ALL")>
				<cfset local.dataStruct.actionStruct.editRegLink = arguments.event.getValue('mainurl') & '&eid=' & arguments.strEvent.qryEventMeta.eventID & '&evAction=regV2&regAction=editReg&er=' & local.regEditLinkEnc>
			</cfif>
			<cfset local.dataStruct.actionStruct.showRates = local.showRates>
			<cfset local.dataStruct.actionStruct.displayedCurrencyType = local.displayedCurrencyType>
			<cfset local.dataStruct.actionStruct.siteResourceID = this.siteResourceID>
			<cfset local.dataStruct.actionStruct.isOnlineMeeting = local.isOnlineMeeting>
			<cfset local.dataStruct.actionStruct.allowEnterOnlineMeeting = local.allowEnterOnlineMeeting>
			<cfset local.dataStruct.actionStruct.onlineMeetingLink = local.onlineMeetingLink>
			<cfset local.dataStruct.actionStruct.disallowEnterOnlineMeetingMessage = local.disallowEnterOnlineMeetingMessage>
			<cfset local.dataStruct.actionStruct.showLoginOnlineMeeting = local.showLoginOnlineMeeting>

			<cfset local.dataStruct.actionStruct.showAddToCalendar = arguments.strEvent.qryEventMeta.showAddCalendarLinks>
			<cfif local.dataStruct.actionStruct.showAddToCalendar and NOT dateDiff('d',local.dataStruct.actionStruct.endTime, NOW()) LTE 0>
				<cfset local.dataStruct.actionStruct.showAddToCalendar = 0>
			</cfif>
			<cfset local.dataStruct.actionStruct.viewRealTimeRoster = val(local.strEventRights.ViewRealTimeRoster)>
			<cfif application.mcCacheManager.sessionValueExists("evRegV2") AND arrayLen(application.mcCacheManager.sessionGetValue("evRegV2").regCart)>
				<cfset local.dataStruct.actionStruct.hasPendingRegistrations = true>
			<cfelse>
				<cfset local.dataStruct.actionStruct.hasPendingRegistrations = false>
			</cfif>
			
			<cfset local.strEventHost = { 
				"mainurl":arguments.event.getValue('mainurl'), 
				"mainregurl":arguments.event.getValue('mainregurl'), 
				"sitename":arguments.event.getValue('mc_siteInfo.sitename'), 
				"mainhostname":arguments.event.getValue('mc_siteInfo.mainhostname'),
				"scheme":arguments.event.getValue('mc_siteInfo.scheme'),
				"currencyType":arguments.event.getValue('mc_siteInfo.defaultCurrencyType')
			}>
			<cfset local.dataStruct.actionStruct.structuredEventJSON_LD = getStructuredEventData(strEvent=arguments.strEvent, strEventHost=local.strEventHost, mode="eventDetail")>

			<cfif now() gt arguments.strEvent.qryEventRegMeta.enddate AND len(arguments.strEvent.qryEventMeta.remarketingURL)>
				<cfset local.dataStruct.actionStruct.remarketingURL = arguments.strEvent.qryEventMeta.remarketingURL>
				<cfset local.dataStruct.actionStruct.remarketingText = arguments.strEvent.qryEventMeta.remarketingText>
				<cfset local.dataStruct.actionStruct.remarketingBtnText = arguments.strEvent.qryEventMeta.remarketingBtnText>
			<cfelse>
				<cfset local.dataStruct.actionStruct.remarketingURL = "">
				<cfset local.dataStruct.actionStruct.remarketingText = "">
				<cfset local.dataStruct.actionStruct.remarketingBtnText = "">
			</cfif>

			<cfsavecontent variable="local.eventJSON_LD">
				<cfoutput>
					<script type="application/ld+json">
						#serializeJSON(local.dataStruct.actionStruct.structuredEventJSON_LD)#
					</script>
					<meta property="og:title" content="#arguments.strEvent.qryEventMeta.eventContentTitle#" />
					<meta property="og:type" content="event" />
					<meta property="og:url" content="#local.dataStruct.actionStruct.structuredEventJSON_LD.url#" />
					<cfif structKeyExists(local.dataStruct.actionStruct.structuredEventJSON_LD, "image") and arrayLen(local.dataStruct.actionStruct.structuredEventJSON_LD.image) gt 0>
					<meta property="og:image" content="#local.dataStruct.actionStruct.structuredEventJSON_LD.image[1]#" />
					</cfif>
					<meta property="event:description" content=#serializeJSON(left(local.dataStruct.actionStruct.structuredEventJSON_LD.description, 70))# />
					<meta property="event:start_time" content="#local.dataStruct.actionStruct.structuredEventJSON_LD.startDate#" />
					<meta property="event:end_time" content="#local.dataStruct.actionStruct.structuredEventJSON_LD.endDate#" />

					<cfif LEN(local.XUserName)>
					<meta name="twitter:card" content="summary"> 
					<meta name="twitter:site" content="@#local.XUserName#"> 
					</cfif>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#local.eventJSON_LD#">

			<cfset local.dataStruct.actionStruct.evalURL = "">

			<cfif local.centralEnd gt now()>
				<cfheader name="X-Robots-Tag" value="unavailable_after: #Datetimeformat(DateAdd("d", 1, local.utcEnd), "ISO8601",local.selectedTimeZoneCode)#">
			<cfelse>
				<cfheader name="X-Robots-Tag" value="noindex">

				<!--- evaluation form --->
				<cfif val(local.qryRegistrant.attended) AND listLen(local.qryRegistrant.pendingEvalFormIDs)>
					<cfset local.dataStruct.actionStruct.evalURL = "#arguments.event.getValue('mainurl')#&evAction=showEval&eid=#arguments.event.getValue('eid')#&formID=#listFirst(local.qryRegistrant.pendingEvalFormIDs)#">
				</cfif>
			</cfif>
		</cfif>
		
		<cfreturn returnAppStruct(local.dataStruct,local.viewToUse)>
	</cffunction>

	<cffunction name="getCreditInfoForEvent" access="public" returntype="struct" output="no">
		<cfargument name="eventID" type="numeric" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.retStruct = StructNew()>
		<cfset local.qryEventRegCredits = CreateObject("component","model.admin.credit.credit").getCreditOfferedGrid("events",arguments.eventID)>

		<cfquery name="local.qryEventRegCreditsApproved" dbtype="query">
			select AuthorityName, offeredCreditTypes, statementAppProvider, creditMessage
			from [local].qryEventRegCredits	
			where status = 'Approved'
			order by AuthorityName
		</cfquery>
		<cfset local.retStruct.creditCount = local.qryEventRegCreditsApproved.recordcount>

		<cfsavecontent variable="local.retStruct.detail">
			<cfoutput query="local.qryEventRegCreditsApproved">
				<cfset local.arrTypes = xmlSearch(local.qryEventRegCreditsApproved.offeredCreditTypes,"/creditTypes/ect")>
				<b><u>#local.qryEventRegCreditsApproved.AuthorityName#</u></b><br/><br/>
				Credit has been approved with the #local.qryEventRegCreditsApproved.authorityName# for 
				<cfloop from="1" to="#arrayLen(local.arrTypes)#" index="local.thisTypeNum">
					<b>#local.arrTypes[local.thisTypeNum].xmlAttributes.creditValue# #local.arrTypes[local.thisTypeNum].xmlAttributes.creditType# credit<cfif local.arrTypes[local.thisTypeNum].xmlAttributes.creditValue is not 1>s</cfif></b><cfif local.thisTypeNum is arrayLen(local.arrTypes)>. <cfelse><cfif arrayLen(local.arrTypes) is 2> and <cfelse><cfif local.thisTypeNum is arrayLen(local.arrTypes) - 1>, and <cfelse>, </cfif></cfif></cfif>
				</cfloop>
				#local.qryEventRegCreditsApproved.statementAppProvider#
				<cfif len(local.qryEventRegCreditsApproved.creditMessage)><br/><br/>#local.qryEventRegCreditsApproved.creditMessage#</cfif>
				<br/><br/>
			</cfoutput>		
		</cfsavecontent>
		
		<cfreturn local.retStruct>
	</cffunction>

	<cffunction name="showOnlineMeeting" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="strEvent" type="struct" required="yes">

		<cfset var local = structNew()>

		<!--- no event found or inactive event --->
		<cfif arguments.strEvent.qryEventMeta.recordcount is 0 or arguments.strEvent.qryEventMeta.status neq "A">
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">

		<!--- if redirect link --->
		<cfelseif len(arguments.strEvent.qryEventRegMeta.onlineEmbedOverrideLink)>
			<cflocation url="#arguments.strEvent.qryEventRegMeta.onlineEmbedOverrideLink#" addtoken="no">

		<!--- not online meeting or has redirect link or not in enter times --->
		<cfelseif (arguments.strEvent.qryEventRegMeta.isOnlineMeeting is 0 
			or now() lt arguments.strEvent.qryEventRegMeta.onlineEnterStartTime 
			or now() gte arguments.strEvent.qryEventRegMeta.onlineEnterEndTime) AND NOT arguments.event.valueExists('prevmode')>
			<cflocation url="#arguments.event.getValue('mainurl')#&eid=#arguments.strEvent.qryEventMeta.eventID#&evAction=showDetail" addtoken="no">
		</cfif>

		<cfif val(arguments.event.getValue('mid',0))>
			<cfset local.orgMemberID = arguments.event.getValue('mid')>
		<cfelse>
			<cfset local.orgMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID)>
		</cfif>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRegistrant">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select top 1 evr.registrantID
			from dbo.ev_registrants as evr
			inner join dbo.ev_registration as r on r.registrationID = evr.registrationID 
				and evr.recordedOnSiteID = r.siteID
				and evr.status = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="A">
				and r.eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEvent.qryEventMeta.eventID#">
			inner join dbo.ams_members as m on m.memberid = evr.memberid
			inner join dbo.ams_members as m2 on m2.memberid = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.orgMemberID#">
				and m2.activeMemberID = m.activeMemberID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<!--- if not reg --->
		<cfif local.qryRegistrant.recordcount is 0 AND NOT arguments.event.valueExists('prevmode')>
			<cflocation url="#arguments.event.getValue('mainurl')#&eid=#arguments.strEvent.qryEventMeta.eventID#&evAction=showDetail" addtoken="no">
		</cfif>	

		<!--- show embed html --->
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>
		<cfset local.viewToUse = 'events/#local.viewDirectory#/eventOnlineMeeting'>
		<cfset local.dataStruct = StructNew()>
		<cfset local.dataStruct.actionStruct = StructNew()>
		<cfset local.dataStruct.actionStruct.eventid = arguments.strEvent.qryEventMeta.eventID>
		<cfset local.dataStruct.actionStruct.embedcode = arguments.strEvent.qryEventRegMeta.onlineEmbedCode>
		<cfset local.dataStruct.actionStruct.mid = local.orgMemberID>
		<cfset local.dataStruct.actionStruct.EventContentTitle = arguments.strEvent.qryEventMeta.EventContentTitle>
		<cfif arguments.event.valueExists('prevmode')>
			<cfset local.dataStruct.actionStruct.prevmode = arguments.event.getValue('prevmode')>
		</cfif>

		<cfreturn returnAppStruct(local.dataStruct,local.viewToUse)>
	</cffunction>

	<cffunction name="showEvaluation" access="private" output="false" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="strEvent" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.data = "">

		<cfset local.memberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID)>
		<cfset local.formID = arguments.event.getValue('formID')>
		<cfset local.FBAction = arguments.event.getValue('FBAction','displayFormStepsWizard')>
		
		<!--- no event found or inactive event --->
		<cfif arguments.strEvent.qryEventMeta.recordcount IS 0 OR arguments.strEvent.qryEventMeta.status NEQ "A">
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
		</cfif>

		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.selectedTimeZoneCode = local.objTSTZ.getTZCodeFromTZID(arguments.strEvent.qryEventTimes_selected.timezoneid)>
		<cfset local.utcEnd = local.objTSTZ.convertTimeZone(arguments.strEvent.qryEventTimes_selected.endTime,local.selectedTimeZoneCode,"UTC")>
		<cfset local.centralEnd = local.objTSTZ.convertTimeZone(arguments.strEvent.qryEventTimes_selected.endTime,local.selectedTimeZoneCode,"US/Central")>

		<!--- event not completed --->
		<cfif local.centralEnd GT now()>
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
		</cfif>

		<!--- get reg info --->
		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryRegistrant">
			EXEC dbo.ev_getRegistrantInfo @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strEvent.qryEventMeta.eventID#">,
				@memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.memberID#">;
		</cfquery>

		<!--- invalid registrant --->
		<cfif NOT local.qryRegistrant.recordCount OR NOT val(local.qryRegistrant.attended)>
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
		
		<!--- eval already submitted --->
		<cfelseif local.FBAction NEQ 'displayThanks' AND listFind(local.qryRegistrant.completedEvalFormIDs,local.formID)>
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">

		<!--- eval not linked to this event --->
		<cfelseif local.FBAction NEQ 'displayThanks' AND NOT listFind(local.qryRegistrant.pendingEvalFormIDs,local.formID)>
			<cflocation url="#arguments.event.getValue('mainurl')#" addtoken="no">
		</cfif>

		<cfscript>
			local.objForm = CreateObject("component","model.formBuilder.FBForms");
			local.strArgs = {
				"FBFormID": local.formID,
				"FBAction": "displayFormStepsWizard",
				"FBEnrollmentID": local.qryRegistrant.registrantID,
				"FBDepoID": 0,
				"FBOrgCode": arguments.event.getValue('mc_siteInfo.orgCode'),
				"loadPoint": "Evaluation",
				"insertEmptyResponse": true,
				"emptyResponseInfo": {
					"FBdepoID": 0,
					"FBorgcode": arguments.event.getValue('mc_siteInfo.orgCode')
				},
				"FBxsl_displayForm": "#application.paths.localAssetRoot.path#common/images/formbuilder/formStepsWizard.xsl",
				"FBcss": "/assets/common/images/formbuilder/formStepsWizard.css",
				"FBPostURL": "#arguments.event.getValue('mainurl')#&evAction=showEval&eid=#arguments.event.getValue('eid')#&formID=#local.formID#&FBAction=postForm"
			};
		</cfscript>

		<cfswitch expression="#local.FBAction#">
			<cfcase value="displayFormStepsWizard">
				<!--- generate eval form --->
				<cfset local.strEvalForm = local.objForm.doAction(FBAction=local.FBAction, strArgs=local.strArgs)>

				<!--- failure --->
				<cfif NOT local.strEvalForm.success>
					<cfreturn returnAppStruct(local.strEvalForm.keyExists("errmsg") ? local.strEvalForm.errmsg : "We were unable to display the evaluation form. Try again.","echo")>
				</cfif>

				<!--- insert response --->
				<cfset insertEvaluationFormResponse(eventID=arguments.strEvent.qryEventMeta.eventID, registrantID=local.qryRegistrant.registrantID, formID=local.formID, responseID=local.strEvalForm.responseID)>

				<cfloop array="#local.strEvalForm.arrHead#" index="local.thisItem">
					<cfhtmlhead text="#local.thisItem#">
				</cfloop>

				<cfsavecontent variable="local.data">
					<cfoutput>#local.strEvalForm.formContent#</cfoutput>
				</cfsavecontent>
			</cfcase>
			<cfcase value="postForm">
				<cfset local.objFBResponses = CreateObject("component","model.formbuilder.FBResponses")>
			
				<cfset local.qryForm = local.objForm.getForm(formID=local.formID)>
				<cfset local.strCheckArgs = StructNew()>
				<cfset local.strCheckArgs.formID = local.strArgs.FBFormid>
				<cfset local.strCheckArgs.depoID = local.strArgs.FBdepoID>
				<cfset local.strCheckArgs.enrollmentID = local.strArgs.FBEnrollmentID>
				<cfset local.strCheckArgs.loadPoint = local.strArgs.loadPoint>
				<cfset local.strCheckArgs.formTypeAbbr = local.qryForm.formTypeAbbr>
				
				<!--- reached the maximum number of submissions --->
				<cfif NOT local.objFBResponses.underMaxResponsesPerUser(local.strCheckArgs)>
					<cfset local.data = '<div><b>You have reached the maximum number of submissions.</b></div>'>
					<cfreturn returnAppStruct(local.data,"echo")>
				</cfif>
				
				<cfset local.responseID = int(val(arguments.event.getValue('FBResponseID',0)))>
				
				<cfif local.responseID EQ 0>
					<cfset local.data = '<div><b>Invalid Response </b></div>'>
					<cfreturn returnAppStruct(local.data,"echo")>
				<cfelse>
					<cfset local.strSaveArgs = {
						"FBformID": local.formID,
						"FBdepoID": 0,
						"FBorgcode": arguments.event.getValue('mc_siteInfo.orgcode'),
						"formTypeAbbr": local.qryForm.formTypeAbbr,
						"FBholderResponse": 0,
						"formVars": {}
					}>
		
					<cfloop collection="#arguments.event.getValue('fieldnames','')#" item="local.key">
						<cfif left(local.key, 2) eq "q_">
							<cfset local.strSaveArgs.formVars[local.key] = arguments.event.getValue(local.key)>
						</cfif>
					</cfloop>

					<cfset local.objFBResponses.replaceFormResponse(responseID=local.responseID, strArgs=local.strSaveArgs)>

					<!--- complete evaluation --->
					<cfset completeEvaluationForm(eventID=arguments.strEvent.qryEventMeta.eventID, registrantID=local.qryRegistrant.registrantID, formID=local.formID, responseID=local.responseID)>

					<cflocation url="#arguments.event.getValue('mainurl')#&evAction=showEval&eid=#arguments.event.getValue('eid')#&formID=#local.formID#&FBAction=displayThanks" addtoken="no">
				</cfif>
			</cfcase>
			<cfdefaultcase>
				<cfsavecontent variable="local.data">
					<cfoutput>#local.objForm.doAction(FBAction=local.FBAction, strArgs=local.strArgs)#</cfoutput>
				</cfsavecontent>
			</cfdefaultcase>
		</cfswitch>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="initOMLog" access="public" output="false" returnType="struct">
		<cfargument name="eid" type="string" required="yes">	<!--- string on purpose --->
		<cfargument name="mid" type="string" required="yes">	<!--- string on purpose --->
		
		<cfset var local = structNew()>
		<cfset local.strReturn = structNew()>
		<cfset local.strReturn['success'] = false>
		<cfset local.strReturn['token'] = ''>
		<cfset local.strReturn['jsurl'] = ''>

		<cftry>
			<cfif NOT len(trim(arguments.mid))>
				<cfset arguments.mid = session.cfcuser.memberdata.memberid>
			</cfif>
			<cfif arguments.mid gt 0>
				<cfstoredproc datasource="#application.dsn.platformstatsMC.dsn#" procedure="ev_InsertOnlineMeetingLog">
					<cfprocparam cfsqltype="cf_sql_integer" type="In" value="#arguments.eid#">
					<cfprocparam cfsqltype="cf_sql_integer" type="In" value="#arguments.mid#">
					<cfprocparam cfsqltype="cf_sql_bigint" type="In" value="#session.cfcuser.statsSessionID#">
					<cfprocparam cfsqltype="CF_SQL_IDSTAMP" type="out" variable="local.strReturn.token">
				</cfstoredproc>
				<cfset local.strReturn.success = true>
				<cfset local.strReturn.jsurl = "//" & application.paths.backendPlatform.hostname & "/">
			</cfif>
			<cfcatch type="any">
				<!--- do not email. nothing for us to do about it. This was always triggered by admins logging in and not being registered. --->
			</cfcatch>
		</cftry>
		
		<cfreturn local.strReturn>		
	</cffunction>

	<cffunction name="saveRSVP" access="private" output="false" returntype="numeric">
		<cfargument name="event" type="any">
		<cfargument name="strEvent" type="struct" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.savedCount = 0>

		<cfif arguments.strEvent.qryEventRegMeta.registrationType eq "RSVP">
			<cfquery name="local.qrySaveRSVP" datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY

					IF OBJECT_ID('tempdb..##tmpRSVP') IS NOT NULL 
						DROP TABLE ##tmpRSVP;
					CREATE TABLE ##tmpRSVP (salutation varchar(50), firstname varchar(70), lastname varchar(70), company varchar(200),
						email varchar(200), phone varchar(30));

					BEGIN TRAN;
						<cfloop from="1" to="5" index="local.x">
							<cfset local.email = left(arguments.event.getTrimValue('email' & local.x, ''), 200)>
							<cfif len(arguments.event.getTrimValue('firstname' & local.x,'')) and len(arguments.event.getTrimValue('lastname' & local.x,'')) and isValid("email", local.email)>
								
								INSERT INTO dbo.ev_rsvp (registrationid, salutation, firstname, lastname, company, email, phone, dateentered)
									OUTPUT INSERTED.salutation, INSERTED.firstname, INSERTED.lastname, INSERTED.company, INSERTED.email, INSERTED.phone
									INTO ##tmpRSVP
								VALUES (
									<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.strEvent.qryEventRegMeta.registrationid#">,
									<cfqueryparam cfsqltype="cf_sql_varchar" maxlength="50" value="#left(arguments.event.getTrimValue('salutation' & local.x,''), 50)#">,
									<cfqueryparam cfsqltype="cf_sql_varchar" maxlength="70" value="#left(arguments.event.getTrimValue('firstname' & local.x,''), 70)#">,
									<cfqueryparam cfsqltype="cf_sql_varchar" maxlength="70" value="#left(arguments.event.getTrimValue('lastname' & local.x,''), 70)#">,
									<cfqueryparam cfsqltype="cf_sql_varchar" maxlength="200" value="#left(arguments.event.getTrimValue('company' & local.x,''), 200)#">,
									<cfqueryparam cfsqltype="cf_sql_varchar" maxlength="200" value="#local.email#">,
									<cfqueryparam cfsqltype="cf_sql_varchar" maxlength="30" value="#left(arguments.event.getTrimValue('phone' & local.x,''), 30)#">,
									getdate()
								);
							</cfif>
						</cfloop>
					COMMIT TRAN;

					SELECT salutation, firstname, lastname, company, email, phone
					FROM ##tmpRSVP
					ORDER BY lastname, firstname;

					IF OBJECT_ID('tempdb..##tmpRSVP') IS NOT NULL 
						DROP TABLE ##tmpRSVP;

				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			<cfset local.savedCount = local.qrySaveRSVP.recordcount>

			<cfif local.savedCount and len(arguments.strEvent.qryEventRegMeta.notifyEmail)>

				<cfsavecontent variable="local.emailContent">
					<cfoutput>
					<p>An online RSVP was just submitted for the following event:</p>
					<p>
						#arguments.strEvent.qryEventMeta.eventContentTitle#<br/>
						Date: #DateFormat(arguments.strEvent.qryEventTimes_selected.startTime,'m/d/yyyy')#
					</p>
					<cfloop query="local.qrySaveRSVP">
						<p>
							Name: #local.qrySaveRSVP.salutation# #local.qrySaveRSVP.firstname# #local.qrySaveRSVP.lastname#<br/>
							Company Name: #local.qrySaveRSVP.company#<br/>
							E-mail: #local.qrySaveRSVP.email#<br/>
							Phone: #local.qrySaveRSVP.phone#<br/>
						</p>
					</cfloop>
					</cfoutput>
				</cfsavecontent>

				<cfscript>
					local.arrEmailTo = [];
					local.toEmailArr = listToArray(arguments.strEvent.qryEventRegMeta.notifyEmail,';');
					for (local.i=1; local.i lte arrayLen(local.toEmailArr); local.i++) {
						local.arrEmailTo.append({ name:'', email:local.toEmailArr[local.i] });
					}
				</cfscript>

				<cfif arrayLen(local.arrEmailTo)>
					<cfset local.eventSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='Events',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
					<cfset local.strEmailResult = application.objEmailWrapper.sendMailESQ(
						emailfrom={ name=arguments.event.getValue('mc_siteinfo.orgname'), email=arguments.event.getValue('mc_siteInfo.networkEmailFrom') },
						emailto=local.arrEmailTo,
						emailreplyto=arguments.event.getValue('mc_siteInfo.supportProviderEmail'),
						emailsubject="Online RSVP for #arguments.strEvent.qryEventMeta.EventContentTitle#",
						emailtitle="#arguments.event.getValue('mc_siteinfo.sitename')# RSVP Confirmation",
						emailhtmlcontent=local.emailContent,
						emailAttachments=[],
						siteID=arguments.event.getValue('mc_siteinfo.siteid'),
						memberID=arguments.event.getValue('mc_siteinfo.sysMemberID'),
						messageTypeID=application.objCommon.getMessageTypeIDFromMessageTypeCode(messageTypeCode="EVENTREGCONF"),
						sendingSiteResourceID=local.eventSiteResourceID
					)>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.savedCount>
	</cffunction>

	<cffunction name="showCalendar" access="private" output="no" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="settings" type="struct" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.objEventReg = CreateObject("component","eventRegV2")>
		<cfset local.objCalendar = CreateObject("component","model.events.calendar")>
		<cfset local.objEventReg.paramRegSession(eid=0)>
		
		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>

		<!--- show currency types --->
		<cfset local.displayedCurrencyType = "">
		<cfif application.objSiteInfo.getSiteInfo(variables.instancesettings.sitecode).showCurrencyType is 1>
			<cfset local.displayedCurrencyType = application.objSiteInfo.getSiteInfo(variables.instancesettings.sitecode).defaultCurrencyType>
		</cfif>

		<cfset local.dataStruct = structNew()>
		<cfset local.dataStruct.evAction = arguments.event.getTrimValue('evAction')>
		<cfif variables.isBot is 1>
			<cfset local.dataStruct.evAction = "listAll">
		</cfif>
		<cfset local.dataStruct.evRegCart = local.objEventReg.evRegCartToQuery()>
		<cfset local.dataStruct.regCartExists = arguments.event.ValueExists('regcartv2')>
		<cfset local.dataStruct.mainurl = arguments.event.getValue('mainurl')>
		<cfset local.dataStruct.baseQueryString = cgi.query_string>
		<cfset local.dataStruct.appRightsStruct = variables.appRightsStruct>
		<cfset local.dataStruct.orgMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.settings.orgID)>
		<cfset local.dataStruct.displayedCurrencyType = local.displayedCurrencyType>
		
		<cfif (local.dataStruct.evAction neq "viewOptions" and local.dataStruct.evRegCart.recordcount gte 1 and not local.dataStruct.regCartExists) OR 
				(local.dataStruct.evAction eq "viewOptions" or local.dataStruct.evRegCart.recordcount lt 1)>
			<cfset local.strCalApp = local.objCalendar.runDefaultEvent(arguments.event,this.appInstanceID,this.siteResourceID)>
			<cfset local.dataStruct.strCalApp = local.strCalApp>
			<cfset local.dataStruct.strCalApp.data.saveCategoriesExists = arguments.event.valueExists('saveCategories')>
		</cfif>

		<!--- Calendar views should not be indexed by search engines. They should only follow event links from the page and index those --->
		<cfheader name="X-Robots-Tag" value="noindex">
		<cfreturn returnAppStruct(local.dataStruct,"events/#local.viewDirectory#/eventCalendar")>
	</cffunction>

	<cffunction name="addEvent" access="private" output="no" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="settings" type="struct" required="yes">
		
		<cfset var local = structNew()>

		<cfif not (structKeyExists(variables.appRightsStruct,'AddEvent') AND variables.appRightsStruct['AddEvent'] is 1)>
			<cfset arguments.event.setValue('evAction','')>
			<cfreturn showCalendar(arguments.event, arguments.settings)>
		</cfif>

		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>

		<cfscript>
			local.objAdminEvent = CreateObject("component","model.admin.events.event");
			local.objCalendar = CreateObject("component","model.events.calendar");
			local.strCalApp = local.objCalendar.runDefaultEvent(arguments.event,this.appInstanceID,this.siteResourceID);

			local.dataStruct = StructNew();
			local.dataStruct.actionStruct = StructNew();
			local.dataStruct.instanceSettings = variables.instanceSettings;
			local.dataStruct.calendarSettings = local.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteID'), calendarID=local.strCalApp.data.calendarSettings.calendarID);
			local.dataStruct.baseQueryString = getBaseQueryString(false);
			
			local.dataStruct.evSubAction = arguments.event.getValue('evSubAction','');
			local.dataStruct.calMonth = arguments.event.getValue('calmonth','');
		
			local.dataStruct.actionStruct.formLink = '/?#getBaseQueryString(false)#&evAction=insertEv';
			if (len(local.dataStruct.evSubAction))
			{
				local.dataStruct.actionStruct.formLink = '#local.dataStruct.actionStruct.formLink#&evSubAction=#local.dataStruct.evSubAction#';
			}
			local.dataStruct.actionStruct.eventID = 0;
			local.dataStruct.actionStruct.orgMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID);

			local.languageID = arguments.event.getValue('mc_siteInfo.defaultLanguageID');
			local.dataStruct.actionStruct.qryCategories = CreateObject("component","model.events.calendar").getCategories(local.dataStruct.calendarSettings.calendarID,local.languageID,arguments.event.getValue('mc_siteInfo.siteID'));
			local.dataStruct.actionStruct.qryTimeZones = local.objAdminEvent.getTimeZones();

			local.dataStruct.actionStruct.status = 'A';
			local.dataStruct.actionStruct.eventTypeID = 1;
			local.dataStruct.actionStruct.eventContentID = 0;
			local.dataStruct.actionStruct.eventContentTitle = '';
			local.dataStruct.actionStruct.eventSubTitle = '';
			local.dataStruct.actionStruct.reportCode = '';
			local.dataStruct.actionStruct.categoryID = 0;
			local.dataStruct.actionStruct.eventContent = '';
			local.dataStruct.actionStruct.eventStartTime = CreateDateTime(year(now()),month(now()),day(now()),9,0,0);
			local.dataStruct.actionStruct.eventEndTime = CreateDateTime(year(now()),month(now()),day(now()),17,0,0);
			local.dataStruct.actionStruct.isAllDayEvent = 0;
			local.dataStruct.actionStruct.lockTimeZone = 0;
			local.dataStruct.actionStruct.locationContentID = 0;
			local.dataStruct.actionStruct.locationContentTitle = '';
			local.dataStruct.actionStruct.locationContent = '';
			local.dataStruct.actionStruct.travelContentID = 0;
			local.dataStruct.actionStruct.travelContentTitle = '';
			local.dataStruct.actionStruct.travelContent = '';
			local.dataStruct.actionStruct.contactContentID = 0;
			local.dataStruct.actionStruct.contactContentTitle = '';
			local.dataStruct.actionStruct.contactContent = '';
			local.dataStruct.actionStruct.cancelContentID = 0;
			local.dataStruct.actionStruct.cancelContentTitle = '';
			local.dataStruct.actionStruct.cancelContent = '';
			local.dataStruct.actionStruct.informationContent = '';
			local.dataStruct.actionStruct.informationContentID = 0;					
			local.dataStruct.actionStruct.defaultTimeZoneID = arguments.event.getValue('mc_siteinfo.defaultTimeZoneID');
			local.dataStruct.actionStruct.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname');
			local.dataStruct.actionStruct.hiddenFromCalendar = 0;
			local.dataStruct.actionStruct.emailContactContent = 0;	
			local.dataStruct.actionStruct.emailLocationContent = 0;	
			local.dataStruct.actionStruct.emailCancelContent = 0;	
			local.dataStruct.actionStruct.emailTravelContent = 0;			
		</cfscript>
		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		
		<cfset local.strCrossEventFields = createObject("component","model.admin.common.modules.customFields.customFields").renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
		viewMode='default', resourceType='EventAdmin', areaName='Event', csrid=local.eventAdminSiteResourceID,
		detailID=0, hideAdminOnly=1, itemType='CrossEvent', itemID=0, trItemType='', trApplicationType='', excludeNonRequiredFields=1)>
		<cfset local.dataStruct.actionStruct.strCrossEventFields = local.strCrossEventFields>
		<cfreturn returnAppStruct(local.dataStruct,"events/#local.viewDirectory#/editEv")>
	</cffunction>

	<cffunction name="editEvent" access="private" output="no" returntype="struct">
		<cfargument name="event" type="any">
		<cfargument name="settings" type="struct" required="yes">
		
		<cfscript>
			var local = structNew();
			
			local.objAdminEvent = CreateObject("component","model.admin.events.event");
			local.objCalendar = CreateObject("component","model.events.calendar");
			local.strCalApp = local.objCalendar.runDefaultEvent(arguments.event,this.appInstanceID,this.siteResourceID);
			local.languageID = arguments.event.getValue('mc_siteInfo.defaultLanguageID');
			
			local.dataStruct = StructNew();
			local.dataStruct.actionStruct = StructNew();
			local.dataStruct.actionStruct.orgMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID);
			local.dataStruct.instanceSettings = variables.instanceSettings;
			local.dataStruct.calendarSettings = local.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteID'), calendarID=local.strCalApp.data.calendarSettings.calendarID);
			local.dataStruct.baseQueryString = getBaseQueryString(false);
			
			local.strEvent = getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=local.languageID);
		</cfscript>

		<cfset local.viewDirectory = arguments.event.getValue('viewDirectory', 'default')>
		
		<cfif not (structKeyExists(variables.appRightsStruct,'evEditOwn')
					AND variables.appRightsStruct['evEditOwn'] is 1
					AND local.dataStruct.actionStruct.orgMemberID eq local.strEvent.qryEventMeta.enteredByMemberID)>
			<cfset arguments.event.setValue('evAction','')>
			<cfreturn showCalendar(arguments.event, arguments.settings)>
		</cfif>

		<cfscript>
			local.dataStruct.evSubAction = arguments.event.getValue('evSubAction','');
			local.dataStruct.calMonth = arguments.event.getValue('calmonth','');
		
			local.dataStruct.actionStruct.formLink = '/?#getBaseQueryString(false)#&evAction=saveEv';
			if (len(local.dataStruct.evSubAction))
			{
				local.dataStruct.actionStruct.formLink = '#local.dataStruct.actionStruct.formLink#&evSubAction=#local.dataStruct.evSubAction#';
			}
			local.dataStruct.actionStruct.testLink = '/?#getBaseQueryString(false)#';
		
			local.dataStruct.actionStruct.eventID = local.strEvent.qryEventMeta.eventID;

			local.languageID = arguments.event.getValue('mc_siteInfo.defaultLanguageID');
			local.dataStruct.actionStruct.qryCategories = CreateObject("component","model.events.calendar").getCategories(local.dataStruct.calendarSettings.calendarID,local.languageID,arguments.event.getValue('mc_siteInfo.siteID'));
			local.dataStruct.actionStruct.qryTimeZones = local.objAdminEvent.getTimeZones();

			local.dataStruct.actionStruct.status = local.strEvent.qryEventMeta.status;
			local.dataStruct.actionStruct.eventTypeID = local.strEvent.qryEventMeta.eventTypeID;
			local.dataStruct.actionStruct.eventContentID = local.strEvent.qryEventMeta.eventContentID;
			local.dataStruct.actionStruct.eventContentTitle = local.strEvent.qryEventMeta.eventContentTitle;
			local.dataStruct.actionStruct.eventSubTitle = local.strEvent.qryEventMeta.eventSubTitle;
			local.dataStruct.actionStruct.reportCode = local.strEvent.qryEventMeta.reportCode;
			local.dataStruct.actionStruct.categoryID = local.strEvent.qryEventCategories.categoryID;
			local.dataStruct.actionStruct.eventContent = local.strEvent.qryEventMeta.eventContent;
			local.dataStruct.actionStruct.eventStartTime = local.strEvent.qryEventTimes_selected.startTime;
			local.dataStruct.actionStruct.eventEndTime = local.strEvent.qryEventTimes_selected.endTime;
			local.dataStruct.actionStruct.defaultTimeZoneID = local.strEvent.qryEventTimes_selected.timeZoneID;
			local.dataStruct.actionStruct.isAllDayEvent = local.strEvent.qryEventMeta.isAllDayEvent;
			local.dataStruct.actionStruct.lockTimeZone = local.strEvent.qryEventMeta.lockTimeZoneID;
			local.dataStruct.actionStruct.locationContentID = local.strEvent.qryEventMeta.locationContentID;
			local.dataStruct.actionStruct.locationContentTitle = local.strEvent.qryEventMeta.locationContentTitle;
			local.dataStruct.actionStruct.locationContent = local.strEvent.qryEventMeta.locationContent;
			local.dataStruct.actionStruct.travelContentID = local.strEvent.qryEventMeta.travelContentID;
			local.dataStruct.actionStruct.travelContentTitle = local.strEvent.qryEventMeta.travelContentTitle;
			local.dataStruct.actionStruct.travelContent = local.strEvent.qryEventMeta.travelContent;
			local.dataStruct.actionStruct.contactContentID = local.strEvent.qryEventMeta.contactContentID;
			local.dataStruct.actionStruct.contactContentTitle = local.strEvent.qryEventMeta.contactContentTitle;
			local.dataStruct.actionStruct.contactContent = local.strEvent.qryEventMeta.contactContent;
			local.dataStruct.actionStruct.cancelContentID = local.strEvent.qryEventMeta.cancelContentID;
			local.dataStruct.actionStruct.cancelContentTitle = local.strEvent.qryEventMeta.cancelContentTitle;
			local.dataStruct.actionStruct.cancelContent = local.strEvent.qryEventMeta.cancelContent;			
			local.dataStruct.actionStruct.informationContentID = local.strEvent.qryEventMeta.informationContentID;
			local.dataStruct.actionStruct.informationContentTitle = local.strEvent.qryEventMeta.informationContentTitle;
			local.dataStruct.actionStruct.informationContent = local.strEvent.qryEventMeta.informationContent;
			local.dataStruct.actionStruct.hiddenFromCalendar = local.strEvent.qryEventMeta.hiddenFromCalendar;
			local.dataStruct.actionStruct.emailContactContent = local.strEvent.qryEventMeta.emailContactContent;
			local.dataStruct.actionStruct.emailLocationContent = local.strEvent.qryEventMeta.emailLocationContent;
			local.dataStruct.actionStruct.emailCancelContent = local.strEvent.qryEventMeta.emailCancelContent;
			local.dataStruct.actionStruct.emailTravelContent = local.strEvent.qryEventMeta.emailTravelContent;	
			local.dataStruct.actionStruct.mainhostname = arguments.event.getValue('mc_siteInfo.mainhostname');	
		</cfscript>
		
		<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
		<cfset local.siteResourceID = local.objAdminEvent.getSiteResourceIDByEventID(siteID=arguments.event.getValue('mc_siteinfo.siteid'), eventID=val(arguments.event.getValue('eID',0)))>
		<cfset local.strCrossEventFields = createObject("component","model.admin.common.modules.customFields.customFields").renderResourceFields(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				viewMode='default', resourceType='EventAdmin', areaName='Event', csrid=local.eventAdminSiteResourceID,
				detailID=0, hideAdminOnly=1, itemType='CrossEvent', itemID=local.siteResourceID, trItemType='', trApplicationType='')>
		<cfset local.dataStruct.actionStruct.strCrossEventFields = local.strCrossEventFields>
		
		<cfreturn returnAppStruct(local.dataStruct,"events/#local.viewDirectory#/editEv")>
	</cffunction>

	<cffunction name="insertEvent" access="private" output="no" returntype="void">
		<cfargument name="event" type="any">

		<cfset var local = structNew()>

		<cfif (structKeyExists(variables.appRightsStruct,'AddEvent') AND variables.appRightsStruct['AddEvent'] is 1)>
			<cfset local.objAdminEvent = CreateObject("component","model.admin.events.event")>
			<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
			<cfset local.languageID	= arguments.event.getValue('mc_siteInfo.defaultLanguageID')>
			<cfset local.arrTimeZoneIDs = listToArray(local.objTSTZ.getTZIDList())>
			<cfif arguments.event.getValue('isAllDayEvent',0)>
				<cfset local.startDateTime = ParseDateTime(arguments.event.getValue('eventStartTime'))>
				<cfset local.endDateTime = ParseDateTime("#arguments.event.getValue('eventEndTime')# 23:59:59.997")>
			<cfelse>
				<cfset local.startDateTime = ParseDateTime("#replace(arguments.event.getValue('eventStartTime'),' - ',' ')#")>
				<cfset local.endDateTime = ParseDateTime("#replace(arguments.event.getValue('eventEndTime'),' - ',' ')#")>
				<cfset local.structAllEventTimes = CreateObject("component","model.admin.events.eventAdmin").getEventTimeinAllTimeZones(local.startDateTime,local.endDateTime,arguments.event.getValue('eventTimeZoneID',0))>
			</cfif>
			
			<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

			<cfset local.crossEventCustomFieldsXML = createObject("component","model.admin.common.modules.customFields.customFields").getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
				resourceType='EventAdmin', areaName='Event', csrid=local.eventAdminSiteResourceID, detailID=0, hideAdminOnly=0)>
			<cfset local.crossEventCustomFieldArr = xmlParse(local.crossEventCustomFieldsXML.returnXML).xmlRoot.xmlChildren>

			<!--- put cross-event custom fields and field types into array --->
			<cfset local.arrCrossEventCustomFields = []>
			<cfif arrayLen(local.crossEventCustomFieldArr)>
				<cfloop array="#local.crossEventCustomFieldArr#" index="local.thisfield">
					<cfset local.tmpAtt = local.thisfield.xmlattributes>
					<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
											displayTypeCode=local.tmpAtt.displayTypeCode, 
											dataTypeCode=local.tmpAtt.dataTypeCode, 
											value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','') }>
					<cfset arrayAppend(local.arrCrossEventCustomFields,local.tmpStr)>
				</cfloop>
			</cfif>
			
			<cfquery datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @timeID int, @eventID int, @siteResourceId int, @eventContentID int, @locationContentID int, @travelContentID int, @contactContentID int, @cancelContentID int, @informationContentID int;

					BEGIN TRAN;

						EXEC dbo.ev_createEvent 
						@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">,
						@calendarID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('cID')#">,
						@eventTypeID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eventTypeID')#">,
						@enteredByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID)#">,
						@eventSubTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.event.getValue('eventSubTitle')#">,
						<cfif arguments.event.getValue('lockTimeZoneID',0)>
							@lockTimeZoneID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('lockTimeZoneID')#">,
						<cfelse>
							@lockTimeZoneID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" null="yes">,
						</cfif>
						@isAllDayEvent=<cfqueryparam cfsqltype="CF_SQL_BIT" value="#arguments.event.getValue('isAllDayEvent',0)#">,
						@altRegistrationURL=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" null="yes">,
						@status=<cfqueryparam cfsqltype="cf_sql_char" value="#arguments.event.getValue('status')#">,
						@reportCode=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('reportCode','')#">,
						@hiddenFromCalendar=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('hiddenFromCalendar',0)#">,
						@emailContactContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailContactContent',0)#">,
						@emailLocationContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailLocationContent',0)#">,
						@emailCancelContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailCancelContent',0)#">,
						@emailTravelContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailTravelContent',0)#">,
						@parentEventID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" null="true">,
						@eventID=@eventID OUTPUT;

						EXEC dbo.ev_saveEventCategories 
						@eventID=@eventID,
						@recordedByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
						@categoryIDList=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="0#arguments.event.getValue('categoryID')#">;

						EXEC dbo.ev_refreshCalendarEventsCache 
						@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;

						EXEC dbo.ev_refreshCalendarEventsCategoryIDList 
						@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">,
						@eventID=@eventID;				

						select @eventID=eventID, @siteResourceId=siteResourceId, @eventContentID=eventContentID, @locationContentID=locationContentID, @travelContentID=travelContentID, 
						@contactContentID=contactContentID, @cancelContentID=cancellationPolicyContentID, @informationContentID=informationContentID
						FROM dbo.ev_events 
						WHERE eventID = @eventID;
						
						-- cross-event custom fields
						<cfif arrayLen(local.arrCrossEventCustomFields)>
							#local.objAdminEvent.insertEventCustomFields(arrCrossEventCustomFields=local.arrCrossEventCustomFields)#
						</cfif>

						<cfif arguments.event.getValue('isAllDayEvent',0)>
							<cfloop array="#local.arrTimeZoneIDs#" index="local.tzid">
								EXEC dbo.ev_createTime 
								@eventID=@eventID,
								@timeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.tzID#">,
								@startTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startDateTime#">,
								@endTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endDateTime#">,
								@timeID=@timeID OUTPUT;
							</cfloop>
						<cfelse>
							<cfloop collection="#local.structAllEventTimes#" item="local.key">
								EXEC dbo.ev_createTime 
								@eventID=@eventID,
								@timeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.structAllEventTimes[local.key].id#">,
								@startTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.structAllEventTimes[local.key].starttime#">,
								@endTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.structAllEventTimes[local.key].endtime#">,
								@timeID=@timeID OUTPUT;
							</cfloop>
						</cfif>

						EXEC dbo.cms_updateContent 
						@contentID=@eventContentID,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('eventContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('eventContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=@contactContentID,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('contactContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('contactContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=@locationContentID,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('locationContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('locationContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=@cancelContentID,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('cancelContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('cancelContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=@travelContentID,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('travelContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('travelContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=@informationContentID,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getValue('informationContent','')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.ev_queueEventSearchText 
						@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">,
						@eventID=@eventID;
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
		</cfif>
	</cffunction>	
	
	<cffunction name="saveEvent" access="private" output="no" returntype="void">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();
			
			local.objAdminEvent = CreateObject("component","model.admin.events.event");
			local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone");
			local.objCalendar = CreateObject("component","model.events.calendar");
			local.strCalApp = local.objCalendar.runDefaultEvent(arguments.event,this.appInstanceID,this.siteResourceID);
			local.languageID = arguments.event.getValue('mc_siteInfo.defaultLanguageID');
			
			local.dataStruct = StructNew();
			local.dataStruct.actionStruct = StructNew();
			local.dataStruct.actionStruct.orgMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID);
			local.dataStruct.instanceSettings = variables.instanceSettings;
			local.dataStruct.calendarSettings = local.objAdminEvent.getCalendar(siteID=arguments.event.getValue('mc_siteinfo.siteID'), calendarID=local.strCalApp.data.calendarSettings.calendarID);
			local.dataStruct.baseQueryString = getBaseQueryString(false);
			
			local.strEvent = getEvent(eventid=arguments.event.getValue('eID',0),siteid=arguments.event.getValue('mc_siteinfo.siteid'),languageid=local.languageID);
		</cfscript>

		<cfif (structKeyExists(variables.appRightsStruct,'evEditOwn') 
					AND variables.appRightsStruct['evEditOwn'] is 1) 
					AND local.dataStruct.actionStruct.orgMemberID eq local.strEvent.qryEventMeta.enteredByMemberID>

			<!--- lock time zone --->
			<cfif arguments.event.getValue('lockTimeZone',0)>
				<cfset local.lockTimeZoneID = arguments.event.getValue('eventTimeZoneID',0)>
			<cfelse>
				<cfset local.lockTimeZoneID = 0>
			</cfif>

			<cfquery name="local.qryGetNotes" datasource="#application.dsn.membercentral.dsn#">
				select internalNotes, remarketingURL
				from dbo.ev_events
				where eventID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.strEvent.qryEventMeta.eventID#">
			</cfquery>

			<cfset local.arrTimeZoneIDs = listToArray(local.objTSTZ.getTZIDList())>
			<cfif arguments.event.getValue('isAllDayEvent',0)>
				<cfset local.startDateTime = ParseDateTime(arguments.event.getValue('eventStartTime'))>
				<cfset local.endDateTime = ParseDateTime("#arguments.event.getValue('eventEndTime')# 23:59:59.997")>
			<cfelse>
				<cfset local.startDateTime = ParseDateTime("#replace(arguments.event.getValue('eventStartTime'),' - ',' ')#")>
				<cfset local.endDateTime = ParseDateTime("#replace(arguments.event.getValue('eventEndTime'),' - ',' ')#")>
				<cfset local.structAllEventTimes = CreateObject("component","model.admin.events.eventAdmin").getEventTimeinAllTimeZones(local.startDateTime,local.endDateTime,arguments.event.getValue('eventTimeZoneID',0))>
			</cfif>
			
			<cfset local.siteResourceID = local.objAdminEvent.getSiteResourceIDByEventID(siteID=arguments.event.getValue('mc_siteinfo.siteid'), eventID=val(arguments.event.getValue('eID',0)))>
			<cfset local.eventAdminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EventAdmin', siteID=arguments.event.getValue('mc_siteinfo.siteid'))>
			
			<cfset local.crossEventCustomFieldsXML = createObject("component","model.admin.common.modules.customFields.customFields").getFieldsXML(siteID=arguments.event.getValue('mc_siteinfo.siteid'),
					resourceType='EventAdmin', areaName='Event', csrid=local.eventAdminSiteResourceID, detailID=0, hideAdminOnly=0)>
			<cfset local.crossEventCustomFieldArr = xmlParse(local.crossEventCustomFieldsXML.returnXML).xmlRoot.xmlChildren>

			<!--- put cross-event custom fields and field types into array --->
			<cfset local.arrCrossEventCustomFields = []>
			<cfif arrayLen(local.crossEventCustomFieldArr)>
				<cfloop array="#local.crossEventCustomFieldArr#" index="local.thisfield">
					<cfset local.tmpAtt = local.thisfield.xmlattributes>
					<cfset local.tmpStr = { fieldID=local.tmpAtt.fieldID,
											displayTypeCode=local.tmpAtt.displayTypeCode, 
											dataTypeCode=local.tmpAtt.dataTypeCode, 
											value=arguments.event.getTrimValue('cf_#local.tmpAtt.fieldID#_','') }>
					<cfset arrayAppend(local.arrCrossEventCustomFields,local.tmpStr)>
				</cfloop>
			</cfif>

			<cfquery datasource="#application.dsn.membercentral.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
					DECLARE @timeID int, @eventID int, @orgID int, @calendarID int, @msgjson varchar(max), @defaultLanguageID int, @eventTitle varchar(200),
						@crossEventFieldUsageID int, @siteResourceID int, @eventAdminSiteResourceID int;

					SET @eventID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.strEvent.qryEventMeta.eventID#">;

					-- Get orgID and calendarID for audit logging
					SELECT @orgID = s.orgID, @defaultLanguageID = s.defaultLanguageID
					FROM dbo.sites s
					INNER JOIN dbo.ev_events e ON e.siteID = s.siteID
					WHERE e.eventID = @eventID;

					SELECT @calendarID = ce.sourceCalendarID
					FROM dbo.ev_calendarEvents ce
					WHERE ce.sourceEventID = @eventID;

					-- Get event title for audit message
					SELECT @eventTitle = ISNULL(NULLIF(ec.contentTitle, ''), 'Event ID ' + CAST(@eventID AS varchar(10)))
					FROM dbo.ev_events e
					CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) ec
					WHERE e.eventID = @eventID;

					-- Get custom field parameters for audit logging
					SELECT @crossEventFieldUsageID = dbo.fn_cf_getUsageID('EventAdmin','Event',NULL);
					SELECT @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.siteResourceID#">;
					SELECT @eventAdminSiteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.eventAdminSiteResourceID#">;

					-- Create temp audit table for content and other changes
					IF OBJECT_ID('tempdb..##tmpFrontEndEventAuditLogData') IS NOT NULL
						DROP TABLE ##tmpFrontEndEventAuditLogData;
					CREATE TABLE ##tmpFrontEndEventAuditLogData (
						[rowCode] varchar(20) PRIMARY KEY,
						[Event Title] varchar(max),
						[Event Content] varchar(max),
						[Contact Content Title] varchar(max),
						[Contact Content] varchar(max),
						[Location Content Title] varchar(max),
						[Location Content] varchar(max),
						[Cancel Content Title] varchar(max),
						[Cancel Content] varchar(max),
						[Travel Content Title] varchar(max),
						[Travel Content] varchar(max),
						[Event Start Time] varchar(max),
						[Event End Time] varchar(max),
						[All Day Event] varchar(max),
						[Categories] varchar(max),
						[Event Status] varchar(max),
						[Report Code] varchar(max),
						[Hidden From Calendar] varchar(max)
					);

					-- Add custom field columns to audit table using reusable stored procedure
					EXEC dbo.cf_populateAuditLogWithCustomFields
						@operation = 'ALTER_TABLE',
						@auditLogTableName = '##tmpFrontEndEventAuditLogData',
						@usageID = @crossEventFieldUsageID,
						@controllingSiteResourceID = @eventAdminSiteResourceID;

					INSERT INTO ##tmpFrontEndEventAuditLogData ([rowCode], [Event Title], [Event Content], [Contact Content Title], [Contact Content],
						[Location Content Title], [Location Content], [Cancel Content Title], [Cancel Content],
						[Travel Content Title], [Travel Content], [Event Start Time], [Event End Time], [All Day Event],
						[Categories], [Event Status], [Report Code], [Hidden From Calendar])
					VALUES ('DATATYPECODE', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING', 'STRING');

					-- Add custom field datatypes to DATATYPECODE row using reusable stored procedure
					EXEC dbo.cf_populateAuditLogWithCustomFields
						@operation = 'DATATYPECODE',
						@auditLogTableName = '##tmpFrontEndEventAuditLogData',
						@usageID = @crossEventFieldUsageID,
						@controllingSiteResourceID = @eventAdminSiteResourceID;

					-- Insert old values for comprehensive comparison
					INSERT INTO ##tmpFrontEndEventAuditLogData ([rowCode], [Event Title], [Event Content], [Contact Content Title], [Contact Content],
						[Location Content Title], [Location Content], [Cancel Content Title], [Cancel Content],
						[Travel Content Title], [Travel Content], [Event Start Time], [Event End Time], [All Day Event],
						[Categories], [Event Status], [Report Code], [Hidden From Calendar])
					SELECT 'OLDVAL',
						ISNULL(ec.contentTitle, ''),
						ISNULL(ec.rawContent, ''),
						ISNULL(cc.contentTitle, ''),
						ISNULL(cc.rawContent, ''),
						ISNULL(lc.contentTitle, ''),
						ISNULL(lc.rawContent, ''),
						ISNULL(cnc.contentTitle, ''),
						ISNULL(cnc.rawContent, ''),
						ISNULL(tc.contentTitle, ''),
						ISNULL(tc.rawContent, ''),
						CASE WHEN e.isAllDayEvent = 1 THEN FORMAT(t.startTime, 'M/d/yyyy') ELSE FORMAT(t.startTime, 'M/d/yyyy - h:mm tt') END,
						CASE WHEN e.isAllDayEvent = 1 THEN FORMAT(t.endTime, 'M/d/yyyy') ELSE FORMAT(t.endTime, 'M/d/yyyy - h:mm tt') END,
						CASE WHEN e.isAllDayEvent = 1 THEN 'Yes' ELSE 'No' END,
						ISNULL(categories.categoryList, ''),
						CASE e.[status] WHEN 'A' THEN 'Active' WHEN 'I' THEN 'Inactive' WHEN 'D' THEN 'Deleted' ELSE e.[status] END,
						ISNULL(e.reportCode, ''),
						CASE WHEN e.hiddenFromCalendar = 1 THEN 'Yes' ELSE 'No' END
					FROM dbo.ev_events e
					CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) ec
					CROSS APPLY dbo.fn_getContent(e.contactContentID, @defaultLanguageID) cc
					CROSS APPLY dbo.fn_getContent(e.locationContentID, @defaultLanguageID) lc
					CROSS APPLY dbo.fn_getContent(e.cancellationPolicyContentID, @defaultLanguageID) cnc
					CROSS APPLY dbo.fn_getContent(e.travelContentID, @defaultLanguageID) tc
					LEFT JOIN dbo.ev_times t ON t.eventID = e.eventID AND t.timeZoneID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eventTimeZoneID',0)#">
					OUTER APPLY (
						SELECT STUFF((SELECT ', ' + c.category
							FROM dbo.ev_eventCategories ec2
							INNER JOIN dbo.ev_categories c ON c.categoryID = ec2.categoryID
							WHERE ec2.eventID = e.eventID
							ORDER BY ec2.categoryOrder, c.category
							FOR XML PATH('')), 1, 2, '') AS categoryList
					) categories
					WHERE e.eventID = @eventID;

					-- Add custom field OLDVAL data using reusable stored procedure
					EXEC dbo.cf_populateAuditLogWithCustomFields
						@operation = 'OLDVAL',
						@auditLogTableName = '##tmpFrontEndEventAuditLogData',
						@usageID = @crossEventFieldUsageID,
						@controllingSiteResourceID = @eventAdminSiteResourceID,
						@itemID = @siteResourceID,
						@itemType = 'CrossEvent';

					BEGIN TRAN;
						-- update event details
						EXEC dbo.ev_updateEvent @eventID=@eventID,
							@eventTypeID=<cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eventTypeID')#">,
							@enteredByMemberID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.dataStruct.actionStruct.orgMemberID#">,
							@eventSubTitle=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('eventSubTitle')#">,
							<cfif local.lockTimeZoneID gt 0>
								@lockTimeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.lockTimeZoneID#">,
							<cfelse>
								@lockTimeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" null="TRUE">,
							</cfif>
							@isAllDayEvent=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('isAllDayEvent',0)#">,
							@status=<cfqueryparam cfsqltype="cf_sql_char" value="#arguments.event.getValue('status')#">,
							@reportCode=<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getTrimValue('reportCode','')#">,
							@internalNotes=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#local.qryGetNotes.internalNotes#">,
							@hiddenFromCalendar=<cfqueryparam cfsqltype="cf_sql_bit" value="#arguments.event.getValue('hiddenFromCalendar',0)#">,
							@emailContactContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailContactContent',0)#">,
							@emailLocationContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailLocationContent',0)#">,
							@emailCancelContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailCancelContent',0)#">,
							@emailTravelContent=<cfqueryparam cfsqltype="cf_sql_longvarchar" value="#arguments.event.getValue('emailTravelContent',0)#">,
							@remarketingURL=<cfqueryparam cfsqltype="cf_sql_varchar" value="#local.qryGetNotes.remarketingURL#">;
						
							EXEC dbo.ev_saveEventCategories @eventID=@eventID,
								@recordedByMemberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#session.cfcuser.memberData.memberID#">,
								@categoryIDList=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="0#arguments.event.getValue('categoryID')#">;

							EXEC dbo.ev_refreshCalendarEventsCache 
								@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;

							EXEC dbo.ev_refreshCalendarEventsCategoryIDList 
								@siteID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">,
								@eventID=@eventID;
						
						-- cross-event custom fields
						<cfif arrayLen(local.arrCrossEventCustomFields)>
							#local.objAdminEvent.updateEventCustomFields(siteResourceID=local.siteResourceID, arrCrossEventCustomFields=local.arrCrossEventCustomFields)#
						</cfif>

						UPDATE dbo.ev_events 
						SET defaultTimeID = null, lockedTimeID = null 
						WHERE eventID=@eventID;

						DELETE FROM dbo.ev_times
						WHERE eventID=@eventID;

						<cfif arguments.event.getValue('isAllDayEvent',0)>
							<cfloop array="#local.arrTimeZoneIDs#" index="local.tzid">
								EXEC dbo.ev_createTime 
								@eventID=@eventID,
								@timeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.tzID#">,
								@startTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.startDateTime#">,
								@endTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.endDateTime#">,
								@timeID=@timeID OUTPUT;
							</cfloop>
						<cfelse>
							<cfloop collection="#local.structAllEventTimes#" item="local.key">
								EXEC dbo.ev_createTime 
								@eventID=@eventID,
								@timeZoneID=<cfqueryparam cfsqltype="cf_sql_integer" value="#local.structAllEventTimes[local.key].id#">,
								@startTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.structAllEventTimes[local.key].starttime#">,
								@endTime=<cfqueryparam cfsqltype="cf_sql_timestamp" value="#local.structAllEventTimes[local.key].endtime#">,
								@timeID=@timeID OUTPUT;
							</cfloop>
						</cfif>

						EXEC dbo.cms_updateContent 
						@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eventContentID',0)#">,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('eventContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('eventContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('contactContentID',0)#">,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('contactContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('contactContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('locationContentID',0)#">,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('locationContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('locationContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('cancelContentID',0)#">,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('cancelContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('cancelContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						EXEC dbo.cms_updateContent 
						@contentID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('travelContentID',0)#">,
						@languageID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.languageID#" null="no">,
						@isHTML=<cfqueryparam cfsqltype="CF_SQL_BIT" value="1" null="no">,
						@contentTitle=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#replace(arguments.event.getTrimValue('travelContentTitle'),'&quot;',chr(34),'ALL')#" null="no">,
						@contentDesc=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="" null="no">,
						@rawcontent=<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#arguments.event.getTrimValue('travelContent')#" null="no">,
						@memberID=<cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=arguments.event.getValue('mc_siteInfo.orgID'))#" null="no">;

						-- Insert new values for comprehensive comparison and generate audit log
						INSERT INTO ##tmpFrontEndEventAuditLogData ([rowCode], [Event Title], [Event Content], [Contact Content Title], [Contact Content],
							[Location Content Title], [Location Content], [Cancel Content Title], [Cancel Content],
							[Travel Content Title], [Travel Content], [Event Start Time], [Event End Time], [All Day Event],
							[Categories], [Event Status], [Report Code], [Hidden From Calendar])
						SELECT 'NEWVAL',
							ISNULL(ec.contentTitle, ''),
							ISNULL(ec.rawContent, ''),
							ISNULL(cc.contentTitle, ''),
							ISNULL(cc.rawContent, ''),
							ISNULL(lc.contentTitle, ''),
							ISNULL(lc.rawContent, ''),
							ISNULL(cnc.contentTitle, ''),
							ISNULL(cnc.rawContent, ''),
							ISNULL(tc.contentTitle, ''),
							ISNULL(tc.rawContent, ''),
							CASE WHEN e.isAllDayEvent = 1 THEN FORMAT(t.startTime, 'M/d/yyyy') ELSE FORMAT(t.startTime, 'M/d/yyyy - h:mm tt') END,
							CASE WHEN e.isAllDayEvent = 1 THEN FORMAT(t.endTime, 'M/d/yyyy') ELSE FORMAT(t.endTime, 'M/d/yyyy - h:mm tt') END,
							CASE WHEN e.isAllDayEvent = 1 THEN 'Yes' ELSE 'No' END,
							ISNULL(categories.categoryList, ''),
							CASE e.[status] WHEN 'A' THEN 'Active' WHEN 'I' THEN 'Inactive' WHEN 'D' THEN 'Deleted' ELSE e.[status] END,
							ISNULL(e.reportCode, ''),
							CASE WHEN e.hiddenFromCalendar = 1 THEN 'Yes' ELSE 'No' END
						FROM dbo.ev_events e
						CROSS APPLY dbo.fn_getContent(e.eventContentID, @defaultLanguageID) ec
						CROSS APPLY dbo.fn_getContent(e.contactContentID, @defaultLanguageID) cc
						CROSS APPLY dbo.fn_getContent(e.locationContentID, @defaultLanguageID) lc
						CROSS APPLY dbo.fn_getContent(e.cancellationPolicyContentID, @defaultLanguageID) cnc
						CROSS APPLY dbo.fn_getContent(e.travelContentID, @defaultLanguageID) tc
						LEFT JOIN dbo.ev_times t ON t.eventID = e.eventID AND t.timeZoneID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('eventTimeZoneID',0)#">
						OUTER APPLY (
							SELECT STUFF((SELECT ', ' + c.category
								FROM dbo.ev_eventCategories ec2
								INNER JOIN dbo.ev_categories c ON c.categoryID = ec2.categoryID
								WHERE ec2.eventID = e.eventID
								ORDER BY ec2.categoryOrder, c.category
								FOR XML PATH('')), 1, 2, '') AS categoryList
						) categories
						WHERE e.eventID = @eventID;

						-- Add custom field NEWVAL data using reusable stored procedure
						EXEC dbo.cf_populateAuditLogWithCustomFields
							@operation = 'NEWVAL',
							@auditLogTableName = '##tmpFrontEndEventAuditLogData',
							@usageID = @crossEventFieldUsageID,
							@controllingSiteResourceID = @eventAdminSiteResourceID,
							@itemID = @siteResourceID,
							@itemType = 'CrossEvent';

						-- Generate audit log message for content changes
						EXEC dbo.ams_getAuditLogMsg @auditLogTable='##tmpFrontEndEventAuditLogData', @msg=@msgjson OUTPUT;

						IF ISNULL(@msgjson, '') <> '' BEGIN
							DECLARE @crlf varchar(2) = CHAR(13) + CHAR(10);
							DECLARE @evKeyMapJSON varchar(100) = '{ "EVENTID":'+CAST(@eventID AS varchar(10))+', "CALENDARID":'+CAST(@calendarID AS VARCHAR(10))+' }';
							SET @msgjson = STRING_ESCAPE('Event [' + @eventTitle + '] has been updated via front-end.', 'json') + @crlf
								+ 'The following changes have been made:' + @crlf + @msgjson;

							EXEC dbo.ev_insertAuditLog @orgID = @orgID, @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteID')#">, @areaCode = 'EVENT', @msgjson = @msgjson, @evKeyMapJSON = @evKeyMapJSON, @isImport = 0, @enteredByMemberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.dataStruct.actionStruct.orgMemberID#">;
						END

						-- Clean up temp audit table
						IF OBJECT_ID('tempdb..##tmpFrontEndEventAuditLogData') IS NOT NULL
							DROP TABLE ##tmpFrontEndEventAuditLogData;
					COMMIT TRAN;
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>

			<cfset local.processConditions = false>
			<cfset local.updateSearchText = false>

			<cfif compare(arguments.event.getValue('oldEventContentTitle',''),arguments.event.getTrimValue('eventContentTitle'))
					OR ListSort(arguments.event.getValue('oldCategoryIDList',0),'numeric','asc') NEQ ListSort(arguments.event.getValue('categoryID'),'numeric','asc')>
				<cfset local.processConditions = true>
				<cfset local.updateSearchText = true>
			<cfelseif arguments.event.getValue('oldEventStartTime','') NEQ arguments.event.getTrimValue('eventStartTime')
					OR compare(arguments.event.getValue('oldReportCode',''),arguments.event.getTrimValue('reportCode'))>
				<cfset local.processConditions = true>
			</cfif>

			<cfif NOT local.updateSearchText>
				<cfset local.arrContentCompare = [ { "key":"eventContentID", "formFieldName":"eventContent" }, { "key":"locationContentID", "formFieldName":"locationContent" }]>
				<cfloop array="#local.arrContentCompare#" index="local.thisStr">
					<cfset local.oldEventContent = application.objCMS.getStaticContent(contentID=arguments.event.getValue('#local.thisStr.key#',0), languageID=local.languageID, skipMerge=1).rawContent>
					<cfif compare(local.oldEventContent, arguments.event.getTrimValue('#local.thisStr.formFieldName#',''))>
						<cfset local.updateSearchText = true>
						<cfbreak>
					</cfif>
				</cfloop>
			</cfif>

			<cfif local.updateSearchText>
				<cfset local.objAdminEvent.queueUpdateSearchText(siteID=arguments.event.getValue('mc_siteInfo.siteID'), eventID=arguments.event.getValue('eID'))>
			</cfif>

			<!--- update any event related conditions --->
			<cfif local.processConditions>
				<cfquery name="local.qryProcessConditions" datasource="#application.dsn.membercentral.dsn#">
					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
					CREATE TABLE ##tblMCQRun (orgID int, memberID int INDEX IX_tblMCQRun_memberID, conditionID int);

					INSERT INTO ##tblMCQRun (orgID, memberID, conditionID)
					select c.orgID, null, c.conditionID
					from dbo.ams_virtualGroupConditions as c 
					where c.orgID = #arguments.event.getValue('mc_siteinfo.orgID')#
					and c.fieldCode = 'ev_entry'
					and exists (select 1 
								from dbo.ev_registrants as er 
								inner join dbo.ev_registration as r on r.registrationid = er.registrationid and er.recordedOnSiteID = r.siteID
								where r.eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('eID')#"> 
								and r.[status] = 'A'
								and er.[status] = 'A');

					EXEC platformQueue.dbo.queue_triggerMCGCache @runImmediately=0, @type='ConditionsAndGroupsChanged';

					IF OBJECT_ID('tempdb..##tblMCQRun') IS NOT NULL 
						DROP TABLE ##tblMCQRun;
				</cfquery>
			</cfif>
		</cfif>
	</cffunction>	
		
	<cffunction name="deleteEvent" access="private" output="no" returntype="void">
		<cfargument name="event" type="any">

		<cfscript>
			var local = structNew();
			local.siteID = arguments.event.getValue('mc_siteinfo.siteid');
			local.objAdminEvent = CreateObject("component","model.admin.events.event");
			local.languageID = arguments.event.getValue('mc_siteinfo.defaultLanguageID');
			local.orgMemberID = application.objUser.getOrgMemberID(cfcuser=session.cfcuser, orgID=variables.instanceSettings.orgID);

			local.strEvent = getEvent(eventid=arguments.event.getValue('eID',0),siteid=local.siteID,languageid=local.languageID);
		</cfscript>

		<cfif structKeyExists(variables.appRightsStruct,'evDeleteOwn')
				AND variables.appRightsStruct['evDeleteOwn'] is 1
				AND local.orgMemberID eq local.strEvent.qryEventMeta.enteredByMemberID>
			<cfset local.objAdminEvent.doDeleteEvent(siteID=local.siteID, eventID=local.strEvent.qryEventMeta.eventID)>
		</cfif>
	</cffunction>
		
	<cffunction name="createAppInstance" access="public" output="true" returntype="void">
		<cfargument name="Event" type="any">
		<cfargument name="baseLink" type="string">
		<cfargument name="appInfo" type="query">		
		<cfargument name="returnToAppAdmin" type="boolean" required="false" default="0">
		<cfargument name="isFromCommunity" type="boolean" required="false" default="0">

		<cfscript>
			var local = structNew();
			// SET EVENT SPECIFICATION ----------------------------------------------

			local.appInfo = arguments.appInfo;
			variables.isCommunityReady = XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isCommunityReady']/@value)");
			variables.isMultiInstanceReady = XMLSearch(local.appInfo.settingsXML,"string(//setting[@name='isMultiInstanceReady']/@value)");

			arguments.event.paramValue('appTypeID','0');
			// LOAD OBJECTS ---------------------------------------------------------
			local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
			// call the contruct to do all the page validation and form params ------
			contructAppInstanceForm(arguments.event,local.appInfo);
		</cfscript>

		<cfif cgi.request_method eq "POST" AND NOT arguments.event.getValue('error.formErrors')>
			<cftry>
				<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.createApp">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
					
						DECLARE @siteID	int, @languageID int, @sectionID int, @pageName varchar(50), @pageTitle varchar(200),
							@pagedesc varchar(400), @zoneID int, @pageTemplateID int, @pageModeID int, @pgResourceTypeID int,
							@pgParentResourceID int, @allowReturnAfterLogin bit, @applicationInstanceName varchar(100),
							@applicationInstanceDesc varchar(200), @applicationInstanceID int, @siteResourceID int,
							@pageID int, @defaultGLAccountID int, @categoryName varchar(50), @categoryShortName varchar(20),
							@categoryColor varchar(15), @calendarID int, @commSRID int;
						
						SELECT @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteInfo.siteID')#">;
						SELECT @languageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('lid')#">;
						SELECT @pageName = dbo.fn_regexReplace(<cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageName')#">,'[^A-Z0-9\-]+','');
						SELECT @pageTitle = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageTitle')#">;
						SELECT @pagedesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('pageDesc')#">;
						SELECT @zoneID = dbo.fn_getZoneID('Main');
						SELECT @pageTemplateID = NULL;
						SELECT @pageModeID = <cfif arguments.event.getValue('pageModeID','0') EQ 0>NULL<cfelse><cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('pageModeID')#"></cfif>;
						SELECT @allowReturnAfterLogin = 1;
						SELECT @applicationInstanceName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceName')#">;
						SELECT @applicationInstanceDesc = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('appInstanceDesc')#">;
						SELECT @defaultGLAccountID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('defaultGLAccountID')#">;
						SELECT @categoryName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('categoryName')#">;
						SELECT @categoryShortName = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('categoryShortName')#">;
						SELECT @categoryColor = <cfqueryparam cfsqltype="cf_sql_varchar" value="#arguments.event.getValue('categoryColor')#">;
						
						<cfif variables.isCommunityReady AND arguments.event.getValue('deployToComm','0')>
							SET @commSRID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('commSRID')#">;

							SELECT @sectionID =	comm.rootSectionID 
							from dbo.comm_communities comm 
							inner join dbo.cms_applicationInstances ai on comm.applicationInstanceID = ai.applicationInstanceID
							AND ai.siteResourceID = @commSRID
							AND ai.siteID = @siteID;

							SELECT @pgResourceTypeID = dbo.fn_getResourceTypeID('ApplicationSubPage');
							SELECT @pgParentResourceID = @commSRID;
						<cfelse>
							SELECT @sectionID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('sectionID')#">;
							SELECT @pgResourceTypeID = dbo.fn_getResourceTypeId('ApplicationCreatedPage');
							SELECT @pgParentResourceID = NULL;
						</cfif>
						
						BEGIN TRAN;
							EXEC dbo.cms_createApplicationInstanceEvents @siteid = @siteid, @languageID = @languageID, @sectionID = @sectionID,
								@pageName = @pageName, @pageTitle = @pageTitle, @pagedesc = @pagedesc, @zoneID = @zoneID,
								@pageTemplateID = @pageTemplateID, @pageModeID = @pageModeID, @pgResourceTypeID = @pgResourceTypeID,
								@pgParentResourceID = @pgParentResourceID, @allowReturnAfterLogin = @allowReturnAfterLogin,
								@defaultGLAccountID = @defaultGLAccountID, @categoryName = @categoryName, @categoryShortName = @categoryShortName,
								@categoryColor = @categoryColor, @applicationInstanceName = @applicationInstanceName,
								@applicationInstanceDesc = @applicationInstanceDesc, @applicationInstanceID = @applicationInstanceID OUTPUT,
								@siteResourceID = @siteResourceID OUTPUT, @calendarID = @calendarID OUTPUT, @pageID = @pageID OUTPUT;

							IF @commSRID IS NOT NULL BEGIN
								DECLARE @viewFunctionID int;
								SELECT @viewFunctionID = 4;

								EXEC dbo.cms_createSiteResourceRight @siteID=@siteID, @siteResourceID=@siteResourceID, @include=1, 
									@functionIDList=@viewFunctionID, @roleID=NULL, @groupID=NULL, @inheritedRightsResourceID=@commSRID, 
									@inheritedRightsFunctionID=@viewFunctionID;
							END
						COMMIT TRAN;

						SELECT @applicationInstanceID as applicationInstanceID, @siteResourceID as siteResourceID, @pageID as pageID, @calendarID as calendarID;

					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>

				<cfset local.message = 1>
			<cfcatch type="any">
				<cfset application.objError.sendError(cfcatch=cfcatch)>
				<cfset local.message = 2>
			</cfcatch>
			</cftry>

			<cfif arguments.isFromCommunity>
				<cfoutput>
					<script language="javascript">
						top.reloadCommSubPagesTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfelseif arguments.returnToAppAdmin>
				<cfoutput>
					<script language="javascript">
						top.reloadCalendarTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			<cfelse>
				<cfoutput>
					<script language="javascript">
						top.reloadPageTable();
						top.MCModalUtils.hideModal();
					</script>
				</cfoutput>
			</cfif>	
			
			
		<cfelse>
			<cfscript>
			local.allow = FALSE;
			if( local.appInfo.instancesCreated EQ 0 ) local.allow = TRUE;
			else{
				if( variables.isMultiInstanceReady AND (local.appInfo.maxInstancesPerSite - local.appInfo.instancesCreated) GT 0 ){
					local.allow = TRUE;
				}
			}
			</cfscript>

			<cfif local.allow>
				<cfscript>
				local.objAppCreation = CreateObject("component","model.admin.pages.appCreationProcess");
				local.objSection = CreateObject("component","model.system.platform.section");
				local.objPageAdmin = CreateObject("component","model.admin.pages.pageAdmin");
				local.qryCommunities = local.objAppCreation.getCommunities(arguments.event.getValue('mc_siteInfo.siteID'));
				local.getSections = local.objSection.getRecursiveSections(siteID=arguments.event.getValue('mc_siteInfo.siteID'));
				local.qryModes = local.objPageAdmin.getAvailableModes();
				local.qryLanguages = local.objPageAdmin.getAvailableLanguages();
				if( local.appInfo.recordCount ) variables.allowPageNameChange = local.appInfo.allowPageNameChange;
				local.isFromCommunity = int(val(arguments.event.getValue('isFromCommunity',0)));

				// gl account selection is empty by default
				arguments.event.setValue('defaultGLAccountID',0);
				arguments.event.setValue('defaultGLAccountPath','');
				
				// Setup GL Account Widget for Revenue GL 
				local.strRevenueGLAcctWidgetData = {
					"label": "GL Account for New Events",
					"btnTxt": "Choose GL Account",
					"glatid": 3,
					"widgetMode": "GLSelector",
					"idFldName": "DefaultGLAccountID",
					"idFldValue": val(arguments.event.getValue('defaultGLAccountID')),
					"pathFldValue": arguments.event.getValue('DefaultGLAccountPath',''),
					"pathNoneTxt": "(No account selected)"
				};
				local.strRevenueGLAcctWidget = CreateObject("component", "model.admin.common.modules.glAccountWidget.glAccountWidget").renderWidget(strWidgetData=local.strRevenueGLAcctWidgetData);

				</cfscript>
				
				<cfinclude template="frm_createAppInstance.cfm">
			<cfelse>
				<cfoutput>
					<div class="alert alert-warning">
						<h4>Unable to add #local.appInfo.applicationTypeName#</h4>
						<p>You may not add this application to your website at this time.</p>
					</div>
				</cfoutput>
			</cfif>
		</cfif>
	</cffunction>

	<cffunction name="printReg" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.objAdminEventReg = CreateObject("component","model.admin.events.eventReg")>
		<cfset local.registrantID = arguments.event.getValue('registrantID',0)>
		
		<cfset local.strEmailConfirmation = local.objAdminEventReg.generateConfirmationEmail(registrantID=local.registrantID, emailMode="registrantprint", siteid=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfif arguments.event.getValue('pdf',0) is 1>
			<cfdocument format="PDF" saveAsName="EventRegistration.pdf" margintop="1" marginbottom="1" marginright="1" marginleft="1" backgroundvisible="Yes" scale="100">
				<cfoutput>
				<html>
				<head>
					<link rel="stylesheet" type="text/css" href="#replaceNoCase(application.paths.internalPlatform.url,'*SITECODE*',arguments.event.getValue('mc_siteinfo.sitecode'))#/assets/admin/css/styles.css" />
					<style>
						body { margin:0px !important; width:8in !important; }
					</style>
				</head>
				<body>
					#local.strEmailConfirmation.emailcontent#
				</body>
				</html>
				</cfoutput>
			</cfdocument>
		<cfelse>
			<cfsavecontent variable="local.js">
				<cfoutput>
				<script language="javascript">
					function printIt() {
						self.location.href = '#arguments.event.getValue('mainurl')#&evAction=printReg&eID=#arguments.event.getValue('eID')#&mid=#arguments.event.getValue('mid')#&registrantID=#local.registrantID#&pdf=1';
					}
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfhtmlhead text="#application.objCommon.minText(local.js)#">

			<cfset local.data = local.strEmailConfirmation.emailcontent>
		</cfif>
		
		<cfreturn local.data>
	</cffunction>	

	<cffunction name="eventDocDownload" access="private" output="false" returntype="struct" hint="for direct document download links embed in emails, etc">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = {};
			local.downloadRequest = processEventDocDownloadRequest(
				siteID=variables.instanceSettings.siteID,
				eid=arguments.event.getValue('eid'),
				documentID=arguments.event.getValue('documentID')
			);
			if (local.downloadRequest.success and len(local.downloadRequest.stDownloadURL))
				//redirect browser to download file
				location(url="/tsdd/#local.downloadRequest.stDownloadURL#", addtoken=false);
			else if (local.downloadRequest.message eq "Access Not Allowed")
				location(url="#arguments.event.getValue('mainurl')#&evAction=showDetail&eid=#arguments.event.getValue('eid')#", addtoken=false);
			return returnAppStruct(local.downloadRequest.message,"echo");
		</cfscript>
	</cffunction>

	<cffunction name="getEventDocumentDownloadLink" access="public" output="false" returntype="struct" hint="called via ajax to get download link">
		<cfargument name="mcproxy_siteID" type="numeric" required="true">
		<cfargument name="eid" required="yes" type="numeric" default=0>
		<cfargument name="documentID" required="yes" type="numeric" default=0>
		<cfscript>
			var local = {};
			local.returnStruct = { "success":false, "stDownloadURL":"", errMsg="" };
			local.downloadRequest = processEventDocDownloadRequest(
				siteID=arguments.mcproxy_siteID,
				eid=arguments.eid,
				documentID=arguments.documentID
			);
			if (local.downloadRequest.success and len(local.downloadRequest.stDownloadURL)) {
				//redirect browser to download file
				local.returnStruct.success = true;
				local.returnStruct.stDownloadURL = '/tsdd/' & local.downloadRequest.stDownloadURL;
			} else {
				local.returnStruct.success = false;
				local.returnStruct.errMsg = local.downloadRequest.message;
			}
			return local.returnStruct;
		</cfscript>
	</cffunction>

	<cffunction name="processEventDocDownloadRequest" access="private" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="eid" required="yes" type="numeric" default=0>
		<cfargument name="documentID" required="yes" type="numeric" default=0>

		<cfscript>
			var local = structNew();
			local.returnStruct = {
				"success": false,
				"message":"",
				"stDownloadURL":""
			};
			local.objAdminEvent = CreateObject("component","model.admin.events.event");
			local.strEvent = getEvent(eventid=arguments.eid,siteid=arguments.siteid,languageid=session.mcstruct.languageID);
		</cfscript>
		
		<cfif val(local.strEvent.qryEventRegMeta.registrationid) is 0>
			<cfset local.returnStruct.success = false>
			<cfset local.returnStruct.message = "Registration not found">
		<cfelse>
			<cfset local.objDocument = CreateObject("component","model.system.platform.document")>
			<cfset local.documentID = int(val(arguments.documentID))>
			<!--- get document and rights --->
			<!--- stop if the documentID doesnt belong to the site --->
			<cfset local.getDocument = local.objDocument.getDocumentData(local.documentID,0,0,'en')>
			<cfif local.getDocument.siteResourceID gt 0>
				<!--- this supports MemberKey --->
				<cfset local.memberID = application.objUser.getIdentifiedMemberID(cfcuser=session.cfcuser, orgID=local.getDocument.orgID)>
		
				<cfif session.mcstruct.sitecode eq local.getDocument.siteCode>
					<cfquery name="local.getDocumentRights" datasource="#application.dsn.memberCentral.dsn#">						
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						select ed.documentID,ed.eventID
						from dbo.ev_registrants as evr
						inner join dbo.ev_registration as r on r.registrationID = evr.registrationID 
							and evr.recordedOnSiteID = r.siteID
							and evr.status = <cfqueryparam cfsqltype="cf_sql_varchar" value="A">
							and r.eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eID#">
						inner join ev_eventDocuments as ed on ed.eventID = r.eventID AND ed.documentID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.documentID#">
						inner join dbo.ams_members as m on m.memberid = evr.memberid
							and m.activeMemberID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberID#">;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
					
					<cfif local.getDocumentRights.recordCount>
						<cfset local.allowed = 1>
					<cfelse>
						<cfset local.allowed = 0>
					</cfif>
				<cfelse>
					<cfset local.allowed = 0>
				</cfif>
			<cfelse>
				<cfset local.allowed = 0>
			</cfif>

			<cfif not local.allowed>
				<cfset local.returnStruct.success = false>
				<cfset local.returnStruct.message = "Access Not Allowed">
			<cfelse>
				<cftry>
					<cfset application.objPlatformStats.recordDocHit(documentVersionID=local.getdocument.documentVersionID,allowed=local.allowed)>
					<cfcatch type="any">
						<cfset application.objError.sendError(cfcatch=cfcatch,objectToDump=local)>
					</cfcatch>
				</cftry>
				<cfset local.theFile = "#application.paths.RAIDSiteDocuments.path##local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#">
				<cfset local.keyMod = numberFormat(local.getdocument.documentVersionID mod 1000,"0000")>
				<cfset local.objectKey = lcase("sitedocuments/#local.getDocument.orgCode#/#local.getDocument.siteCode#/#local.keyMod#/#local.getdocument.documentVersionID#.#local.getDocument.fileExt#")>
				<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath=local.theFile, displayName=local.getDocument.fileName, s3bucket="membercentralcdn", s3objectKey=local.objectKey, checkLocalFirst=1)>
				<cfif len(local.stDownloadURL)>
					<cfset local.returnStruct.success = true>
					<cfset local.returnStruct.stDownloadURL = local.stDownloadURL>
					<cfset local.returnStruct.message = "Success">
				<cfelse>
					<cfset local.returnStruct.success = false>
					<cfset local.returnStruct.message = "There was a problem preparing the file for download">
				</cfif>
			</cfif>
		</cfif>
		
		<cfreturn local.returnStruct>
	</cffunction>

	
	<cffunction name="getCalendarSettings" access="public" output="false" returntype="struct">
		<cfargument name="applicationInstanceID" type="numeric" required="yes"/>
		
		<cfset var local = structNew()>
		
		<cfset local.instanceSettings = getInstanceSettings(arguments.applicationInstanceID)>

		<cfquery name="local.getCalSettings" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.instanceSettings.siteID#">;

			select cal.calendarID, cal.showAddCalendarLinks, cal.allowWebCalSubscriptions, cal.alwaysShowEventTimezone
			from dbo.ev_calendars as cal
			where cal.siteID = @siteID
			and cal.applicationInstanceID = <cfqueryparam cfsqltype="cf_sql_integer" value="#local.instanceSettings.applicationInstanceID#">;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.instanceSettings["calendarID"] = local.getCalSettings.calendarID>
		<cfset local.instanceSettings["showAddCalendarLinks"] = local.getCalSettings.showAddCalendarLinks>
		<cfset local.instanceSettings["allowWebCalSubscriptions"] = local.getCalSettings.allowWebCalSubscriptions>
		<cfset local.instanceSettings["alwaysShowEventTimezone"] = local.getCalSettings.alwaysShowEventTimezone>
		
		<cfreturn local.instanceSettings>
	</cffunction>
	
	<cffunction name="getRegListForRoster" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="mcproxy_siteID" type="numeric" required="yes">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="posStart" type="numeric" required="yes">
		
		<cfscript>
			var local = structNew();
			
			local.returnStruct = structNew();
			local.returnStruct.success = true;
			local.returnStruct.data = arrayNew(1);
			local.returnStruct.totalCount = 0;
		</cfscript>
		
		<!--- get siteresouceID used by roster by looking at home calendar. --->
		<cfquery name="local.qryGetSRID" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @siteID int = <cfqueryparam value="#arguments.mcproxy_siteID#" cfsqltype="CF_SQL_INTEGER">;

			SELECT ai.siteResourceID
			FROM dbo.ev_events as e
			INNER JOIN dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID
				and ce.calendarID = ce.sourceCalendarID
			INNER JOIN dbo.ev_calendars as c ON c.siteID = @siteID and c.calendarID = ce.calendarID
			INNER JOIN dbo.cms_applicationInstances as ai on ai.siteID = @siteID and ai.applicationInstanceID = c.applicationInstanceID
			WHERE e.eventID = <cfqueryparam value="#arguments.eventID#" cfsqltype="CF_SQL_INTEGER">
			AND e.siteID = @siteID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		<cfset local.resultFieldSet = getLocatorFieldsetID(siteResourceID=local.qryGetSRID.siteResourceID, area='roster')>
		
		<cfset local.AppearInRealTimeRosterRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="Events", functionName="AppearInRealTimeRoster")>
		<cfquery name="local.qryMembers" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpReg') IS NOT NULL 
				DROP TABLE ##tmpReg;
			IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
				DROP TABLE ##tmpMembersFS;
			CREATE TABLE ##tmpReg (memberID int PRIMARY KEY, lastname varchar(75), firstname varchar(75), row int);
			CREATE TABLE ##tmpMembersFS (MFSAutoID int IDENTITY(1,1) NOT NULL);
			
			DECLARE @orgID int, @siteID int, @eventID int, @totalCount int, @functionID int, @siteResourceID int, 
				@posStart int, @posStartAndCount int, @outputFieldsXML xml;
			SET @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
			SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_siteID#">;
			SET @eventID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">;
			SET @functionID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.AppearInRealTimeRosterRFID#">;
			SET @siteResourceID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryGetSRID.siteResourceID#">;
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.posStart#"> + 1;
			SET @posStartAndCount = (@posStart + 25) - 1;
			
			INSERT INTO ##tmpReg (memberID, lastname, firstname, row)
			SELECT tmp.memberID, tmp.lastname, tmp.firstname, min(row) as row
			FROM (
				SELECT mActive.memberID, mActive.lastname, mActive.firstname, 
					ROW_NUMBER() OVER (ORDER BY mActive.lastname, mActive.firstname) as row
				FROM dbo.ev_registrants as r
				INNER JOIN dbo.ev_registration as rn ON rn.registrationID = r.registrationID and rn.siteID = @siteID AND rn.status = 'A' 
				INNER JOIN dbo.ev_events as e on e.eventID = rn.eventID AND e.siteID = @siteID
				INNER JOIN dbo.ams_members as m ON m.orgID = @orgID and m.memberID = r.memberID
				INNER JOIN dbo.ams_members as mActive ON mActive.orgID = @orgID and mActive.memberID = m.activeMemberID
				INNER JOIN dbo.cache_perms_groupPrints as gp on mActive.groupPrintID = gp.groupPrintID
				INNER JOIN dbo.cache_perms_siteResourceFunctionRightPrints as srfrp on srfrp.siteID = @siteID
					and srfrp.siteResourceID = @siteResourceID
					AND srfrp.functionID = @functionID
				INNER JOIN dbo.cache_perms_groupPrintsRightPrints as gprp on gprp.siteID = @siteID
					AND gp.groupPrintID = gprp.groupPrintID
					AND gprp.rightPrintID = srfrp.rightPrintID
				WHERE rn.eventID = @eventID
				AND r.status = 'A'
				AND mActive.status <> 'D'
			) as tmp
			GROUP BY tmp.memberID, tmp.lastname, tmp.firstname
			ORDER BY row;
			
			SET @totalCount = @@rowcount;

			DELETE FROM ##tmpReg
			WHERE row NOT BETWEEN @posStart and @posStartAndCount;

			-- get fieldset data and set back to snapshot because proc ends in read committed
			EXEC dbo.ams_getMemberDataByFieldSets @orgID=@orgID, 
				@fieldsetIDList=<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#val(local.resultFieldSet.fieldsetid)#">,
				@existingFields='m_lastname,m_firstname', @ovNameFormat=NULL, @ovMaskEmails=NULL, 
				@membersTableName='##tmpReg', @membersResultTableName='##tmpMembersFS', @linkedMembers=0, 
				@mode='view', @outputFieldsXML=@outputFieldsXML OUTPUT;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select tmp.row, tmp.lastname, tmp.firstname, m.*, @totalCount as totalCount,
				case when tmp.row = @posStart then @outputFieldsXML else null end AS mc_outputFieldsXML
			from ##tmpReg as tmp
			inner join ##tmpMembersFS as m on m.memberID = tmp.memberID
			order by tmp.row;

			IF OBJECT_ID('tempdb..##tmpReg') IS NOT NULL 
				DROP TABLE ##tmpReg;
			IF OBJECT_ID('tempdb..##tmpMembersFS') IS NOT NULL
				DROP TABLE ##tmpMembersFS;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.returnStruct.totalCount = val(local.qryMembers.totalCount)>
		<cfif local.returnStruct.totalCount>
			<cfset local.xmlResultFields = local.qryMembers.mc_outputFieldsXML[1]>
			<cfif len(local.xmlResultFields)>
				<cfset local.xmlResultFields = XmlParse(local.xmlResultFields)>
				<cfset local.qryOutputFields = CreateObject("component","model.system.platform.memberFieldsets").getOutputFieldsFromXML(outputFieldsXML=local.xmlResultFields)>

				<cfset local.orgAddressTypes = application.objOrgInfo.getOrgAddressTypes(orgID=arguments.mcproxy_orgID, includeTags=1)>
				<cfset local.strOrgAddressTypes = structNew()>
				<cfloop query="local.orgAddressTypes">
					<cfif local.orgAddressTypes.isTag is 1>
						<cfset local.strOrgAddressTypes["t#local.orgAddressTypes.addressTypeID#"] = local.orgAddressTypes.addressType>
					<cfelse>
						<cfset local.strOrgAddressTypes[local.orgAddressTypes.addressTypeID] = local.orgAddressTypes.addressType>
					</cfif>
				</cfloop>
				<cfset local.mc_combinedAddresses = structNew()>
				<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,3)='ma_' or substring(@fieldCode,1,3)='mp_' or substring(@fieldCode,1,4)='mat_' or substring(@fieldCode,1,4)='mpt_']")>
				<cfloop array="#local.tmp#" index="local.thisField">
					<cfset local.thisATID = listGetAt(local.thisField.xmlattributes.fieldcode,2,"_")>
					<cfif listFindNoCase("mat,mpt",listGetAt(local.thisField.xmlattributes.fieldcode,1,"_"))>
						<cfset local.strKey = "t#local.thisATID#">
					<cfelse>
						<cfset local.strKey = local.thisATID>
					</cfif>
					<cfif NOT StructKeyExists(local.mc_combinedAddresses,local.strKey)>
						<cfset local.mc_combinedAddresses[local.strKey] = { addr='', type=local.strOrgAddressTypes[local.strKey] } >
					</cfif>
				</cfloop>

				<!--- remove fields from qryOutputFields that are handled manually --->
				<cfquery name="local.qryOutputFieldsForLoop" dbtype="query">
					SELECT *
					FROM [local].qryOutputFields
					WHERE fieldcodeSect NOT IN ('mc','ma','mat','mp','mpt')
					AND fieldCode NOT IN ('m_recordtypeid','m_status','m_membertypeid','m_suffix','m_professionalsuffix','m_prefix','m_firstname','m_middlename','m_lastname',)
					AND fieldCode NOT LIKE 'acct_balance_%'
				</cfquery>
			</cfif>

			<cfloop query="local.qryMembers">

				<cfif isXML(local.xmlResultFields)>
					<cfset local.mc_combinedName = local.qryMembers['Extended Name'][local.qryMembers.currentrow]>

					<!--- combine address fields if there are any --->
					<cfset local.thisMem_mc_combinedAddresses = duplicate(local.mc_combinedAddresses)>
					<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
						<cfsavecontent variable="local.thisATFull">
							<cfoutput>
							<cfif left(local.thisATID,1) eq "t">
								<cfset local.MAfcPrefix = "mat_#right(local.thisATID,len(local.thisATID)-1)#_">
								<cfset local.MPfcPrefix = "mpt_#right(local.thisATID,len(local.thisATID)-1)#_">
							<cfelse>
								<cfset local.MAfcPrefix = "ma_#local.thisATID#_">
								<cfset local.MPfcPrefix = "mp_#local.thisATID#_">
							</cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address1']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address2']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/> </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#address3']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#city']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]# </cfif>
							<cfset local.tmp2 = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#stateprov']")>
							<cfif arrayLen(local.tmp2) is 1 and len(local.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>, #local.qryMembers[local.tmp2[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]# </cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#postalcode']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])> #local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#county']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])>#local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]# County<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[@fieldCode='#local.MAfcPrefix#country']")>
							<cfif arrayLen(local.tmp) is 1 and len(local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow])> #local.qryMembers[local.tmp[1].xmlAttributes.FieldLabel][local.qryMembers.currentrow]#<br/></cfif>
							<cfset local.tmp = XMLSearch(local.xmlResultFields,"//field[substring(@fieldCode,1,#len(local.MPfcPrefix)#)='#local.MPfcPrefix#']")>
							<cfloop array="#local.tmp#" index="local.thisPT">
								<cfif len(local.qryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryMembers.currentrow])>
									<div>#local.thisPT.xmlAttributes.FieldLabel#: #local.qryMembers[local.thisPT.xmlAttributes.FieldLabel][local.qryMembers.currentrow]#</div>
								</cfif>
							</cfloop>
							</cfoutput>
						</cfsavecontent>
						<cfset local.thisATfull = trim(replace(replace(local.thisATFull,'  ',' ','ALL'),' ,',',','ALL'))>
						<cfif left(local.thisATfull,2) eq ", ">
							<cfset local.thisATfull = right(local.thisATfull,len(local.thisATfull)-2)>
						</cfif>
						<cfif len(local.thisATfull)>
							<cfset local.thisMem_mc_combinedAddresses[local.thisATID]['addr'] = local.thisATfull>
						<cfelse>
							<cfset structDelete(local.thisMem_mc_combinedAddresses,local.thisATID,false)>
						</cfif>
					</cfloop>
				<cfelse>
					<cfset local.mc_combinedName = "#local.qryMembers.lastname#, #local.qryMembers.firstname#">
					<cfset local.thisMem_mc_combinedAddresses = structNew()>
				</cfif>

				<cfsavecontent variable="local.tmpDataHolder">
					<cfoutput>
					<div style="width:100%;">
						<b>#local.mc_combinedName#</b><br/>
						<cfif StructCount(local.thisMem_mc_combinedAddresses)>
							<cfloop collection="#local.thisMem_mc_combinedAddresses#" item="local.thisATID">
								<div style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
									#local.thisMem_mc_combinedAddresses[local.thisATID]['type']#: #local.thisMem_mc_combinedAddresses[local.thisATID]['addr']#
								</div>
							</cfloop>
						</cfif>
						<cfif isXML(local.xmlResultFields) AND local.qryOutputFieldsForLoop.recordCount>
							<cfloop query="local.qryOutputFieldsForLoop">
								<cfset local.currValue = local.qryMembers[local.qryOutputFieldsForLoop.fieldLabel][local.qryMembers.currentrow]>
								<cfif len(local.currValue)>
									<div style="white-space:nowrap;overflow:hidden;text-overflow: ellipsis;">
										#htmlEditFormat(local.qryOutputFieldsForLoop.fieldLabel)#: 
										<cfif left(local.qryOutputFieldsForLoop.fieldCode,13) eq 'acct_balance_'>
											#dollarFormat(local.currValue)#
										<cfelse>
											<cfswitch expression="#local.qryOutputFieldsForLoop.dataTypeCode#">
												<cfcase value="DATE">
													#dateFormat(local.currValue,"m/d/yyyy")#
												</cfcase>
												<cfcase value="STRING,DECIMAL2,INTEGER">
													#local.currValue#
												</cfcase>
												<cfcase value="BIT">
													#YesNoFormat(local.currValue)#
												</cfcase>
											</cfswitch>
										</cfif>
									</div>
								</cfif>
							</cfloop>
						</cfif>
					</div>
					</cfoutput>
				</cfsavecontent>
				
				<cfset arrayAppend(local.returnStruct.data, application.objCommon.minText(local.tmpDataHolder))>
			</cfloop>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="getLinkToEvent" access="public" output="false" returntype="string">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="siteID" type="numeric" required="yes">
		<cfargument name="mainHostName" type="string" required="yes">

		<cfset var local = structNew()>

		<cfset local.strEvent = getEvent(eventid=arguments.eventID, siteid=arguments.siteID, languageid=1)>

		<cfif local.strEvent.qryEventMeta.recordcount is 0> 
			<cfset local.pageLink = "/?pg=404">
		<cfelse>
			<cfset local.pageLink = "/?#application.objApplications.getAppBaseLink(applicationInstanceID=local.strEvent.qryEventMeta.calendarApplicationInstanceID, siteID=local.strEvent.qryEventMeta.siteID)#&evAction=showDetail&eid=#local.strEvent.qryEventMeta.eventID#">
		</cfif>

		<cfreturn local.pageLink>
	</cffunction>

	<cffunction name="getStructuredEventData" access="private" output="false" returntype="struct">
		<cfargument name="strEvent" type="struct" required="true">
		<cfargument name="strEventHost" type="struct" required="true">
		<cfargument name="mode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.featuredImagesObj = createObject("component","model.admin.common.modules.featuredImages.featuredImages")>

		<cfset local.description = "">
		<cfif len(arguments.strEvent.qryEventMeta.eventContent)>
			<cfset local.evDescSanitizeOptions = {
						"allowedTags": [],
						"allowedAttributes": {}
					}>
			<cfset local.sanitizeEVDescResponse = application.objCommon.sanitizeHTML(dirtyHTML=trim(arguments.strEvent.qryEventMeta.eventContent), sanitizeOptions=local.evDescSanitizeOptions)>
			<cfif local.sanitizeEVDescResponse.success>
				<cfset local.description = local.sanitizeEVDescResponse.content>
			</cfif>
		</cfif>
		<cfset local.qryFeaturedImageDetails = local.featuredImagesObj.getFeaturedImageDetails(referenceID=arguments.strEvent.qryEventMeta.eventID, referenceType='eventsList')>
		<cfif local.qryFeaturedImageDetails.recordcount is 0>
			<cfset local.qryFeaturedImageDetails = local.featuredImagesObj.getFeaturedImageDetails(referenceID=arguments.strEvent.qryEventMeta.calendarID, referenceType='defaultCalendarEvent')>
		</cfif>
		<cfset local.orgSiteFolder = LCASE("#variables.instancesettings.orgcode#/#variables.instancesettings.sitecode#")>
		<cfif local.qryFeaturedImageDetails.recordcount>
			<cfset local.imageURL = "#arguments.strEventHost.scheme#://#arguments.strEventHost.mainhostname#/userassets/#local.orgSiteFolder#/featuredimages/originals/#local.qryFeaturedImageDetails.featureImageID#.#local.qryFeaturedImageDetails.fileExtension#">
		</cfif>
		<cfset local.eventDetailURL = "#arguments.strEventHost.scheme#://#arguments.strEventHost.mainhostname#/#arguments.strEventHost.mainurl#&eid=#arguments.strEvent.qryEventMeta.eventID#&evAction=showDetail">
		<cfset local.eventRegURL = "#arguments.strEventHost.scheme#://#arguments.strEventHost.mainhostname#/#arguments.strEventHost.mainregurl#">

		<!--- set location key. We dont have structured location address data, so just use text. google wont like it but it is valid. --->
		<cfif val(arguments.strEvent.qryEventRegMeta.isOnlineMeeting)>
			<cfset local.location = {
					"@type": "VirtualLocation",
					"url": local.eventDetailURL
				}>
			<cfset local.eventAttendanceMode = "https://schema.org/OnlineEventAttendanceMode">
		<cfelse>
			<!--- Setting as virtual location object for non-online events until we have an actual address in events --->
			<cfset local.location = {
					"@type": "VirtualLocation",
					"url": local.eventDetailURL
				}>
			<cfset local.eventAttendanceMode = "https://schema.org/MixedEventAttendanceMode">
		</cfif>

		<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>
		<cfset local.selectedTimeZoneCode = local.objTSTZ.getTZCodeFromTZID(arguments.strEvent.qryEventTimes_selected.timezoneid)>
		<cfif arguments.strEvent.qryEventMeta.isAllDayEvent>
			<cfset local.startDate = DateFormat(arguments.strEvent.qryEventTimes_selected.startTime,"yyyy-mm-dd")>
			<cfset local.endDate = DateFormat(arguments.strEvent.qryEventTimes_selected.endTime,"yyyy-mm-dd")>
		<cfelse>
			<cfset local.utcStart = local.objTSTZ.convertTimeZone(arguments.strEvent.qryEventTimes_selected.startTime,local.selectedTimeZoneCode,"UTC")>
			<cfset local.utcEnd = local.objTSTZ.convertTimeZone(arguments.strEvent.qryEventTimes_selected.endTime,local.selectedTimeZoneCode,"UTC")>
			<cfset local.startDate = DateTimeFormat(local.utcStart,"ISO8601",local.selectedTimeZoneCode)>
			<cfset local.endDate = DateTimeFormat(local.utcEnd,"ISO8601",local.selectedTimeZoneCode)>
		</cfif>

		<cfset local.strEventData = {
				"@context": "http://schema.org",
				"@type": "Event",
				"name": arguments.strEvent.qryEventMeta.eventContentTitle,
				"eventAttendanceMode": local.eventAttendanceMode,
				"eventStatus": "https://schema.org/EventScheduled",
				"url": local.eventDetailURL,
				"description": local.description,
				"location": local.location,
				"identifier": arguments.strEvent.qryEventMeta.reportCode,
				"startDate": local.startDate,
				"endDate": local.endDate,
				"organizer": {
					"@type": "Organization",
					"name": arguments.strEventHost.sitename,
					"url": "#arguments.strEventHost.scheme#://#arguments.strEventHost.mainhostname#"
				}
			}>
		<cfif local.qryFeaturedImageDetails.recordcount>
			<cfset local.strEventData["image"] = [local.imageURL]>
		</cfif>

		<cfif arguments.strEvent.qryEventRegMeta.recordcount gt 0>
			<cfset local.regStartDate = DateTimeFormat(arguments.strEvent.qryEventRegMeta.startDate,"ISO8601",local.selectedTimeZoneCode)>
			<cfset local.regEndDate = DateTimeFormat(arguments.strEvent.qryEventRegMeta.enddate,"ISO8601",local.selectedTimeZoneCode)>
			<cfset local.qryEventRegRates = CreateObject("component","eventRegV2").getRegRates(regid=arguments.strEvent.qryEventRegMeta.registrationid, mid=0, showall=true)>
			<cfif local.qryEventRegRates.recordcount>
				<cfset local.strEventData['offers'] = arrayNew(1)>
				<cfloop query="local.qryEventRegRates">
					<cfset local.strEventData['offers'].append(
							{
								"@type": "Offer",
								"url": local.eventRegURL,
								"name": local.qryEventRegRates.rateName,
								"category": local.qryEventRegRates.rateGrouping,
								"price": local.qryEventRegRates.rate,
								"priceCurrency": arguments.strEventHost.currencyType,
								"availability": "https://schema.org/InStock",
								"validFrom": local.regStartDate,
								"validThrough": local.regEndDate
							}
						)>
				</cfloop>
			</cfif>

			<cfif val(arguments.strEvent.qryEventRegMeta.registrantCap) gt 0>
				<cfset local.strEventData['maximumAttendeeCapacity'] = arguments.strEvent.qryEventRegMeta.registrantCap>
				<cfif val(arguments.strEvent.qryEventRegMeta.regCapReached) eq 1>
					<cfset local.strEventData['remainingAttendeeCapacity'] = 0>
				</cfif>
			</cfif>
		</cfif>

		<cfreturn local.strEventData>
	</cffunction>
	
	<cffunction name="processCalendarMergeCodes" access="private" output="false" returntype="string">
		<cfargument name="event" type="any">
		<cfargument name="eventContent" type="string" required="yes">
		<cfargument name="regId" type="numeric" required="yes">
		
		<cfquery name="local.qryRegistrantDetails" datasource="#application.dsn.membercentral.dsn#">
			SELECT m.memberID
			FROM dbo.ev_registrants as r
			INNER JOIN dbo.ams_members as m on m.memberID = r.memberID
			WHERE r.registrantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.regId#">
			AND r.recordedOnSiteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.siteID')#">
			AND r.status = 'A'
		</cfquery>
		<cfif local.qryRegistrantDetails.recordcount gt 0>
			<cfset local.qryRegistrant = CreateObject("component","eventRegV2").getRegistrantInfo(local.qryRegistrantDetails.memberID)>
			<cfset local.qryMemberFields = application.objMergeCodes.getMergeViewFields(orgID=arguments.event.getValue('mc_siteinfo.orgID'), memberID=local.qryRegistrantDetails.memberID, content=arguments.eventContent)>

			<cfif application.MCEnvironment eq "production">
				<cfset local.thisHostname = arguments.event.getValue('mc_siteinfo.mainHostName')>
			<cfelse>
				<cfset local.thisHostname = application.objPlatform.getCurrentHostname()>
			</cfif>

			<cfset local.tempMemberData = { memberid=local.qryRegistrant.memberID, firstName=local.qryRegistrant.FirstName, 
											middleName=local.qryRegistrant.MiddleName, lastName=local.qryRegistrant.LastName, 
											company=local.qryRegistrant.Company, suffix=local.qryRegistrant.Suffix, 
											professionalsuffix=local.qryRegistrant.professionalsuffix, 
											prefix=local.qryRegistrant.Prefix, membernumber=local.qryRegistrant.membernumber, 
											orgcode=local.qryRegistrant.orgcode, siteID=arguments.event.getValue('mc_siteinfo.siteID'),
											hostname=local.thisHostname, useRemoteLogin=arguments.event.getValue('mc_siteinfo.useRemoteLogin') }>
			
			<cfloop array="#getMetaData(local.qryMemberFields)#" index="local.thisColumn">
				<cfif NOT StructKeyExists(local.tempMemberData,local.thisColumn.Name)>
					<cfset structInsert(local.tempMemberData,local.thisColumn.Name,local.qryMemberFields[local.thisColumn.Name][1],true)>
				</cfif>
			</cfloop>

			<cfset local.tempEventData = CreateObject("component","model.admin.events.event").getRegistrantDataForEmailing(registrantID=arguments.regId, recipientType="Registrants", mode="calendar").eventdata>
			<cfset local.strArgs = { content=arguments.eventContent, memberdata=local.tempMemberData, eventdata=local.tempEventData, orgcode=arguments.event.getValue('mc_siteinfo.orgcode'), sitecode=arguments.event.getValue('mc_siteInfo.sitecode') }>
			
			<cfset local.eventContent = application.objMergeCodes.processMergeCodes(argumentcollection=local.strArgs).content>
		
			<cfreturn local.eventContent>
		<cfelse>
			<cfreturn 'This registration no longer exists.'>
		</cfif>
	</cffunction>

	<cffunction name="insertEvaluationFormResponse" access="public" output="false" returntype="void">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="formID" type="numeric" required="yes">
		<cfargument name="responseID" type="numeric" required="yes">
		
		<cfset var qryInsertResponse = "">

		<cfquery name="qryInsertResponse" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
	
				DECLARE @eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">,
					@formID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">,
					@registrantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">,
					@responseID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.responseID#">,
					@loadPoint varchar(15) = 'evaluation',
					@eventFormID int;
	
				SELECT @eventFormID = eventFormID
				FROM dbo.ev_eventsAndForms
				WHERE eventID = @eventID
				AND formID = @formID
				AND loadPoint = @loadPoint;
	
				BEGIN TRAN;
					-- inactivate prev responses
					UPDATE fbr
					SET fbr.isActive = 0
					FROM dbo.ev_eventsAndFormResponses AS r
					INNER JOIN formBuilder.dbo.tblResponses AS fbr ON fbr.responseID = r.responseID
						AND fbr.isActive = 1
					WHERE r.registrantID = @registrantID
					AND r.eventFormID = @eventFormID;
	
					INSERT INTO dbo.ev_eventsAndFormResponses (eventFormID, registrantID, responseID)
					VALUES (@eventFormID, @registrantID, @responseID);
				COMMIT TRAN;
	
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>

	<cffunction name="completeEvaluationForm" access="public" output="false" returntype="void">
		<cfargument name="eventID" type="numeric" required="yes">
		<cfargument name="registrantID" type="numeric" required="yes">
		<cfargument name="formID" type="numeric" required="yes">
		<cfargument name="responseID" type="numeric" required="yes">
		
		<cfset var qryCompleteEvaluation = "">

		<cfquery name="qryCompleteEvaluation" datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
	
				DECLARE @eventID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.eventID#">,
					@formID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.formID#">,
					@registrantID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.registrantID#">,
					@responseID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.responseID#">,
					@loadPoint varchar(15) = 'evaluation',
					@eventFormID int;
	
				SELECT @eventFormID = eventFormID
				FROM dbo.ev_eventsAndForms
				WHERE eventID = @eventID
				AND formID = @formID
				AND loadPoint = @loadPoint;
	
				UPDATE fbr
				SET fbr.dateCompleted = GETDATE()
				FROM formBuilder.dbo.tblResponses AS fbr
				INNER JOIN dbo.ev_eventsAndFormResponses AS er ON er.responseID = fbr.responseID
				WHERE er.eventFormID = @eventFormID
				AND er.registrantID = @registrantID
				AND er.responseID = @responseID
				AND fbr.isActive = 1
				AND fbr.dateCompleted IS NULL;
	
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>
	</cffunction>
	
	<cffunction name="getEventsCategories" access="public" output="false" returntype="query">
		<cfargument name="eventIDs" type="string" required="yes">

		<cfset var local = structNew()>

		<cfquery name="local.qryEventCategories" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			SELECT c.categoryID, c.category, c.categoryShort, c.calColor, c.visibility, ec.categoryOrder, ec.eventID
			FROM dbo.ev_categories AS c
			INNER JOIN dbo.ev_eventCategories AS ec ON ec.categoryID = c.categoryID
			WHERE ec.eventID IN (<cfqueryparam value="#arguments.eventIDs#" cfsqltype="CF_SQL_INTEGER" list="true">)
			ORDER BY ec.eventID, ec.categoryOrder, c.category;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn local.qryEventCategories>
	</cffunction>
</cfcomponent>